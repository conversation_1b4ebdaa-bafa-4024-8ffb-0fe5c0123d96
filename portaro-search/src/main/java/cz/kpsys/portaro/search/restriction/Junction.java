package cz.kpsys.portaro.search.restriction;

import com.fasterxml.jackson.annotation.JsonValue;
import cz.kpsys.portaro.commons.property.Property;
import cz.kpsys.portaro.search.DefaultGettableAndSettableSearchParams;
import cz.kpsys.portaro.search.restriction.matcher.SearchMatcher;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collector;

public class Junction<FIELD> implements Restriction<FIELD> {

    public static Collector<Restriction<?>, List<Restriction<?>>, Junction<?>> restrictionsToJunctionCollector() {
        return new Collector<>() {
            @Override
            public Supplier<List<Restriction<?>>> supplier() {
                return ArrayList::new;
            }

            @Override
            public BiConsumer<List<Restriction<?>>, Restriction<?>> accumulator() {
                return List::add;
            }

            @Override
            public BinaryOperator<List<Restriction<?>>> combiner() {
                return (restrictions, restrictions2) -> {
                    restrictions.addAll(restrictions2);
                    return restrictions;
                };
            }

            @Override
            public Function<List<Restriction<?>>, Junction<?>> finisher() {
                return Junction::new;
            }

            @Override
            public Set<Collector.Characteristics> characteristics() {
                return Collections.singleton(Characteristics.UNORDERED);
            }
        };
    }

    protected List<Restriction<? extends FIELD>> items;

    public Junction() {
        this(new ArrayList<>());
    }

    public Junction(List<? extends Restriction<? extends FIELD>> items) {
        this.items = new ArrayList<>(items);
    }

    public Junction<FIELD> add(Restriction<? extends FIELD> item) {
        items.add(item);
        return this;
    }

    public Junction<FIELD> addAll(Collection<Restriction<? extends FIELD>> item) {
        items.addAll(item);
        return this;
    }

    public Junction<FIELD> addIfNotNull(Restriction<FIELD> restriction) {
        return addIf(restriction != null, restriction);
    }

    public Junction<FIELD> addIf(boolean condition, Restriction<FIELD> restriction) {
        if (condition) {
            add(restriction);
        }
        return this;
    }

    public Junction<FIELD> addIf(boolean condition, Supplier<Restriction<FIELD>> restriction) {
        if (condition) {
            add(restriction.get());
        }
        return this;
    }

    public <VAL> Junction<FIELD> addIfNotNull(Supplier<@Nullable VAL> getter, Function<@NonNull VAL, Restriction<FIELD>> restriction) {
        VAL val = getter.get();
        if (val != null) {
            add(restriction.apply(val));
        }
        return this;
    }

    public <VAL> Junction<FIELD> addIfHas(DefaultGettableAndSettableSearchParams p, Property<VAL> searchParam, Function<VAL, Restriction<FIELD>> restriction) {
        if (p.has(searchParam)) {
            add(restriction.apply(p.get(searchParam)));
        }
        return this;
    }

    public Junction<FIELD> addTerm(FIELD field, SearchMatcher matcher) {
        return addIf(true, new Term<>(field, matcher));
    }

    public boolean isEmpty() {
        return items.isEmpty();
    }

    @JsonValue //to Junction be unwrapped
    public List<Restriction<? extends FIELD>> getItems() {
        return items;
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + " " + items;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Junction<?> junction = (Junction<?>) o;
        return Objects.equals(getItems(), junction.getItems());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getItems());
    }
}
