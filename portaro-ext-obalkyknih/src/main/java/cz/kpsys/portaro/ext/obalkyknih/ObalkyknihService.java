package cz.kpsys.portaro.ext.obalkyknih;

import cz.kpsys.portaro.auth.ThisThreadAuthenticatingDynamicRunner;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.CacheDeletableById;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.io.ExternalServiceException;
import cz.kpsys.portaro.commons.io.ExternalServiceResponseException;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdDeleter;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.ext.obalkyknih.db.Obalkyknih;
import cz.kpsys.portaro.ext.obalkyknih.metadata.*;
import cz.kpsys.portaro.file.FileSearchParams;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.filecategory.FileCategoryBySystemTypeLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.record.file.cover.CoverSearchException;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import cz.kpsys.portaro.setting.FondedValues;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.file.filecategory.FileCategorySystemType.COVER;
import static cz.kpsys.portaro.file.filecategory.FileCategorySystemType.TOC;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ObalkyknihService {

    public static final String SERVICE_NAME = "obalkyknih";
    public static final String SERVICE_COVER = SERVICE_NAME + ".cover";
    public static final String SERVICE_TOC = SERVICE_NAME + ".toc";
    public static final String SERVICE_TOC_THUMBNAIL = SERVICE_NAME + ".toc_thumbnail";
    public static final String SERVICE_TOC_REFERENCE = SERVICE_NAME + ".toc_reference";

    @NonNull Provider<@NonNull Boolean> serviceEnabledProvider;
    @NonNull Provider<@NonNull Boolean> applicationServerServiceEnabledProvider;
    @NonNull Provider<@NonNull Boolean> downloadTocScanEnabledProvider;
    @NonNull Provider<@NonNull Boolean> downloadTocScanPreviewEnabledProvider;
    @NonNull Provider<@NonNull Boolean> downloadReferencesScanEnabledProvider;
    @NonNull Provider<@NonNull Boolean> obalkyknihUpStatusProvider;
    @NonNull Provider<@NonNull Department> rootDepartmentProvider;
    @NonNull ContextualProvider<Department, @NonNull FondedValues<Boolean>> fondedObalkyknihEnabled;
    @NonNull ByIdLoadable<? extends Record, UUID> recordLoader;
    @NonNull ObalkyknihDocumentMetadataLoader obalkyknihDocumentMetadataLoader;
    @NonNull ObalkyknihAuthorityMetadataLoader obalkyknihAuthorityMetadataLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, IdentifiedFile> fileSearchLoader;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull Saver<Obalkyknih, ?> obalkyknihSaver;
    @NonNull ByIdDeleter<UUID> obalkyknihByIdDeleter;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull LinkFileUpdater linkFileUpdater;
    @NonNull CacheDeletableById cacheDeletableByRecordId;
    @NonNull FileCategoryBySystemTypeLoader fileCategoryBySystemTypeLoader;
    @NonNull Saver<RecordWithAttachmentSaveCommand, IdentifiedFile> recordWithAttachmentSaver;
    @NonNull ThisThreadAuthenticatingDynamicRunner obalkyknihAuthRunner;

    @NonFinal List<Obalkyknih> stack;

    public void run(@NonNull NextObalkyknihItemSupplier obalkyknihItemSupplier,
                    @NonNull Provider<Boolean> schedulerIsShuttingDownProvider) {
        var ctx = rootDepartmentProvider.get();
        obalkyknihAuthRunner.accept(currentAuth ->
                run(obalkyknihItemSupplier, schedulerIsShuttingDownProvider, currentAuth, ctx), ctx);
    }

    @SneakyThrows
    private void run(@NonNull NextObalkyknihItemSupplier obalkyknihItemSupplier,
                     @NonNull Provider<Boolean> schedulerIsShuttingDownProvider,
                     @NonNull UserAuthentication currentAuth,
                     @NonNull Department ctx) {

        if (stack == null) {
            stack = new ArrayList<>(obalkyknihItemSupplier.getRecommendedStackSize());
        }

        do {
            if (!serviceEnabledProvider.get()) {
                return;
            }
            if (applicationServerServiceEnabledProvider.get()) {
                log.warn("Service obalkyknih is activated in AS. I will not run!");
                return;
            }

            if (!obalkyknihUpStatusProvider.get()) {
                log.warn("Service obalkyknih is not available now! Waiting for next cycle..");
                return;
            }

            var loadedStack = loadStack(obalkyknihItemSupplier);
            if (loadedStack.isEmpty()) {
                return; // Wait for next data
            }

            TimeMeter tm = null;
            int lastBatchSize = 0;
            if (log.isDebugEnabled()) {
                tm = TimeMeter.start();
                lastBatchSize = loadedStack.size();
            }

            while (!loadedStack.isEmpty()) {
                if (schedulerIsShuttingDownProvider.get()) {
                    log.info("Shutting down ObalkyknihService task!");
                    return;
                }

                final Obalkyknih processed = loadedStack.getLast();
                transactionalProcess(processed, currentAuth, ctx);
                loadedStack.removeLast(); // Je rychlejší než ArrayList#removeFirst()
                Thread.sleep(1000);
            }

            if (tm != null) {
                log.debug("Obalkyknih queue of size {} processed in: {}", lastBatchSize, tm.elapsedTimeString());
            }
        } while (true); // This task runs as long as there are covers to process
    }

    public void process(Obalkyknih obalkyknih) {
        var ctx = rootDepartmentProvider.get();
        obalkyknihAuthRunner.accept(currentAuth -> transactionalProcess(obalkyknih, currentAuth, ctx), ctx);
    }

    private void transactionalProcess(Obalkyknih obalkyknih, UserAuthentication currentAuth, Department ctx) {
        transactionTemplate.executeWithoutResult(_ -> processImpl(obalkyknih, currentAuth, ctx));
    }

    @SneakyThrows
    private void processImpl(Obalkyknih obalkyknih, UserAuthentication currentAuth, Department ctx) {
        Record record = recordLoader.getById(obalkyknih.recordId());
        log.trace("Processing record {} with state {}", record, obalkyknih.state());
        if (record.isDeleted()) {
            log.info("Removing metadata scheduling of {}", record);
            cacheDeletableByRecordId.deleteFromCacheById(record.getId());
            obalkyknihByIdDeleter.deleteById(obalkyknih.id());
            return;
        }

        if (isRecordSkipped(record)) {
            obalkyknihSaver.save(obalkyknih
                    .withState(Obalkyknih.State.SKIPPED)
                    .withLastSearchDate(Instant.now()));
            return;
        }

        try {
            Obalkyknih result = processRecord(obalkyknih, record, currentAuth, ctx);
            obalkyknihSaver.save(result
                    .withState(Obalkyknih.State.OK)
                    .withLastSearchDate(Instant.now()));
        } catch (RecordIsNotSearchableException e) {
            log.debug("{}", e.getMessage());
            obalkyknihSaver.save(obalkyknih
                    .withState(Obalkyknih.State.NO_METADATA)
                    .withLastSearchDate(Instant.now()));
        } catch (NoDataException e) {
            log.debug("No metadata loaded from obalkyknih.cz! {}", e.getMessage());
            obalkyknihSaver.save(obalkyknih
                    .withState(Obalkyknih.State.OK)
                    .withLastSearchDate(Instant.now()));
        } catch (ExternalServiceResponseException e) {
            log.debug("Problem when fetching metadata from obalkyknih.cz! {}; {}", e.getMessage(), e.getCause().getMessage());
            obalkyknihSaver.save(obalkyknih
                    .withState(Obalkyknih.State.ERROR)
                    .withLastSearchDate(Instant.now()));
        } catch (CoverSearchException e) {
            if (e.getReason().equals(CoverSearchException.CONNECTION_PROBLEM) || e.getReason().equals(CoverSearchException.DOCASNE_ZABLOKOVANO)) {
                throw e;
            }
            obalkyknihSaver.save(obalkyknih
                    .withState(Obalkyknih.State.ERROR)
                    .withLastSearchDate(Instant.now()));
        } finally {
            cacheDeletableByRecordId.deleteFromCacheById(record.getId());
        }
    }

    private Obalkyknih processRecord(Obalkyknih obalkyknih, Record record, UserAuthentication currentAuth, Department ctx) throws ExternalServiceException {
        if (FondTypeResolver.isDocumentFond(record)) {
            DocumentMetadata metadata = obalkyknihDocumentMetadataLoader.download(record);
            Obalkyknih updatedObalkyknih = obalkyknih.updateWithMetadata(metadata);
            updatedObalkyknih = updatePrimaryCoverLink(updatedObalkyknih, record, currentAuth, ctx);
            updateTocLinkFiles(metadata, record, currentAuth, ctx);
            return updatedObalkyknih;
        }

        if (FondTypeResolver.isAuthorityFond(record)) {
            AuthorityMetadata metadata = obalkyknihAuthorityMetadataLoader.download(record);
            Obalkyknih updatedObalkyknih = obalkyknih.updateWithMetadata(metadata);
            updatedObalkyknih = updatePrimaryCoverLink(updatedObalkyknih, record, currentAuth, ctx);
            return updatedObalkyknih;
        }

        throw new IllegalArgumentException(String.format("Unknown fond for %s!", record));
    }

    private Obalkyknih updatePrimaryCoverLink(Obalkyknih obalkyknih, Record record,
                                              UserAuthentication currentAuth, Department ctx) {
        var coverFileDownloadCommand = new FileDownloadCommand(obalkyknih.coverUrl(), fileCategoryBySystemTypeLoader.singleOrThrow(COVER), "obalka");
        var coverFile = linkFileUpdater.processCoverUrl(coverFileDownloadCommand, record, Provider.of(() ->
                ObjectUtil.elvis(record.getCover(), coverReference -> identifiedFileLoader.getById(coverReference.getId()))
        ));

        if (coverFile != null) {
            coverFile.setService(SERVICE_COVER);
            var savedCoverFile = recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofCover(record, coverFile, currentAuth, ctx));
            // TODO: uložit odkaz na náhled? V aplikáči je zápis null hodnoty: cz.kpsys.db.StahovacObalek#aktualizujFulltextObalkyKnih. Náhled ale vygeneruje i Portaro, ne?
            // TODO: z metadat se načítá jiná obálka než skrz stávající CoverLoader. Která by měla být?
            return obalkyknih.withCoverId(savedCoverFile.getId());
        }
        return obalkyknih;
    }

    private void updateTocLinkFiles(DocumentMetadata metadata, Record record,
                                    UserAuthentication currentAuth, Department ctx) {
        var allRecordTocFilesProvider = Provider.<List<IdentifiedFile>>of(() -> {
            if (record.getDirectoryId() == null) {
                return List.of();
            }
            return fileSearchLoader.getContent(RangePaging.forAll(), StaticParamsModifier.of(
                    FileSearchParams.DIRECTORY, List.of(record.getDirectoryId()),
                    FileSearchParams.FILE_CATEGORY, List.of(fileCategoryBySystemTypeLoader.singleOrThrow(TOC).getId())));
        }).cached();

        if (downloadTocScanEnabledProvider.get()) {
            var tocFileDownloadCommand = new FileDownloadCommand(metadata.tocPdfUrl(), fileCategoryBySystemTypeLoader.singleOrThrow(TOC), "Obsah z obalkyknih.cz");
            var toc = linkFileUpdater.processTocUrl(tocFileDownloadCommand, TocFileTypes.TOC_PDF, fileTypeFinder(TocFileTypes.TOC_PDF, allRecordTocFilesProvider));
            if (toc != null) {
                toc.setService(SERVICE_TOC);
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofFile(record, toc, currentAuth, ctx));
            }
        }

        if (downloadTocScanPreviewEnabledProvider.get()) {
            var tocThumbnailFileDownloadCommand = new FileDownloadCommand(metadata.tocThumbnailUrl(), fileCategoryBySystemTypeLoader.singleOrThrow(TOC), "Náhled obsahu z obalkyknih.cz");
            var tocThumbnail = linkFileUpdater.processTocUrl(tocThumbnailFileDownloadCommand, TocFileTypes.TOC_THUMBNAIL, fileTypeFinder(TocFileTypes.TOC_THUMBNAIL, allRecordTocFilesProvider));
            if (tocThumbnail != null) {
                tocThumbnail.setService(SERVICE_TOC_THUMBNAIL);
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofFile(record, tocThumbnail, currentAuth, ctx));
            }
        }

        if (downloadReferencesScanEnabledProvider.get()) {
            var tocReferenceFileDownloadCommand = new FileDownloadCommand(metadata.bibPdfUrl(), fileCategoryBySystemTypeLoader.singleOrThrow(TOC), "Seznam použité literatury z obalkyknih.cz");
            var tocReference = linkFileUpdater.processTocUrl(tocReferenceFileDownloadCommand, TocFileTypes.TOC_REFERENCE, fileTypeFinder(TocFileTypes.TOC_REFERENCE, allRecordTocFilesProvider));
            if (tocReference != null) {
                tocReference.setService(SERVICE_TOC_REFERENCE);
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofFile(record, tocReference, currentAuth, ctx));
            }
        }
    }

    private Provider<IdentifiedFile> fileTypeFinder(TocFileTypes searchedType,
                                                    Provider<List<IdentifiedFile>> filesProvider) {
        return filesProvider.andThenFastReturningNull(files ->
                files.stream()
                        .filter(f -> searchedType.getFileName().equals(f.getFilename()))
                        .findAny()
                        .orElse(null)
        );
    }

    private List<Obalkyknih> loadStack(NextObalkyknihItemSupplier obalkyknihItemSupplier) {
        if (stack.isEmpty()) {
            transactionTemplate.executeWithoutResult(_ -> obalkyknihItemSupplier.fillStack(stack));
        }
        return stack;
    }

    private boolean obalkyknihEnabledForFond(Fond fond) {
        return fondedObalkyknihEnabled.getOn(rootDepartmentProvider.get()).getFor(fond.getId()).orElseThrow();
    }

    private boolean isRecordSkipped(Record record) {
        return !record.isActive() // isDraft
                || !obalkyknihEnabledForFond(record.getFond());
    }

}
