dependencies {
    testCompileOnly("org.projectlombok:lombok:+")
    testAnnotationProcessor("org.projectlombok:lombok:+")

    testImplementation("org.junit.jupiter:junit-jupiter:+")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    compileOnly("org.projectlombok:lombok:+")
    annotationProcessor("org.projectlombok:lombok:+")

    api(project(":portaro-record"))
    api(project(":portaro-search"))
    api(project(":portaro-sql-generator"))
    api(project(":portaro-user"))
    implementation(project(":portaro-catalog"))
    implementation(project(":portaro-database-structure"))

    implementation("org.springframework:spring-context:6.+")
    implementation("org.springframework:spring-core:6.+")
    implementation("org.springframework.boot:spring-boot:3.+")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa:3.+")

    implementation("org.slf4j:slf4j-api:+")
    implementation("jakarta.platform:jakarta.jakartaee-api:10.+")
    implementation("jakarta.validation:jakarta.validation-api:3.+")
}
