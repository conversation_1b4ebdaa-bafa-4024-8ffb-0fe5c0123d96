package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.value.DetailedRecordValueCommand;
import cz.kpsys.portaro.record.detail.value.FieldValueCommand;
import cz.kpsys.portaro.record.detail.value.LocalDateValueCommand;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class DefaultFieldValueGenerator {

    @NonNull ByIdLoadable<Record, UUID> recordLoader;
    @NonNull RecordDayIdLoader recordDayIdLoader;
    @NonNull UserByBasicUserLoader userByBasicUserLoader;


    public boolean isGenerated(String defaultValue) {
        return defaultValue.startsWith("$(") && defaultValue.endsWith(")");
    }

    public Optional<? extends FieldValueCommand> generate(String generator, Department ctx, UserAuthentication currentAuth) {
        return switch (generator) {
            case "$(current_day)" -> getCurrentDay();
            case "$(current_fonded_day)" -> getCurrentFondedDay(ctx, currentAuth);
            case "$(active_user)" -> getActiveUser(ctx, currentAuth);
            default -> throw new IllegalArgumentException("Unknown generator " + generator);
        };
    }

    private Optional<LocalDateValueCommand> getCurrentDay() {
        return Optional.of(new LocalDateValueCommand(LocalDate.now()));
    }

    private Optional<DetailedRecordValueCommand> getCurrentFondedDay(Department ctx, UserAuthentication currentAuth) {
        var today = LocalDate.now();
        var loaded = recordDayIdLoader.load(List.of(today));
        if (loaded.isEmpty()) {
            log.warn("Missing record day for '{}' when filling default value!", today);
            return Optional.empty();
        }
        var todayRecord = recordLoader.getById(loaded.getFirst());

        return Optional.of(new DetailedRecordValueCommand(todayRecord, ctx, currentAuth));
    }

    private Optional<FieldValueCommand> getActiveUser(Department ctx, UserAuthentication currentAuth) {
        var userRecordId = userByBasicUserLoader.getUser(currentAuth.getActiveUser()).getRecordId();
        if (userRecordId == null) {
            log.warn("Missing user record for '{}' when filling default value!", currentAuth.getActiveUser());
            return Optional.empty();
        }
        var creatorUserRecord = recordLoader.getById(userRecordId);

        return Optional.of(new DetailedRecordValueCommand(creatorUserRecord, ctx, currentAuth));
    }
}
