package cz.kpsys.portaro.record.detail.data;

import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import static cz.kpsys.portaro.databasestructure.RecordDb.FDEF_ROOT.*;

@MappedSuperclass
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@Getter
public class FieldTypeEntity extends BasicIdentified<Long> {

    @Column(name = ID)
    String fieldTypeId;

    @Column(name = NAZEV)
    String name;

    @Column(name = PIC)
    String pic;

    @Column(name = EXPORT_CIS_POLE)
    Integer exportFieldNumber;

    @Column(name = EXPORT_KOD_PODPOLE)
    String exportSubfieldCode;

    @Column(name = NENIOPAK)
    Boolean notRepeatable;

    @Column(name = AUTTYP)
    @NonNull
    Integer authorityType;

    @Column(name = FONDEXT)
    Integer linkedRecordFond;

    @Column(name = PODPEXT)
    String linkedRecordEntryFieldSubfieldCode;

    @Column(name = PRENASET)
    Integer transferTypeId;

    @Column(name = FK_FRAZESKUP)
    String phraseGroupId;

    @Override
    public boolean equals(Object obj) {
        return ObjectUtil.equals(this, obj, FieldTypeEntity.class, FieldTypeEntity::getFieldTypeId);
    }
}
