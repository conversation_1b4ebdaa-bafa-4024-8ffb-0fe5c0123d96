package cz.kpsys.portaro.record.detail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import cz.kpsys.portaro.commons.util.*;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.*;

@JsonSerialize(using = FieldTypeIdJsonSerializer.class)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@EqualsAndHashCode
public class FieldTypeId implements Serializable, WithCode {

    public static final int LEVEL_RECORD = 0;
    public static final int LEVEL_TOPFIELD = 1;
    public static final int LEVEL_SUBFIELD = 2;
    public static final int LEVEL_SUBSUBFIELD = 3;
    public static final int LEVEL_HIGHEST = LEVEL_TOPFIELD;

    public static final char NEW_AUTHORITY_NUMBER_PREFIX_CHAR = 'a';
    public static final char NEW_DOCUMENT_NUMBER_PREFIX_CHAR = 'd';
    public static final String NEW_AUTHORITY_NUMBER_PREFIX = String.valueOf(NEW_AUTHORITY_NUMBER_PREFIX_CHAR);
    public static final String NEW_DOCUMENT_NUMBER_PREFIX = String.valueOf(NEW_DOCUMENT_NUMBER_PREFIX_CHAR);
    public static final String AUTHORITY_TOPFIELD_CODE_FORMAT_REGEX = RegExpUtils.startEnd(NEW_AUTHORITY_NUMBER_PREFIX + RegExpUtils.atLeastOne(RegExpUtils.number()));
    public static final char DELIMITER_CHAR = '.';
    public static final String DELIMITER = String.valueOf(DELIMITER_CHAR);

    public static final String OLD_AUTHORITY_CODE_WITH_DOT = "A.";
    public static final String OLD_DOCUMENT_CODE_WITH_DOT = "D.";

    public static Comparator<FieldTypeId> NUMERICALLY_COMPATIBLE_SORTER = new SameLevelFieldTypeIdComparator(
            new PrioritizingComparator<>(List.of(FieldTypes.AUTHORITY_LEADER_FIELD_CODE, FieldTypes.DOCUMENT_LEADER_FIELD_CODE, FieldTypes.IND_1_FIELD_CODE, FieldTypes.IND_2_FIELD_CODE))
                    .thenComparing(new PrefixedNumberAwareStringComparator(List.of(NEW_AUTHORITY_NUMBER_PREFIX_CHAR, NEW_DOCUMENT_NUMBER_PREFIX_CHAR)))
                    .thenComparing(Comparator.naturalOrder())
    );

    @Nullable FieldTypeId parent;
    @NonNull String code;

    protected FieldTypeId(@Nullable FieldTypeId parent, @NonNull String code) {
        this.parent = parent;
        this.code = code;
    }

    public static FieldTypeId top(String code) {
        return new FieldTypeId(null, code);
    }

    public static FieldTypeId recordField(boolean inAuthorityRecord, String number) {
        return top((inAuthorityRecord ? NEW_AUTHORITY_NUMBER_PREFIX : NEW_DOCUMENT_NUMBER_PREFIX) + number);
    }

    public static FieldTypeId docField(String number) {
        return recordField(false, number);
    }

    public static FieldTypeId subfield(FieldTypeId parentFieldTypeId, String code) {
        Assert.hasLength(code, () -> "Illegal subfield code \"%s\" of field %s".formatted(code, parentFieldTypeId));
        return new FieldTypeId(parentFieldTypeId, code);
    }

    public static FieldTypeId subfield(FieldTypeId recordTypeId, String number, String code) {
        Assert.hasLength(code, () -> "Illegal subfield code \"%s\" of field %s".formatted(code, number));
        return recordTypeId.sub(number).sub(code);
    }


    /**
     * Parsuje id z formatu "d245" nebo "a100" nebo "d245.a" nebo "a100.c". Podporuje i stary format "D.245" nebo "A.100" nebo "D.245.a" nebo "A.100.c"
     */
    public static @NonNull FieldTypeId parse(@NonNull @NotBlank String id) {
        if (id.isBlank()) {
            throw new IllegalArgumentException(String.format("FieldTypeId \"%s\" cannot be blank", id));
        }
        if (id.contains("#")) {
            throw new IllegalArgumentException(String.format("FieldTypeId \"%s\" cannot contain \"#\" character (it is char for FieldId)", id));
        }
        if (id.contains(" ")) {
            throw new IllegalArgumentException(String.format("FieldTypeId \"%s\" cannot contain \" \" character (space)", id));
        }

        boolean supportOldPrefixFormat = true;
        if (supportOldPrefixFormat) {
            Optional<String> authorityWithoutPrefix = StringUtil.removePrefixOpt(id, OLD_AUTHORITY_CODE_WITH_DOT);
            if (authorityWithoutPrefix.isPresent()) {
                id = NEW_AUTHORITY_NUMBER_PREFIX + authorityWithoutPrefix.get();
            }
            Optional<String> documentWithoutPrefix = StringUtil.removePrefixOpt(id, OLD_DOCUMENT_CODE_WITH_DOT);
            if (documentWithoutPrefix.isPresent()) {
                id = NEW_DOCUMENT_NUMBER_PREFIX + documentWithoutPrefix.get();
            }
        }

        var codes = id.split(RegExpUtils.escape(DELIMITER));
        Assert.notEmpty(codes, "FieldTypeId \"" + id + "\" is invalid");
        FieldTypeId result = null;
        for (String code : codes) {
            if (result == null) {
                result = top(code);
            } else {
                result = result.sub(code);
            }
        }
        return Objects.requireNonNull(result);
    }

    public static FieldTypeId parseSubfield(String id) {
        FieldTypeId parsed = parse(id);
        Assert.state(parsed.getLevel() == LEVEL_SUBFIELD, () -> "Given field type id %s is not for subfield (level is %s)".formatted(id, parsed.getLevel()));
        return parsed;
    }

    @Deprecated
    @JsonIgnore
    public boolean isInAuthority() {
        String code = this.code;
        if (parent != null) {
            code = parent.getCode();
            if (parent.parent != null) {
                code = parent.parent.getCode();
                if (parent.parent.parent != null) {
                    code = parent.parent.parent.getCode();
                }
            }
        }
        return code.matches(AUTHORITY_TOPFIELD_CODE_FORMAT_REGEX);
    }

    @Nullable
    @JsonIgnore
    public FieldTypeId getParent() {
        return parent;
    }

    @JsonIgnore
    @NonNull
    public FieldTypeId existingParent() {
        return Objects.requireNonNull(parent, "This field type id is root, cannot get a parent");
    }

    @JsonIgnore
    public boolean hasParent() {
        return parent != null;
    }

    @JsonIgnore
    public boolean hasSameParentAs(FieldTypeId other) {
        return hasParent() && other.hasParent() && existingParent().equals(other.existingParent());
    }

    @JsonIgnore
    public boolean isRoot() {
        return !hasParent();
    }

    @JsonIgnore
    public boolean isParentOf(@NonNull FieldTypeId other) {
        if (other.isRoot()) {
            return false;
        }
        if (other.existingParent().equals(this)) {
            return true;
        }
        return isParentOf(other.existingParent());
    }

    @JsonIgnore
    public FieldTypeId getParentOfLevel(int desiredLevel) {
        Assert.state(desiredLevel >= LEVEL_HIGHEST, () -> "Cannot access getParentOfLevel with desired level < %s".formatted(LEVEL_HIGHEST));

        int currentLevel = getLevel();

        if (currentLevel == desiredLevel) {
            return this;
        }

        Assert.state(hasParent(), () -> "Desired level is > %s but this field id is root (has not a parent)".formatted(LEVEL_HIGHEST));

        return existingParent().getParentOfLevel(desiredLevel);
    }

    @JsonIgnore
    public FieldTypeId getOfLevel(int desiredLevel) {
        if (desiredLevel == getLevel()) {
            return this;
        }
        return getParentOfLevel(desiredLevel);
    }

    @JsonIgnore
    public List<FieldTypeId> getBottomUpParentChain() {
        List<FieldTypeId> parentChain = new ArrayList<>();
        var curr = parent;
        while (curr != null) {
            parentChain.add(curr);
            curr = curr.parent;
        }
        return parentChain;
    }

    @JsonIgnore
    public List<FieldTypeId> getTopDownParentChain() {
        return ListUtil.toReversed(getBottomUpParentChain());
    }

    @JsonIgnore
    public List<FieldTypeId> asSegments() {
        return ListUtil.createNewListAppending(getTopDownParentChain(), this);
    }

    @Override
    public @NonNull String getCode() {
        return code;
    }

    @JsonIgnore
    public int getLevel() {
        if (parent == null) {
            return LEVEL_TOPFIELD;
        }
        if (parent.parent == null) {
            return LEVEL_SUBFIELD;
        }
        if (parent.parent.parent == null) {
            return LEVEL_SUBSUBFIELD;
        }
        throw new IllegalStateException("Max 4 nesting levels of fields are supported");
    }

    public FieldTypeId sub(@NonNull String code) {
        return subfield(this, code);
    }

    public FieldTypeId withCode(@NonNull String code) {
        return new FieldTypeId(parent, code);
    }

    @Override
    public String toString() {
        return value();
    }

    @JsonProperty("value")
    public String value() {
        if (parent == null) {
            return code;
        }
        return delim(parent.value(), code);
    }

    @JsonIgnore
    public String dbAliasCompatibleValue() {
        return value().replace(".", "_");
    }

    /**
     * Deprecated - ale pouziva se (a asi dlouho bude) v AS
     */
    @Deprecated
    @JsonIgnore
    public String withoutADPrefix() {
        return StringUtil.removePrefixOpt(value(), NEW_DOCUMENT_NUMBER_PREFIX)
                .or(() -> StringUtil.removePrefixOpt(value(), NEW_AUTHORITY_NUMBER_PREFIX))
                .orElse(value());
    }

    /**
     * Deprecated - ale pouziva se (a asi dlouho bude) v AS
     */
    @Deprecated
    @JsonIgnore
    public Short codeNumberOrThrow() {
        return Short.parseShort(withoutADPrefix());
    }

    public static String delim(@NonNull String parent, @NonNull String child) {
        return parent + DELIMITER + child;
    }

    @JsonIgnore
    public FieldId toFieldIdWithAllFirstIndices() {
        if (isRoot()) {
            return FieldId.top(code, FieldId.FIRST_FIELD_REPETITION);
        }
        return existingParent().toFieldIdWithAllFirstIndices().sub(code, FieldId.FIRST_FIELD_REPETITION);
    }
}
