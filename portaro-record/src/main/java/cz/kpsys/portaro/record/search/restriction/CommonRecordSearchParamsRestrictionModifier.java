package cz.kpsys.portaro.record.search.restriction;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.detail.constraints.FieldTypeConstraintsResolver;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.CoreSearchParams;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.field.StaticSearchFields;
import cz.kpsys.portaro.search.restriction.Conjunction;
import cz.kpsys.portaro.search.restriction.Not;
import cz.kpsys.portaro.search.restriction.Term;
import cz.kpsys.portaro.search.restriction.matcher.Eq;
import cz.kpsys.portaro.search.restriction.matcher.EqWords;
import cz.kpsys.portaro.search.restriction.matcher.In;
import cz.kpsys.portaro.search.restriction.matcher.StartsWithWords;
import cz.kpsys.portaro.search.restriction.modifier.RestrictionModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.util.List;
import java.util.function.Function;

import static cz.kpsys.portaro.record.RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS;
import static cz.kpsys.portaro.search.field.StaticSearchFields.RECORD_STATUS;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class CommonRecordSearchParamsRestrictionModifier implements RestrictionModifier<MapBackedParams> {

    @NonNull FieldTypeConstraintsResolver fieldTypeConstraintsResolver;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;

    @Override
    public Conjunction<SearchField> modify(Conjunction<SearchField> conjunction, MapBackedParams p, Department ctx) {
        Assert.state(!p.has(CoreSearchParams.SUBKIND), () -> CoreSearchParams.SUBKIND + " parameter must be already expanded to fonds before conjunction modifier");
        Assert.state(!p.has(RecordConstants.SearchParams.ROOT_FOND), () -> RecordConstants.SearchParams.ROOT_FOND + " parameter must be already expanded to fonds before conjunction modifier");

        if (!p.has(CoreSearchParams.FINAL_RAW_QUERY)) {

            //document status
            conjunction.addIf(p.hasLength(FORBIDDEN_RECORD_STATUS), () -> new Not<>(new Term<>(RECORD_STATUS, new In(p.get(FORBIDDEN_RECORD_STATUS)))));

            //related record (napr. hledani dokumentu podle autority)
            conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD_RELATED_RECORD, val -> new Term<>(StaticSearchFields.RECORD_RELATED_RECORD, new Eq(val)));

            //forbidden record ids
            conjunction.addIf(p.hasLength(RecordConstants.SearchParams.FORBIDDEN_RECORD), () -> new Not<>(new Term<>(StaticSearchFields.RECORD_ID, new In(p.get(RecordConstants.SearchParams.FORBIDDEN_RECORD)))));
        }

        conjunction.addIfHas(p, RecordConstants.SearchParams.RECORD, val -> new Term<>(StaticSearchFields.RECORD_ID, new In(val)));

        conjunction.addIfHas(p, CoreSearchParams.NAME, val -> new Term<>(StaticSearchFields.NAME, new EqWords(val)));

        conjunction.addIfHas(p, RecordConstants.SearchParams.PREFIX, val -> new Term<>(StaticSearchFields.NAME, new StartsWithWords(val)));

        if (p.has(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE)) {
            RecordIdFieldTypeId constraintsRecordFieldType = p.get(RecordConstants.SearchParams.CONSTRAINTS_RECORD_FIELD_TYPE);
            FieldTypeConstraintsResolver.FieldTypeConstraints constraints = fieldTypeConstraintsResolver.resolve(constraintsRecordFieldType);

            conjunction.addIfNotNull(constraints::fond, fond -> {
                List<Fond> loadableFonds = enabledLoadableFondsExpander.apply(fond);
                return new Term<>(StaticSearchFields.FOND, new In(loadableFonds));
            });
        }

        return conjunction;
    }
}
