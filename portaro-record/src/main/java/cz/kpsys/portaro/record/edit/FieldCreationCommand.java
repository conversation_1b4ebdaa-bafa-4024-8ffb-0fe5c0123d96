package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import lombok.NonNull;
import lombok.With;
import org.jspecify.annotations.Nullable;

import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@With
public record FieldCreationCommand(

        @Nullable
        RecordFieldId parentRecordFieldId,

        @NonNull
        FieldTypeId fieldTypeId,

        @NonNull
        Optional<Integer> repetition,

        boolean existingReusing,

        boolean missingHierarchyCreating,

        @Nullable
        Department initialValueCtx,

        @Nullable
        UserAuthentication currentAuth,

        @Nullable
        UUID customId

) {

    public static FieldCreationCommand of(@NonNull RecordFieldId recordFieldId) {
        return new FieldCreationCommand(
                recordFieldId.hasParentFieldId() ? recordFieldId.existingParentFieldId() : null,
                recordFieldId.fieldId().getFieldTypeId(),
                Optional.of(recordFieldId.fieldId().getRepetition()),
                false,
                false,
                null,
                null,
                null
        );
    }

    public static FieldCreationCommand ofAppending(@NonNull FieldTypeId fieldTypeId) {
        return new FieldCreationCommand(
                null,
                fieldTypeId,
                Optional.empty(),
                false,
                false,
                null,
                null,
                null
        );
    }

    public static FieldCreationCommand ofAppending(@NonNull RecordFieldId parentRecordFieldId, @NonNull FieldTypeId fieldTypeId) {
        return new FieldCreationCommand(
                parentRecordFieldId,
                fieldTypeId,
                Optional.empty(),
                false,
                false,
                null,
                null,
                null
        );
    }

    public static FieldCreationCommand of(@NonNull RecordIdFondPair record, @NonNull FieldId fieldId) {
        return of(RecordFieldId.of(record, fieldId));
    }

    public FieldCreationCommand reuseExisting() {
        return withExistingReusing(true);
    }

    public FieldCreationCommand notReuseExisting() {
        return withExistingReusing(false);
    }

    public FieldCreationCommand createMissingHierarchy() {
        return withMissingHierarchyCreating(true);
    }

    public FieldCreationCommand notCreateMissingHierarchy() {
        return withMissingHierarchyCreating(false);
    }

    public FieldCreationCommand initialValueCreating(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return withInitialValueCtx(ctx).withCurrentAuth(currentAuth);
    }

    public boolean initialValueCreating() {
        return initialValueCtx != null;
    }

    public @NonNull Department existingInitialValueCtx() {
        return Objects.requireNonNull(initialValueCtx);
    }

    public @NonNull UserAuthentication existingCurrentAuth() {
        return Objects.requireNonNull(currentAuth);
    }

    public FieldCreationCommand settingCustomId(@NonNull UUID customId) {
        return withCustomId(customId);
    }

    public boolean customIdSetting() {
        return customId != null;
    }

    public @NonNull UUID existingCustomId() {
        return Objects.requireNonNull(customId);
    }
}
