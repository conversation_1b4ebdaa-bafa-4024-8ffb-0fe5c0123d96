package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordCatalogizationPhase;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EmptyFieldsComplingRecordEditationWrapper implements AutoSaveableRecordEditation {

    @NonNull RecordEditation target;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull Department initialValueCtx;
    @NonNull UserAuthentication currentAuth;

    public EmptyFieldsComplingRecordEditationWrapper(@NonNull RecordEditation target, @NonNull RecordFieldEditor recordFieldEditor, @NonNull Department initialValueCtx, @NonNull UserAuthentication currentAuth) {
        this.target = target;
        this.recordFieldEditor = recordFieldEditor;
        this.initialValueCtx = initialValueCtx;
        this.currentAuth = currentAuth;
        compleFields();
    }

    @Override
    public String getId() {
        return target.getId();
    }

    @Override
    public boolean isDraft() {
        return target.isDraft();
    }

    @Override
    public boolean isDeleted() {
        return target.isDeleted();
    }

    @Override
    public boolean isRevisionSaved() {
        return target.isRevisionSaved();
    }

    @Override
    public boolean isAuthority() {
        return target.isAuthority();
    }

    @NonNull
    @Override
    public Fond getFond() {
        return target.getFond();
    }

    @NonNull
    @Override
    public Record getRecord() {
        return target.getRecord();
    }

    @NonNull
    @Override
    public List<Field<?>> getFields() {
        return target.getFields();
    }

    @Override
    public @NonNull Stream<Field<?>> streamFields() {
        return target.streamFields();
    }

    @Override
    public @NonNull List<? extends EditableFieldType> getSubfieldTypes() {
        return target.getSubfieldTypes();
    }

    @Override
    public Field<?> createField(@NonNull EmptyFieldCreation creation) {
        Field<?> topfield = target.createField(creation);
        compleFields();
        return topfield;
    }

    @Override
    public RecordEditation replaceAllFields(FieldContainer newFields) {
        target.replaceAllFields(newFields);
        return this;
    }

    @Override
    public RecordEditation moveFields(FieldMovementCommand command) {
        target.moveFields(command);
        return this;
    }

    @Override
    public RecordEditation changeFond(Fond fond) {
        target.changeFond(fond);
        compleFields();
        return this;
    }

    @Override
    public RecordEditation changeCatalogizationPhase(RecordCatalogizationPhase status) {
        target.changeCatalogizationPhase(status);
        return this;
    }

    @Override
    public void copyTopfield(Field<?> targetField, Field<?> sourceTopfield) {
        target.copyTopfield(targetField, sourceTopfield);
    }

    @Override
    public RecordEditation publish(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) throws IllegalStateException {
        target.publish(ctx, currentAuth);
        return this;
    }

    @Override
    public RecordEditation saveIfModified(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        target.saveIfModified(ctx, currentAuth);
        return this;
    }

    @Override
    public void notifyChange(RecordEditationEvent event) {
        target.notifyChange(event);
        compleFields(); //po ulozeni komplex-autoritniho podpole muze byt nutne prizobrazit dalsi svazana podpole
    }

    /**
     * Pridani prazdnych poli a podpoli pro moznost editace a komplex-autoritnich podpoli
     */
    private void compleFields() {
        List<? extends EditableFieldType> editableFieldTypes = getSubfieldTypes();
        for (EditableFieldType editableFieldType : editableFieldTypes) {
            FieldTypeId fieldTypeId = editableFieldType.getFieldTypeId();

            //topfield
            //pokud zpusob zobrazeni je "vzdy" nebo "pouze editace" a to pole v zaznamu neni, pridame nove
            if ((editableFieldType.getDisplayType() == FieldDisplayType.ALWAYS || editableFieldType.getDisplayType() == FieldDisplayType.EDIT_ONLY) && getFields(By.typeId(fieldTypeId)).isEmpty()) {
                FieldCreationCommand cmd = FieldCreationCommand.of(getRecord().idFondPair(), fieldTypeId.toFieldIdWithAllFirstIndices())
                        .reuseExisting()
                        .createMissingHierarchy()
                        .initialValueCreating(initialValueCtx, currentAuth);
                recordFieldEditor.getOrCreateField(target, cmd);
            }

            //subfields
            for (EditableFieldType editableSubfieldType : editableFieldType.getSubfieldTypes()) {

                //pro kazdy typ podpole zobrazene "vzdy" nebo "pouze editace"
                if (editableSubfieldType.getDisplayType() == FieldDisplayType.ALWAYS || editableSubfieldType.getDisplayType() == FieldDisplayType.EDIT_ONLY) {
                    //pokud dany typ podpole v poli chybi, doplnime prazdnym
                    streamFields(By.typeId(fieldTypeId))
                            .filter(parentField -> parentField.getFields(By.typeId(editableSubfieldType.getFieldTypeId())).isEmpty())
                            .forEach(parentField -> {
                                FieldCreationCommand cmd = FieldCreationCommand.of(parentField.getRecordFieldId().toSubfieldId(editableSubfieldType.getFieldTypeId(), FieldId.FIRST_FIELD_REPETITION))
                                        .reuseExisting()
                                        .createMissingHierarchy()
                                        .initialValueCreating(initialValueCtx, currentAuth);
                                recordFieldEditor.getOrCreateField(target, cmd);
                            });
                }

            }
        }
    }
}
