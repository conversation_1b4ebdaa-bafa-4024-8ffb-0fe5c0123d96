package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldId;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordMultifieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpecSet;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(access = AccessLevel.PRIVATE)
public class Multifield implements ValuableFieldNode, Iterable<FieldSlot> {

    @NonNull RecordIdFondPair record;
    @NonNull RecordMultifieldId id;
    @NonNull EditableFieldType fieldType;
    @NonNull Map<Integer, FieldSlot> fieldSlots = new HashMap<>();
    @Nullable @NonFinal FailedResult generatedError = null;
    @Nullable @NonFinal RecordIdFondPair generatedRecordLink = null;

    public static Multifield createEmpty(@NonNull RecordIdFondPair record, @NonNull RecordMultifieldId id, @NonNull EditableFieldType fieldType) {
        Multifield multifield = new Multifield(record, id, fieldType);
        if (multifield.isBlankFieldGenerationCapable()) {
            multifield.setGeneratedNoValueField();
        }
        return multifield;
    }

    public void addOrMerge(@NonNull List<Field<?>> fields) {
        for (Field<?> field : fields) {
            addOrMerge(field);
        }
    }

    public void addOrMerge(@NonNull Field<?> field) {
        Assert.state(field.getType().equals(fieldType), () -> "Cannot add (to multifield) field of other type: field " + field + " is not of " + fieldType);

        FieldSlot fieldSlot = fieldSlots.get(field.getRepetition());
        if (fieldSlot != null) {
            fieldSlot.setField(field);
        } else {
            fieldSlot = FieldSlot.createFilledAndCreateGeneratedMultifields(record, field.getRecordFieldId(), fieldType, field);
            fieldSlots.put(field.getRepetition(), fieldSlot);
        }
    }

    public RecordSpecSet updateLoadedAndGetNextSpecs(@NonNull LoadedRecords loadedRecords, @NonNull RecordSpecSet searchedRecordSpecs) {
        FormulaResult formulaResult = loadedRecords.resolve(searchedRecordSpecs, this);
        RecordSpecSet missingSpecs = formulaResult instanceof AbsentDataResult absentDataResult ? absentDataResult.toSpec() : RecordSpecSet.ofEmptyModifiable();
        RecordSpecSet unsearchedSpecs = missingSpecs.minus(searchedRecordSpecs);

        RecordSpecSet result = RecordSpecSet.ofEmptyModifiable();
        result.addAll(unsearchedSpecs);
        for (FieldSlot fieldSlot : this) {
            result.addAll(fieldSlot.updateLoadedAndGetNextSpecs(loadedRecords, searchedRecordSpecs));
        }
        return result;
    }

    @Override
    public @NonNull RecordIdFondPair record() {
        return record;
    }

    public @NonNull RecordMultifieldId id() {
        return id;
    }

    public @NonNull EditableFieldType fieldType() {
        return fieldType;
    }

    public List<Multifield> findMultifieldsByFieldTypeId(@NonNull FieldTypeId fieldTypeId) {
        if (fieldTypeId.equals(id.fieldTypeId())) {
            throw new IllegalStateException("Cannot call findSingleByFieldTypeId for same field type as own: " + fieldTypeId);
        }
        if (fieldTypeId.getLevel() == id.nestingLevel()) {
            throw new IllegalStateException("Cannot call findSingleByFieldTypeId with same nesting level as own: level of " + fieldTypeId + " < level of " + id.fieldTypeId());
        }
        if (fieldTypeId.getLevel() < id.nestingLevel()) {
            throw new IllegalStateException("Cannot call findSingleByFieldTypeId with nesting level lower than own: level of " + fieldTypeId + " < level of " + id.fieldTypeId());
        }
        return stream()
                .flatMap(fieldSlot -> fieldSlot.findMultifieldsByFieldTypeId(fieldTypeId).stream())
                .toList();
    }

    public Optional<FieldSlot> findSingleByFieldId(@NonNull RecordFieldId recordFieldId) {
        Optional<FieldSlot> topSlot = findTopSlotFor(recordFieldId);
        if (topSlot.isEmpty()) {
            return Optional.empty();
        }
        if (topSlot.get().id().equals(recordFieldId)) {
            return topSlot;
        }
        return topSlot.get().findSingleByFieldId(recordFieldId);
    }

    private Optional<FieldSlot> findTopSlotFor(@NonNull RecordFieldId recordFieldId) {
        FieldId sameLevelWantedFieldId = recordFieldId.fieldId().getOfLevel(id.nestingLevel());
        return find(sameLevelWantedFieldId);
    }

    public Optional<FieldSlot> find(@NonNull FieldId fieldId) {
        FieldSlot multifield = fieldSlots.get(fieldId.getRepetition());
        if (multifield == null) {
            return Optional.empty();
        }
        return Optional.of(multifield);
    }

    private Optional<FieldSlot> findSlot(int index) {
        if (!fieldSlots.containsKey(index)) {
            return Optional.empty();
        }
        return Optional.of(fieldSlots.get(index));
    }

    public @NonNull FieldSlot getSlot(int index) {
        if (fieldSlots.isEmpty()) {
            throw new IllegalStateException("Cannot get single slot #" + index + ", no slots present (in multifield " + id + ")");
        }
        return findSlot(index)
                .orElseThrow(() -> new IllegalStateException("Cannot get single slot #" + index + ", there is no such slot (in multifield " + id + ", slots " + fieldSlots + ")"));
    }

    public @NonNull FieldSlot findAnySlotOrCreate() {
        if (!fieldSlots.isEmpty()) {
            return fieldSlots.get(fieldSlots.keySet().stream().min(Integer::compareTo).orElseThrow());
        }
        return createEmptyField(FieldId.FIRST_FIELD_REPETITION, false);
    }

    public boolean isFilled() {
        return !fieldSlots.isEmpty();
    }

    @Override
    public boolean hasRecordLink() {
        return stream().anyMatch(FieldSlot::hasRecordLink);
    }

    @Override
    public @NonNull RecordIdFondPair getExistingRecordLink() {
        return singleExistingField().getExistingRecordLink();
    }

    @Override
    public void setGeneratedRecordLink(@NonNull RecordIdFondPair recordLink) {
        generatedRecordLink = recordLink;
        update(field -> field.setRecordLink(generatedRecordLink));
    }

    @Override
    public boolean hasVectorValue() {
        return switch (fieldSlots.size()) {
            case 0, 1 -> false;
            default -> stream().allMatch(FieldSlot::hasScalarValue);
        };
    }

    @Override
    public @NonNull VectorFieldValue<?> existingVectorValue() {
        List<ScalarFieldValue<?>> scalars = stream().map(FieldSlot::existingScalarValue).collect(Collectors.toUnmodifiableList());
        return VectorFieldValue.ofUnknownTypes(scalars);
    }

    /// test multiple linked same-code subfields on https://test.kpsys.cz/api/records/3ef59b7d-6f8e-4700-a292-ed5fb92f8730
    @Override
    public void setGeneratedVectorValue(@NonNull VectorFieldValue<?> value) {
        generatedError = null;

        ListCutter<FieldSlot> existingFieldsCutter = ListCutter.ofCopyOf(fieldSlots.values());
        for (ScalarFieldValue<?> fieldValueHolder : value.values()) {
            FieldSlot existingFieldSlot = existingFieldsCutter.hasRemainingItems()
                    ? existingFieldsCutter.cutFirst()
                    : createNextEmptyField();
            existingFieldSlot.setGeneratedScalarValue(fieldValueHolder);
            if (generatedRecordLink != null) {
                existingFieldSlot.setGeneratedRecordLink(generatedRecordLink);
            }
        }
    }

    @Override
    public boolean hasScalarValue() {
        return switch (fieldSlots.size()) {
            case 1 -> singleExistingField().getValueHolder() != null;
            default -> false;
        };
    }

    @Override
    public @NonNull ScalarFieldValue<?> existingScalarValue() {
        return Objects.requireNonNull(singleExistingField().getValueHolder());
    }

    @Override
    public void setGeneratedScalarValue(@NonNull ScalarFieldValue<?> value) {
        generatedError = null;
        update(field -> field.setValue(value));
    }

    @Override
    public boolean hasError() {
        return generatedError != null;
    }

    public @NonNull FailedResult getExistingError() {
        return Objects.requireNonNull(generatedError, () -> "No error present in multifield " + id);
    }

    @Override
    public void setGeneratedError(@NonNull FailedResult failedResult) {
        fieldSlots.clear();
        generatedError = failedResult;
        update(field -> field.setError(failedResult));
    }


    @Override
    public boolean hasGeneratedBlankField() {
        return isBlankFieldGenerationCapable() && isFilled();
    }

    @Override
    public void setGeneratedNoValueField() {
        createEmptyField(FieldId.FIRST_FIELD_REPETITION, true);
    }



    public Field<?> singleExistingField() {
        Assert.state(!fieldSlots.isEmpty(), () -> "Cannot get single existing field from multifield " + id + ", there is no slot present (in multifield " + id + ")");
        Assert.state(fieldSlots.size() == 1, () -> "Cannot get single existing field from multifield " + id + ", there must be exactly 1 slot present, but there are " + fieldSlots.size() + " (in multifield " + id + ", slots " + fieldSlots + ")");
        return stream()
                .map(FieldSlot::field)
                .findFirst()
                .orElseThrow();
    }

    private void update(Consumer<Field<?>> update) {
        if (!isFilled()) {
            createEmptyField(FieldId.FIRST_FIELD_REPETITION, false);
        }

        for (FieldSlot fieldSlot : this) {
            update.accept(fieldSlot.field());
        }
    }

    private FieldSlot createNextEmptyField() {
        Integer nextIndex = fieldSlots.keySet().stream().max(Comparator.naturalOrder()).map(maxExistingIndex -> maxExistingIndex + 1).orElse(FieldId.FIRST_FIELD_REPETITION);
        return createEmptyField(nextIndex, false);
    }

    private FieldSlot createEmptyField(int index, boolean withMultifields) {
        RecordFieldId recordFieldId = id.toRecordFieldId(index);
        Field<ScalarFieldValue<?>> field = fieldType.createEmptyField(record, recordFieldId.fieldId(), UuidGenerator.forIdentifier());
        FieldSlot fieldSlot = withMultifields
                ? FieldSlot.createFilledAndCreateGeneratedMultifields(record, recordFieldId, fieldType, field)
                : FieldSlot.createFilled(record, recordFieldId, fieldType, field);
        fieldSlots.put(recordFieldId.fieldId().getRepetition(), fieldSlot);
        return fieldSlot;
    }

    public List<Field<?>> fields() {
        return stream().map(FieldSlot::field).collect(Collectors.toUnmodifiableList());
    }

    public @NonNull Iterator<FieldSlot> iterator() {
        return fieldSlots.values().iterator();
    }

    public @NonNull Stream<FieldSlot> stream() {
        return fieldSlots.values().stream();
    }

    @Override
    public final boolean equals(Object o) {
        if (!(o instanceof Multifield that)) {
            return false;
        }
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }

    @Override
    public String toString() {
        String slotsPart = fieldSlots.isEmpty() ? "<no-slots>" : fieldSlots.size() + " slots: " + fieldSlots.values();
        return "Multifield[" + id + " " + slotsPart + ']';
    }

}
