package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor(staticName = "forFond")
public class TestingFondedFieldTypeFactory {

    @NonNull Fond fond;

    private @NonNull EditableFieldType convert(SimpleFieldType fieldType) {
        return EditableFieldType.ofAlwaysVisibleSingle(fieldType, fond);
    }

    public EditableFieldType controlfieldType(FieldTypeId id) {
        SimpleFieldType fieldType = TestingFieldTypeFactory.controlfieldType(id);
        return convert(fieldType);
    }

    public EditableFieldType datafieldType(FieldTypeId id, String nativeName) {
        SimpleFieldType fieldType = TestingFieldTypeFactory.datafieldType(id, nativeName);
        return convert(fieldType);
    }

    public EditableFieldType datafieldType(FieldTypeId id) {
        SimpleFieldType fieldType = TestingFieldTypeFactory.datafieldType(id);
        return convert(fieldType);
    }

    public EditableFieldType standardSubfieldType(FieldTypeId id) {
        SimpleFieldType fieldType = TestingFieldTypeFactory.standardSubfieldType(id);
        return convert(fieldType);
    }

    public EditableFieldType subfieldType(FieldTypeId id, String nativeName, FieldGeneration fieldGeneration, FieldExportSetting exportSetting) {
        SimpleFieldType fieldType = TestingFieldTypeFactory.subfieldType(id, nativeName, fieldGeneration, exportSetting);
        return convert(fieldType);
    }

}
