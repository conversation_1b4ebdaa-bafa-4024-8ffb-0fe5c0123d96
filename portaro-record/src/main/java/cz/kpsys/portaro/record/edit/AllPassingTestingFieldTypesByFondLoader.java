package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.util.List;
import java.util.Optional;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class AllPassingTestingFieldTypesByFondLoader implements FieldTypesByFondLoader {

    @NonNull FieldTypeLoader fieldTypeLoader;

    @Override
    public List<EditableFieldType> getTopfieldTypesByFond(@NonNull Fond fond) {
        return ListUtil.convertStrict(fieldTypeLoader.getAll(), fieldType -> convertToEditable(fond, fieldType));
    }

    @Override
    public Optional<EditableFieldType> findTopfieldTypeByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId topfieldId, @NonNull WhenMissing whenMissing) {
        return Optional.of(convertToEditable(fond, fieldTypeLoader.getTopfieldTypeById(topfieldId)));
    }

    @Override
    public EditableFieldType getByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId fieldTypeId) {
        return convertToEditable(fond, fieldTypeLoader.getById(fieldTypeId));
    }

    private @NonNull EditableFieldType convertToEditable(@NonNull Fond fond, FieldType fieldType) {
        return new EditableFieldType(fieldType, fond, FieldSettings.unknown(), SubfieldTypeToEditableConverter.createEmpty(fond));
    }
}
