package cz.kpsys.portaro.record.document;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.BasicRecord;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordWellKnownFields.DocumentTitle;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.print.RecordDetailHtmlWithoutLinksPrinter;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.UUID;

public class BasicDocument extends BasicRecord implements Document, Serializable {

    public BasicDocument(@NonNull RecordIdFondPair recordIdFondPair,
                         @Nullable Integer kindedId,
                         @Nullable UUID creationEventId,
                         boolean external,
                         boolean withForeignOccurrences,
                         @NullableNotBlank String name,
                         @Nullable FieldContainer detail,
                         @NonNull ObjectProperties props) {
        super(recordIdFondPair, kindedId, creationEventId, external, withForeignOccurrences, name, detail, props);
    }

    public BasicDocument(@NonNull BasicDocument original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        super(original, newRecordIdFondPair, kindedId);
    }

    @Override
    public @NullableNotBlank String getName() {
        ValFieldGroup query = query(DocumentTitle.Name.QUERY_CODE);
        if (query.isAny()) {
            return StringUtil.notBlankTrimmedString(RecordDetailHtmlWithoutLinksPrinter.printGroup(query.purify()));
        }
        return super.getName();
    }

    @JsonIgnore
    @Override
    public BasicDocument copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, BasicDocument.class);
        return new BasicDocument(this, newRecordIdFondPair, kindedId);
    }

}
