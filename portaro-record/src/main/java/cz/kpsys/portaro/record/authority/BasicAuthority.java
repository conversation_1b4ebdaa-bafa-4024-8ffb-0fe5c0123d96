package cz.kpsys.portaro.record.authority;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.BasicRecord;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.export.RecordViewFunctions;
import lombok.NonNull;
import org.springframework.lang.Nullable;

import java.util.Optional;
import java.util.UUID;

public class BasicAuthority extends BasicRecord implements Authority {

    public BasicAuthority(@NonNull RecordIdFondPair recordIdFondPair,
                          @Nullable Integer kindedId,
                          @Nullable UUID creationEventId,
                          boolean external,
                          boolean withForeignOccurrences,
                          @NullableNotBlank String name,
                          @Nullable FieldContainer detail,
                          @NonNull ObjectProperties props) {
        super(recordIdFondPair, kindedId, creationEventId, external, withForeignOccurrences, name, detail, props);
        setStatus(RecordStatus.NOT_NATIONAL_CREATED_BY_RECONS);
    }

    public BasicAuthority(@NonNull BasicAuthority original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        super(original, newRecordIdFondPair, kindedId);
    }

    @Override
    public @NullableNotBlank String getName() {
        if (StringUtil.isNullOrBlank(super.getName())) {
            Optional<String> fromDetail = RecordViewFunctions.authorityName(this.getDetail());
            if (fromDetail.isPresent()) {
                return fromDetail.get();
            }
        }
        return super.getName();
    }

    @JsonIgnore
    @Override
    public BasicAuthority copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, BasicAuthority.class);
        return new BasicAuthority(this, newRecordIdFondPair, kindedId);
    }

}
