package cz.kpsys.portaro.record.edit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.LabeledIdentified;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.LookupDefinition;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.detail.value.FieldValueConverter;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import org.jspecify.annotations.Nullable;

import java.util.*;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class EditableFieldType implements FieldType {

    public static final Comparator<EditableFieldType> EDITATION_ORDER_COMPARATOR = Comparator.comparing(EditableFieldType::getEditationOrder);

    @NonNull
    FieldType delegate;

    @JsonIgnore
    @Getter
    @NonNull
    Fond effectiveFond;

    @NonNull
    FieldSettings editSetting;

    @NonNull
    Function<FieldType, EditableFieldType> toEditableSubfieldTypeConverter;

    @Nullable
    @NonFinal
    List<EditableFieldType> editableSubfieldTypes = null;

    public EditableFieldType(@NonNull FieldType delegate,
                             @NonNull Fond effectiveFond,
                             @NonNull FieldSettings editSetting,
                             @NonNull Function<FieldType, EditableFieldType> toEditableSubfieldTypeConverter) {
        this.delegate = delegate;
        this.effectiveFond = effectiveFond;
        this.editSetting = editSetting;
        this.toEditableSubfieldTypeConverter = toEditableSubfieldTypeConverter;
    }

    public static EditableFieldType ofAlwaysVisibleSingle(@NonNull FieldType target, @NonNull Fond effectiveFond) {
        return new EditableFieldType(
                target,
                effectiveFond,
                FieldSettings.ofAlwaysVisible(FieldSettings.FIRST_ORDER),
                SubfieldTypeToEditableConverter.createEmpty(effectiveFond)
        );
    }

    public @NonNull FieldDisplayType getDisplayType() {
        return editSetting.displayType();
    }

    public @NonNull FieldRequirementType getRequirementType() {
        return editSetting.requirementType();
    }

    public boolean isRequired() {
        return FieldRequirementType.REQUIRED.equals(getRequirementType());
    }

    public Text getEditNote() {
        Text columnMessageCode = Texts.ofMessageCoded("field.editNote.%s".formatted(getFieldTypeId().value()));
        return Texts.ofDefaulted(columnMessageCode, Texts.ofEmpty());
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createEmptyField(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId, @NonNull UUID id) {
        return Field.empty(id, recordIdFondPair, RecordFieldId.of(recordIdFondPair, fieldId), this);
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createField(@NonNull RecordIdFondPair recordIdFondPair, @NonNull FieldId fieldId, @NonNull UUID id, @NonNull FieldPayload<VH> payload) {
        Field<VH> field = createEmptyField(recordIdFondPair, fieldId, id);
        field.setPayload(payload);
        return field;
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createEmptyFieldByParentId(@NonNull RecordIdFondPair recordIdFondPair, @Nullable FieldId parentFieldId, @NonNull UUID id) {
        FieldId fieldId = parentFieldId != null
                ? parentFieldId.sub(getCode(), FieldId.FIRST_FIELD_REPETITION)
                : FieldId.top(getCode(), FieldId.FIRST_FIELD_REPETITION);
        return createEmptyField(recordIdFondPair, fieldId, id);
    }

    public <VH extends ScalarFieldValue<?>> Field<VH> createFieldByParentId(@NonNull RecordIdFondPair recordIdFondPair, @Nullable FieldId parentFieldId, @NonNull UUID id, @NonNull FieldPayload<VH> payload) {
        Field<VH> field = createEmptyFieldByParentId(recordIdFondPair, parentFieldId, id);
        field.setPayload(payload);
        return field;
    }

    @JsonIgnore
    public @NonNull Integer getEditationOrder() {
        return editSetting.order();
    }

    public @NonNull FieldTypeId getFieldTypeId() {
        return delegate.getFieldTypeId();
    }

    public boolean isRepeatable() {
        return delegate.isRepeatable();
    }

    public boolean isAutonomous() {
        return delegate.isAutonomous();
    }

    public Optional<ScalarDatatype> getDatatype() {
        return delegate.getDatatype();
    }

    public ScalarDatatype getDatatypeOrThrow() {
        return delegate.getDatatypeOrThrow();
    }

    public FieldExportSetting getExportSetting() {
        return delegate.getExportSetting();
    }

    public TransferType getTransferType() {
        return delegate.getTransferType();
    }

    public boolean isVirtualGroup() {
        return delegate.isVirtualGroup();
    }

    public boolean isWholeFieldGenerated() {
        return delegate.isWholeFieldGenerated();
    }

    public @NonNull FieldGeneration getFieldGeneration() {
        return delegate.getFieldGeneration();
    }

    public boolean hasAutonomousSubfieldTypes() {
        return delegate.hasAutonomousSubfieldTypes();
    }

    public FieldType addSubfieldType(FieldType subfieldType) {
        return delegate.addSubfieldType(subfieldType);
    }

    @Deprecated
    @Override
    public @NonNull EditableFieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull FieldTypeId fieldTypeId) {
        return getSubfieldTypeFor(fieldTypeId);
    }

    @Override
    public @NonNull EditableFieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        FieldType target = delegate.getSubfieldTypeOrParentVirtualGroupTypeFor(subfieldCode);
        return toEditableSubfieldTypeConverter.apply(target);
    }

    @Override
    public @NonNull EditableFieldType getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        FieldType target = delegate.getSubfieldTypeFor(fieldTypeId);
        return toEditableSubfieldTypeConverter.apply(target);
    }

    @Override
    public @NonNull List<EditableFieldType> getSubfieldTypes() {
        if (editableSubfieldTypes == null) {
            editableSubfieldTypes = delegate.getSubfieldTypes().stream()
                    .map(toEditableSubfieldTypeConverter)
                    .sorted(EDITATION_ORDER_COMPARATOR)
                    .toList();
        }
        return editableSubfieldTypes;
    }

    public FieldValueConverter getValueConverter() {
        return delegate.getValueConverter();
    }

    public @NonNull FieldSource getFieldSource() {
        return delegate.getFieldSource();
    }

    public @NonNull Set<LookupDefinition> getDependencies() {
        return delegate.getDependencies();
    }

    public @NonNull Optional<Formula> getFormula() {
        return delegate.getFormula();
    }

    public @NonNull Optional<Codebook<? extends LabeledIdentified<String>, String>> getCodebook() {
        return delegate.getCodebook();
    }

    public @NonNull Optional<Fond> getLinkedAnotherRecordFond() {
        return delegate.getLinkedAnotherRecordFond();
    }

    public String getId() {
        return delegate.getId();
    }

    public Text getText() {
        return delegate.getText();
    }

    public String getName() {
        return delegate.getName();
    }

    public @NonNull String getCode() {
        return delegate.getCode();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof FieldType that)) {
            return false;
        }
        return delegate.equals(that);
    }

    @Override
    public int hashCode() {
        return Objects.hash(delegate);
    }

    @Override
    public String toString() {
        return delegate.toString();
    }

}
