package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.logging.ExecutionTimeLogged;
import cz.kpsys.portaro.record.authority.Authority;
import cz.kpsys.portaro.record.document.Document;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;

@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbAllDatabasedRichRecordsByIdsLoader implements InternalRecordLoader, RowMapper<Record> {

    public static final String[] ALL_COLUMNS = {
            TC(RECORD.TABLE, RECORD.ID),
            TC(RECORD.TABLE, RECORD.MASTER_RECORD_ID),
            TC(RECORD.TABLE, RECORD.FOND_ID),
            TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID),
            TC(RECORD.TABLE, RECORD.CREATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DIRECTORY_ID),
            TC(RECORD.TABLE, RECORD.PRIMARY_FILE_ID),

            AS(COALESCE(TC(KAT1_4.TABLE, KAT1_4.NAZEV), TC(KATAUT_4.TABLE, KATAUT_4.NAZEV)), COMMON_KINDED_NAME),

            TC(KAT1_4.TABLE, KAT1_4.ID_ZAZ),
            TC(KAT1_4.TABLE, KAT1_4.AUTOR),
            TC(KAT1_4.TABLE, KAT1_4.NAKL),
            TC(KAT1_4.TABLE, KAT1_4.ISBN),
            TC(KAT1_4.TABLE, KAT1_4.ROK_OD),
            TC(KAT1_4.TABLE, KAT1_4.ROK_DO),

            TC(KATAUT_4.TABLE, KATAUT_4.ID_AUT)
    };

    public static final String[] DOC_ALL_COLUMNS = {
            TC(RECORD.TABLE, RECORD.ID),
            TC(RECORD.TABLE, RECORD.MASTER_RECORD_ID),
            TC(RECORD.TABLE, RECORD.FOND_ID),
            TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID),
            TC(RECORD.TABLE, RECORD.CREATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DIRECTORY_ID),
            TC(RECORD.TABLE, RECORD.PRIMARY_FILE_ID),

            AS(TC(KAT1_4.TABLE, KAT1_4.NAZEV), COMMON_KINDED_NAME),

            TC(KAT1_4.TABLE, KAT1_4.ID_ZAZ),
            TC(KAT1_4.TABLE, KAT1_4.AUTOR),
            TC(KAT1_4.TABLE, KAT1_4.NAKL),
            TC(KAT1_4.TABLE, KAT1_4.ISBN),
            TC(KAT1_4.TABLE, KAT1_4.ROK_OD),
            TC(KAT1_4.TABLE, KAT1_4.ROK_DO),

            NULL_AS(KATAUT_4.ID_AUT)
    };

    public static final String[] AUT_ALL_COLUMNS = {
            TC(RECORD.TABLE, RECORD.ID),
            TC(RECORD.TABLE, RECORD.MASTER_RECORD_ID),
            TC(RECORD.TABLE, RECORD.FOND_ID),
            TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID),
            TC(RECORD.TABLE, RECORD.CREATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID),
            TC(RECORD.TABLE, RECORD.DIRECTORY_ID),
            TC(RECORD.TABLE, RECORD.PRIMARY_FILE_ID),

            AS(TC(KATAUT_4.TABLE, KATAUT_4.NAZEV), COMMON_KINDED_NAME),

            NULL_AS(KAT1_4.ID_ZAZ),
            NULL_AS(KAT1_4.AUTOR),
            NULL_AS(KAT1_4.NAKL),
            NULL_AS(KAT1_4.ISBN),
            NULL_AS(KAT1_4.ROK_OD),
            NULL_AS(KAT1_4.ROK_DO),

            TC(KATAUT_4.TABLE, KATAUT_4.ID_AUT)
    };

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull RowMapper<Document> documentRowMapper;
    @NonNull RowMapper<Record> authorityRowMapper;


    @Transactional(readOnly = true)
    @Override
    public Record getById(@NonNull UUID id) throws ItemNotFoundException {
        return InternalRecordLoader.super.getById(id);
    }

    @Transactional(readOnly = true)
    @ExecutionTimeLogged
    @Override
    public List<Record> getAllByIds(@NonNull List<UUID> ids) {
        if (ids.isEmpty()) {
            return List.of();
        }
        return DataUtils.loadInChunksWithSortingById(ids, 150, this::getAllByIdsFromDb, Record::getId);
    }

    @Transactional(readOnly = true)
    @ExecutionTimeLogged
    @Override
    public List<Document> getDocumentsByKindedIds(@NonNull List<Integer> kindedIds) {
        if (kindedIds.isEmpty()) {
            return List.of();
        }
        return DataUtils.loadInChunksWithSortingById(kindedIds, 500, this::getAllByDocumentKindedIdsFromDb, Record::getKindedId).stream()
                .map(record -> (Document) record)
                .toList();
    }

    @Transactional(readOnly = true)
    @ExecutionTimeLogged
    @Override
    public List<Authority> getAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds) {
        if (kindedIds.isEmpty()) {
            return List.of();
        }
        return DataUtils.loadInChunksWithSortingById(kindedIds, 500, this::getAllByAuthorityKindedIdsFromDb, Record::getKindedId).stream()
                .map(record -> (Authority) record)
                .toList();
    }

    private List<Record> getAllByDocumentKindedIdsFromDb(@NonNull @NotEmpty List<Integer> documentKindedIds) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(DOC_ALL_COLUMNS);
        sq.from(RECORD.TABLE);
        sq.joins().addLeft(KAT1_4.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KAT1_4.TABLE, KAT1_4.RECORD_ID)));
        sq.where().in(TC(KAT1_4.TABLE, KAT1_4.ID_ZAZ), documentKindedIds);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    private List<Record> getAllByAuthorityKindedIdsFromDb(@NonNull @NotEmpty List<Integer> authorityKindedIds) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(AUT_ALL_COLUMNS);
        sq.from(RECORD.TABLE);
        sq.joins().addLeft(KATAUT_4.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));
        sq.where().or().in(TC(KATAUT_4.TABLE, KATAUT_4.ID_AUT), authorityKindedIds);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    private List<Record> getAllByIdsFromDb(@NonNull @NotEmpty List<UUID> ids) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.select(ALL_COLUMNS);
        sq.from(RECORD.TABLE);
        sq.joins().addLeft(KAT1_4.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KAT1_4.TABLE, KAT1_4.RECORD_ID)));
        sq.joins().addLeft(KATAUT_4.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));
        sq.where().or().in(TC(RECORD.TABLE, RECORD.ID), ids);
        return jdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
    }

    @Override
    public Record mapRow(@NonNull ResultSet rs, int rowNum) throws SQLException {
        UUID recordId = DbUtils.uuidNotNull(rs, RECORD.ID);

        Integer documentKindedId = DbUtils.getInteger(rs, KAT1_4.ID_ZAZ);
        Integer authorityKindedId = DbUtils.getInteger(rs, KATAUT_4.ID_AUT);
        if (documentKindedId != null && authorityKindedId != null) {
            throw new IllegalStateException("Record %s has row in both kat1_4 and kataut4".formatted(recordId));
        }
        if (documentKindedId == null && authorityKindedId == null) {
            log.warn("Record {} has no row in kat1_4 nor kataut4", recordId);
        }

        if (documentKindedId != null) {
            return documentRowMapper.mapRow(rs, rowNum);
        }
        return authorityRowMapper.mapRow(rs, rowNum);
    }
}
