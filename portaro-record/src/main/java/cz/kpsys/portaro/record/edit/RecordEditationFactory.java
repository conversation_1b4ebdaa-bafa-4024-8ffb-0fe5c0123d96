package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordCatalogizationPhase;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordSaver;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.detail.dflt.DefaultFieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.fieldshierarchy.SetFieldValueRequest;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.load.RecordFieldsLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class RecordEditationFactory {

    @NonNull RecordFieldTypesLoader missingAddingRecordEditableFieldTypesLoader;
    @NonNull RecordValidator recordValidator;
    @NonNull RecordSaver recordSaver;
    @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader;
    @NonNull ContextualProvider<Department, RecordCatalogizationPhase> publishingDocumentCatalogizationPhaseProvider;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;
    @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull RecordFieldsLoader recordFieldsLoader;
    @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver;


    public RecordAddingBuilder on(@NonNull Department ctx) {
        return on(ctx, List.of());
    }

    public RecordAddingBuilder on(@NonNull Department ctx, @NonNull List<Department> holdings) {
        Dependencies dependencies = new Dependencies(
                missingAddingRecordEditableFieldTypesLoader,
                recordValidator,
                recordSaver,
                unknownSupportingEditableTopfieldTypeLoader,
                publishingDocumentCatalogizationPhaseProvider,
                recordHoldingUpserter,
                fieldValueCommandResolver,
                fieldTypeLoader,
                recordFieldEditor,
                recordFieldsLoader,
                defaultFieldValueCommandResolver
        );
        return new RecordAddingBuilder(dependencies, ctx, holdings);
    }


    private record Dependencies(
        @NonNull RecordFieldTypesLoader missingAddingRecordEditableFieldTypesLoader,
        @NonNull RecordValidator recordValidator,
        @NonNull RecordSaver recordSaver,
        @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader,
        @NonNull ContextualProvider<Department, RecordCatalogizationPhase> publishingDocumentCatalogizationPhaseProvider,
        @NonNull RecordHoldingUpserter recordHoldingUpserter,
        @NonNull FieldValueCommandResolver fieldValueCommandResolver,
        @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader,
        @NonNull RecordFieldEditor recordFieldEditor,
        @NonNull RecordFieldsLoader recordFieldsLoader,
        @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver
    ) {}

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class RecordAddingBuilder {

        @NonNull Dependencies dependencies;
        @NonNull Department ctx;
        @NonNull List<Department> holdings;
        @NonFinal boolean external;

        public RecordAddingBuilder ofExternal() {
            this.external = true;
            return this;
        }

        public FinalBuilder ofNew(UUID recordId, @NonNull Fond fond) {
            Record record = Record.createDraft(RecordIdFondPair.of(recordId, fond), external, new SimpleFieldContainer());
            return new FinalBuilder(dependencies, ctx, record, holdings);
        }

        public FinalBuilder ofNew(@NonNull Fond fond) {
            return ofNew(UuidGenerator.forIdentifier(), fond);
        }

        public FinalBuilder ofNew(@NonNull Fond fond, @NonNull FieldContainer detail) {
            RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UuidGenerator.forIdentifier(), fond);
            Record record = Record.createDraft(recordIdFondPair, external, detail.copy(recordIdFondPair, null));
            return new FinalBuilder(dependencies, ctx, record, holdings);
        }

        public FinalBuilder ofExisting(@NonNull Record record) {
            Objects.requireNonNull(record.getFond(), () -> String.format("Record %s has not fond", record));
            return new FinalBuilder(dependencies, ctx, record.copy(record.idFondPair(), record.getKindedId()), holdings);
        }

    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class FinalBuilder {

        @NonNull Dependencies dependencies;
        @NonNull @NonFinal Department ctx;
        @NonNull Record record;
        @NonNull List<Department> holdings;
        @NonFinal boolean defaultInitialValuesFill = false;
        @NonFinal boolean machineEditation = false;
        @NonFinal boolean createContextualHolding = true;
        @NonFinal Map<String, SetFieldValueRequest> customInitialValues = null;


        public FinalBuilder withDefaultInitialValuesFilled() {
            this.defaultInitialValuesFill = true;
            return this;
        }

        public FinalBuilder withCustomInitialValues(Map<String, SetFieldValueRequest> initialValues) {
            this.customInitialValues = initialValues;
            return this;
        }

        public FinalBuilder byMachineEditation() {
            this.machineEditation = true;
            return this;
        }

        public FinalBuilder notCreatingContextualHolding() {
            this.createContextualHolding = false;
            return this;
        }


        public RecordEditation build(@NonNull UserAuthentication currentAuth) {
            var newHoldings = new ArrayList<>(holdings);
            if (createContextualHolding && !newHoldings.contains(ctx)) {
                newHoldings.add(ctx);
            }

            RecordEditation editation = new DefaultRecordEditation(
                    record,
                    ctx,
                    newHoldings,
                    machineEditation,
                    dependencies.missingAddingRecordEditableFieldTypesLoader(),
                    dependencies.recordValidator(),
                    dependencies.recordSaver(),
                    dependencies.unknownSupportingEditableTopfieldTypeLoader(),
                    dependencies.publishingDocumentCatalogizationPhaseProvider(),
                    dependencies.recordHoldingUpserter(),
                    dependencies.recordFieldsLoader()
            );

            // Fill default values from settings
            if (defaultInitialValuesFill) {
                editation = new DefaultInitialValuesFillingRecordEditationWrapper(editation, dependencies.recordFieldEditor(), dependencies.defaultFieldValueCommandResolver(), ctx, currentAuth);
            }

            if (customInitialValues != null) {
                editation = new CustomInitialValuesFillingRecordEditationWrapper(
                        editation,
                        dependencies.fieldTypeLoader(),
                        dependencies.recordFieldEditor(),
                        dependencies.fieldValueCommandResolver(),
                        ctx,
                        customInitialValues,
                        currentAuth
                );
            }

            return editation;
        }
    }

}
