package cz.kpsys.portaro.record.detail.constraints;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.With;
import lombok.experimental.FieldDefaults;
import org.jspecify.annotations.Nullable;

import java.util.Optional;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FieldTypeConstraintsResolver {

    @NonNull ByIdLoadable<? extends Record, UUID> nonDetailedRecordLoader;
    @NonNull ByIdLoadable<? extends Record, UUID> recordLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;

    public FieldTypeConstraints resolve(@NonNull RecordIdFieldTypeId recordIdFieldTypeId) {
        Record record = nonDetailedRecordLoader.getById(recordIdFieldTypeId.recordId());
        Fond fond = record.getFond();
        EditableFieldType fondedFieldType = fieldTypesByFondLoader.findByFondAndId(fond, recordIdFieldTypeId.fieldTypeId(), FieldTypesByFondLoader.WhenMissing.THROW).orElseThrow();

        FieldTypeConstraints constraints = FieldTypeConstraints.empty();

        Optional<Fond> linkedFond = fondedFieldType.getLinkedAnotherRecordFond();
        if (linkedFond.isPresent()) {
            constraints = constraints.withFond(linkedFond.get());
        }

        return constraints;
    }

    @With
    public record FieldTypeConstraints(
            @Nullable Fond fond
    ) {

        public static FieldTypeConstraints empty() {
            return new FieldTypeConstraints(null);
        }
    }

}
