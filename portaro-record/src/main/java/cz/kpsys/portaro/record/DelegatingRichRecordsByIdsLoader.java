package cz.kpsys.portaro.record;

import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.concurrent.SizedMultiLocker;
import cz.kpsys.portaro.commons.concurrent.StripedLocker;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ComparatorForExplicitIdSorting;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.TimeMeter;
import cz.kpsys.portaro.logging.ExecutionTimeLogged;
import cz.kpsys.portaro.record.authority.Authority;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import cz.kpsys.portaro.record.document.Document;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class DelegatingRichRecordsByIdsLoader implements RichRecordLoader {

    @NonNull DynamicCache<Record> cache; //jednoducha cache ukladajici poslednich par zaznamu
    @NonNull AllByIdsLoadable<IdentifiedFieldContainer, Record> recordDetailLoader;
    @NonNull InternalRecordLoader databaseRecordAllByIdsLoader;
    boolean loadDetail;
    @NonNull SizedMultiLocker<UUID> locker = new StripedLocker<>();

    @Override
    public Record getDetailed(Record basic) {
        if (basic.getDetail() != null) {
            return basic;
        }
        loadRecordDetailsIfMissing(List.of(basic));
        return basic;
    }

    @Override
    public Record getById(@NonNull UUID id) {
        return getById(id, loadDetail);
    }

    @ExecutionTimeLogged
    @Override
    public Record getById(@NonNull UUID id, boolean withDetail) {
        return locker.lock(id, () -> {
            TimeMeter tm = TimeMeter.start();
            log.info("Loading record {} - started", id);

            //nejdrive zkusim najit dokument v cache
            Optional<Record> cached = cache.findById(id);
            Record r = cached.orElseGet(() -> databaseRecordAllByIdsLoader.getById(id));

            if (withDetail) {
                loadRecordDetailsIfMissing(List.of(r));
            }

            cache.add(id, r);

            if (log.isInfoEnabled()) {
                log.info("Loading record {} - finished ({})", id, tm.elapsedTimeString());
            }

            return r;
        });
    }

    @Override
    public List<Record> getAllByIds(List<UUID> ids, boolean withDetail) {
        //je-li seznam prazdny, nemusime hledat
        if (ListUtil.isNullOrEmpty(ids)) {
            return List.of();
        }

        List<Record> loadedOrCachedRecords = locker.lock(ids, () -> {
            List<UUID> idsToLoad = new ArrayList<>(ids.size());
            List<Record> result = new ArrayList<>(ids.size());

            for (UUID id : ids) {
                Optional<Record> cached = cache.findById(id);
                if (cached.isPresent()) {
                    result.add(cached.get());
                } else {
                    idsToLoad.add(id);
                }
            }

            if (log.isInfoEnabled()) {
                log.info("In cache is {} records, will load {} records from DB.", result.size(), idsToLoad.size());
            }

            //zaznamy, ktere nemam v cachi nactu z db

            if (!idsToLoad.isEmpty()) {
                List<Record> databased = databaseRecordAllByIdsLoader.getAllByIds(idsToLoad);

                //prave nactene dokumenty hodime do cache
                databased.forEach(databasedRecord -> cache.add(databasedRecord.getId(), databasedRecord));

                //spojime cached a databased dokumenty
                result.addAll(databased);
            }

            //pokud chceme i detail, nacteme detaily
            if (withDetail) {
                loadRecordDetailsIfMissing(result);
            }

            return result;
        });

        try {
            Assert.state(ids.size() == loadedOrCachedRecords.size(), () -> {
                var notFound = new ArrayList<>();
                var idCounts = new HashMap<Object, Integer>();

                for (var id : ids) {
                    idCounts.merge(id, 1, Integer::sum);

                    var found = loadedOrCachedRecords.stream()
                            .map(Record::getId)
                            .anyMatch(foundId -> foundId.equals(id));
                    if (!found) {
                        notFound.add(id);
                    }
                }

                var duplicates = idCounts.entrySet().stream()
                        .filter(e -> e.getValue() > 1)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue,
                                (a, b) -> a,
                                LinkedHashMap::new
                        ));

                if (notFound.isEmpty() && duplicates.isEmpty()) {
                    return "Unexpected mismatch in record count.";
                }

                var message = new StringBuilder("Didn't load all records!");

                if (!notFound.isEmpty()) {
                    message.append(" Missing: ").append(notFound);
                }

                if (!duplicates.isEmpty()) {
                    message.append(" Duplicates in input IDs: ").append(duplicates);
                }

                return message.toString();
            });
        } catch (IllegalStateException e) {
            log.error("Record loading assertion failed. Details: {}", e.getMessage(), e);
        }


        //musime seradit podle idListu
        loadedOrCachedRecords.sort(new ComparatorForExplicitIdSorting<>(ids, Record::getId));
        return loadedOrCachedRecords;
    }

    @ExecutionTimeLogged
    @Override
    public List<Record> getAllByIds(@NonNull List<UUID> ids) {
        return getAllByIds(ids, loadDetail);
    }

    @ExecutionTimeLogged
    @Override
    public List<Document> getDocumentsByKindedIds(@NonNull List<Integer> kindedIds) {
        return replaceWithCachedDetailedOrAddToCache(Document.class, databaseRecordAllByIdsLoader.getDocumentsByKindedIds(kindedIds));
    }

    @ExecutionTimeLogged
    @Override
    public List<Document> getDetailedDocumentsByKindedIds(@NonNull List<Integer> kindedIds) {
        List<Document> databasedRecords = getDocumentsByKindedIds(kindedIds);
        loadRecordDetailsIfMissing(databasedRecords);
        return databasedRecords;
    }

    @ExecutionTimeLogged
    @Override
    public List<Authority> getAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds) {
        return replaceWithCachedDetailedOrAddToCache(Authority.class, databaseRecordAllByIdsLoader.getAuthoritiesByKindedIds(kindedIds));
    }

    private <R extends Record> @NonNull List<R> replaceWithCachedDetailedOrAddToCache(@NonNull Class<R> clazz, List<R> nonDetailedDatabasedRecords) {
        List<R> resultRecords = new ArrayList<>(nonDetailedDatabasedRecords.size());

        for (R databasedRecord : nonDetailedDatabasedRecords) {
            Optional<Record> recordFromCache = cache.findById(databasedRecord.getId());
            if (recordFromCache.isPresent() && recordFromCache.get().getDetail() != null) {
                resultRecords.add((R) recordFromCache.get());
            } else {
                cache.add(databasedRecord.getId(), databasedRecord);
                resultRecords.add(databasedRecord);
            }
        }

        return resultRecords;
    }

    @ExecutionTimeLogged
    @Override
    public List<Authority> getDetailedAuthoritiesByKindedIds(@NonNull List<Integer> kindedIds) {
        List<Authority> databasedRecords = getAuthoritiesByKindedIds(kindedIds);
        loadRecordDetailsIfMissing(databasedRecords);
        return databasedRecords;
    }

    private void loadRecordDetailsIfMissing(List<? extends Record> records) {
        List<Record> recordsToFetch = records.stream()
                .filter(record -> {
                    if (record instanceof LazyLoaded lazyLoadedRecord) {
                        return !lazyLoadedRecord.isLoaded();
                    }
                    return Objects.isNull(record.getDetail());
                })
                .collect(Collectors.toUnmodifiableList());

        recordDetailLoader.getAllByIds(recordsToFetch)
                .forEach(detail -> {
                    Record record = Record.getByRecordId(records, detail.getId(), true, () -> "Record detail loader (probably appserver) returned detail with id %s, which we did not want to load - we wanted %s".formatted(detail.getId(), recordsToFetch));
                    record.setDetail(detail);
                });
    }

    @Scheduled(fixedDelay = 10 * 1000, initialDelay = 10 * 1000)
    public void logCurrentState() {
        if (locker.size() > 200) {
            log.warn("Record loader locks size is {} (loader {})", locker.size(), this);
        }
        if (cache instanceof GuavaTimedDynamicCache<?> guavaCache && guavaCache.getSize() > 400) {
            log.warn("Record loader cache size is {} (loader {})", guavaCache.getSize(), this);
        }
    }



}
