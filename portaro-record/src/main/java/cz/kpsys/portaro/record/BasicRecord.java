package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.query.FieldGroupFillerImpl;
import cz.kpsys.portaro.record.query.MarqueryParser;
import cz.kpsys.portaro.record.query.MultipleValFieldGroup;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import cz.kpsys.portaro.record.view.DownloadLinkResolver;
import cz.kpsys.portaro.record.view.DownloadLinkResolverBy856Fields;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.NonNull;
import lombok.Setter;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE)
public class BasicRecord extends BasicKindedIdRecordDescriptor implements Record {

    private static final DownloadLinkResolver DOWNLOAD_LINK_RESOLVER = new DownloadLinkResolverBy856Fields();

    @NonNull
    RecordIdFondPair recordIdFondPair;

    @Getter
    @Setter
    @Nullable // TODO: měl by být NonNull
    UUID creationEventId;

    @Getter
    @Setter
    @Nullable
    UUID activationEventId;

    @Getter
    @Setter
    @Nullable
    UUID deletionEventId;

    @Getter
    @Setter
    boolean external;

    @JsonIgnore
    @Getter
    @Setter
    FieldContainer detail;

    @Getter
    @Setter
    RecordStatus status = RecordStatus.NOT_DEFINED;

    @Getter
    @Setter
    boolean valid = true;

    @Deprecated
    @Getter
    @NonNull
    final ObjectProperties props;

    @Getter
    @Setter
    private Integer directoryId;

    @Getter
    @Setter
    private IDdFile cover;

    @Getter
    @Setter(AccessLevel.PROTECTED)
    private boolean withForeignOccurrences;

    final MarqueryParser queryParser = new MarqueryParser();

    public BasicRecord(@NonNull RecordIdFondPair recordIdFondPair,
                       @Nullable Integer kindedId,
                       @Nullable UUID creationEventId,
                       boolean external,
                       boolean withForeignOccurrences,
                       @NullableNotBlank String name,
                       @Nullable FieldContainer detail,
                       @Deprecated @NonNull ObjectProperties props) {
        super(recordIdFondPair.id().id(), kindedId);
        this.recordIdFondPair = recordIdFondPair;
        this.creationEventId = creationEventId;
        this.external = external;
        this.withForeignOccurrences = withForeignOccurrences;
        this.detail = detail;
        this.props = props;
        setName(name);
    }

    public BasicRecord(@NonNull BasicRecord original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        this(
                newRecordIdFondPair,
                kindedId,
                original.creationEventId,
                original.external,
                original.withForeignOccurrences,
                original.getName(),
                ObjectUtil.elvis(original.detail, existingDetail -> existingDetail.copy(newRecordIdFondPair, kindedId)),
                original.props
        );
        this.activationEventId = original.activationEventId;
        this.deletionEventId = original.deletionEventId;
        this.status = original.status;
        this.valid = original.valid;
        this.directoryId = original.directoryId;
        this.cover = original.cover;
    }

    @Override
    public @NullableNotBlank String getName() {
        return super.getName();
    }

    @Override
    public @NonNull RecordIdFondPair idFondPair() {
        return recordIdFondPair;
    }

    @NonNull
    @Override
    public String getType() {
        if (getFond().isOfAuthority()) {
            return TYPE_AUTHORITY;
        }
        return TYPE_DOCUMENT;
    }

    @Override
    public @NonNull Fond getFond() {
        return recordIdFondPair.fond();
    }

    @Override
    public void setFond(Fond fond) {
        recordIdFondPair = recordIdFondPair.withFond(fond);
    }

    @Override
    public List<Field<?>> getFields(String fieldNumber) {
        return getDetail() == null ? null : getDetail().getFields(By.code(fieldNumber));
    }

    @Override
    public List<Field<?>> getFields() {
        return getDetail() == null ? null : getDetail().getFields();
    }

    @Override
    public ValFieldGroup query(String q) {
        return getDetail() == null ? MultipleValFieldGroup.empty() : queryParser.parse(q, FieldGroupFillerImpl.ofFlattedDetail(getDetail()));
    }

    @Override
    public boolean isDownloadable() {
        return !DOWNLOAD_LINK_RESOLVER.resolveLinks(this).isEmpty();
    }

    @JsonIgnore
    @Override
    public BasicRecord copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, BasicRecord.class);
        return new BasicRecord(this, newRecordIdFondPair, kindedId);
    }

    @Override
    public String toString() {
        return getType() + " " + getId() + " (" + StringUtil.limitCharsAndTrimWithoutEllipsis(StringUtil.notNullString(getName()), 10, false) + ")";
    }

}