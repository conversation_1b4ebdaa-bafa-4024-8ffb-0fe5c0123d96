package cz.kpsys.portaro.record;

import cz.kpsys.portaro.CoreConstants;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.property.SimpleProperty;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.property.JavatypedDatatypedProperty;
import cz.kpsys.portaro.property.PropertyFactory;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordIdFieldTypeId;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.operation.RecordOperationType;
import cz.kpsys.portaro.search.SearchParamsConstants;
import cz.kpsys.portaro.search.restriction.Junction;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static cz.kpsys.portaro.datatype.Datatype.scalar;

public class RecordConstants {

    public static final String AUTHORITY_PREFIX = "A";
    public static final String DOCUMENT_PREFIX = "D";

    public static final String MOCK_RECORD_ID_STRING = "00000000-0000-0000-0000-000000000000";
    public static final UUID MOCK_RECORD_ID = UUID.fromString("00000000-0000-0000-0000-000000000000");

    public static class IdPrefixes {
        public static final String DELIMITER = ":";
        public static final String DOCUMENT_KINDED_ID = "dkid";
        public static final String RECORD_FIELD1_ID = "f1";
    }

    public static class LocalizationCodes {
        public static final String MarcXmlReconstructionError = "record.ErrorWhileFromMarcXmlReconstruction";
    }

    public static class CacheNames {
        public static final String DEFAULT_FIELD_VALUES = "defaultFieldValues";
    }

    public static class MappedActions {
        public static final String RECORD_HOLDING_CREATION = "record-holding-creation";
        public static final String RECORD_HOLDING_CREATION_NO_RECORD_PRESEARCH = "record-holding-creation-no-record-presearch";
    }

    public static class Datatype {
        public static final ScalarDatatype ISBN = scalar("ISBN");
        public static final ScalarDatatype ISSN = scalar("ISSN");
        public static final ScalarDatatype ISBN_OR_ISSN = scalar("ISBN_OR_ISSN");
        public static final ScalarDatatype FOND = scalar("FOND");
        @Deprecated
        public static final ScalarDatatype DOCUMENT_FOND = scalar("FOND_DOK");
        @Deprecated
        public static final ScalarDatatype AUTHORITY_FOND = scalar("FOND_AUT");
        public static final ScalarDatatype RECORD_STATUS = scalar("RECORD_STATUS");
        public static final ScalarDatatype RECORD = scalar("RECORD");
        public static final ScalarDatatype RECORD_ID_FIELD_TYPE_ID = scalar("RecordIdFieldTypeId");
        public static final ScalarDatatype DOCUMENT = scalar("DOCUMENT");
        public static final ScalarDatatype EXEMPLAR_STATUS = scalar("STATUS_EX");
        public static final ScalarDatatype EXEMPLAR = scalar("EXEMPLAR");
        public static final ScalarDatatype THEMATIC_GROUP = scalar("TEMSKUP");
        public static final ScalarDatatype ACQUISITION_WAY = scalar("ZP_NAB");
        public static final ScalarDatatype RECORD_OPERATION_TYPE = scalar("OPERATION_TYPE");
        public static final ScalarDatatype FIELD_ID = scalar("FIELD_ID");
    }

    public static class SearchParams implements SearchParamsConstants {

        public static final JavatypedDatatypedProperty<Datasource> DATASOURCE = PropertyFactory.ofSearchProperty("datasource", Texts.ofMessageCoded("commons.Database"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.DATASOURCE), TypeDescriptor.valueOf(Datasource.class));
        public static final JavatypedDatatypedProperty<String> DATASOURCE_GROUP = PropertyFactory.ofSearchProperty("datasourceGroup", Texts.ofMessageCoded("commons.DatabaseType"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class));

        public static final JavatypedDatatypedProperty<String> CNA = PropertyFactory.<String>ofSearchProperty("cna", Texts.ofNative("ČNA"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> ISBN = PropertyFactory.<String>ofSearchProperty("isbn", Texts.ofNative("ISBN"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> ISSN = PropertyFactory.<String>ofSearchProperty("issn", Texts.ofNative("ISSN"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> ISBN_OR_ISSN = PropertyFactory.<String>ofSearchProperty("isbnOrIssn", Texts.ofMessageCoded("commons.IsbnOrIssn"), Datatype.ISBN_OR_ISSN, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankTrimmedString);

        public static final JavatypedDatatypedProperty<String> AUTHOR = PropertyFactory.<String>ofSearchProperty("author", Texts.ofMessageCoded("hledani.autorDokumentu"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> PUBLISHER = PropertyFactory.<String>ofSearchProperty("publisher", Texts.ofMessageCoded("hledani.Publishing"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> ICO = PropertyFactory.<String>ofSearchProperty("ico", Texts.ofMessageCoded("commons.CIN"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class));

        public static final JavatypedDatatypedProperty<Integer> PUBLICATION_YEAR = PropertyFactory.<Integer>ofSearchProperty("publicationYear", Texts.ofMessageCoded("hledani.rokVydani"), CoreConstants.Datatype.YEAR, TypeDescriptor.valueOf(Integer.class))
                .checkNotnullModifiedValue(integer -> Assert.isTrue(integer > 0, "Publication year must be greater than zero"));

        public static final JavatypedDatatypedProperty<String> PREFIX = PropertyFactory.<String>ofSearchProperty("prefix", Texts.ofMessageCoded("hledani.InitialLetters"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<String> DIACRITICAL_PREFIX = PropertyFactory.<String>ofSearchProperty("diacriticalPrefix", Texts.ofMessageCoded("hledani.ExactInitialLetters"), CoreConstants.Datatype.TEXT, TypeDescriptor.valueOf(String.class))
                .modifyBeforeSet(StringUtil::notBlankString);

        public static final JavatypedDatatypedProperty<List<UUID>> FORBIDDEN_RECORD = PropertyFactory.ofSearchProperty("forbiddenRecord", Texts.ofMessageCoded("hledani.ForbidenRecord"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.RECORD), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UUID.class)));

        public static final JavatypedDatatypedProperty<List<UUID>> RECORD = PropertyFactory.ofSearchProperty("record", Texts.ofMessageCoded("hledani.Record"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.RECORD), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(UUID.class)));

        public static final JavatypedDatatypedProperty<List<Fond>> ROOT_FOND = PropertyFactory.ofSearchProperty("rootFond", Texts.ofMessageCoded("commons.fondy"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.FOND), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Fond.class)));

        public static final JavatypedDatatypedProperty<List<Fond>> FOND = PropertyFactory.ofSearchProperty("fond", Texts.ofMessageCoded("commons.fondy"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.FOND), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Fond.class)));

        public static final JavatypedDatatypedProperty<RecordIdFieldTypeId> CONSTRAINTS_RECORD_FIELD_TYPE = PropertyFactory.ofSearchProperty("constraintsRecordFieldType", Texts.ofNative("constraintsRecordFieldType"), Datatype.RECORD_ID_FIELD_TYPE_ID, TypeDescriptor.valueOf(RecordIdFieldTypeId.class));

        public static final JavatypedDatatypedProperty<List<Fond>> RECORD_RELATED_RECORD_FOND = PropertyFactory.ofSearchProperty("recordRelatedRecordFond", Texts.ofMessageCoded("commons.FundsOfRelatedRecordsAcquisition"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.FOND), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Fond.class)));

        /**
         * Vzdy nastavujeme hledanou autoritu! I kdyz je autorita vizem, nastavujeme viz, nikoliv jeji skutecnou autoritu!
         */
        public static final JavatypedDatatypedProperty<Record> RECORD_RELATED_RECORD = PropertyFactory.ofSearchProperty("recordRelatedRecord", Texts.ofMessageCoded("record.RelatedAuthority"), Datatype.RECORD, TypeDescriptor.valueOf(Record.class));

        // TODO: rename forbiddenDocumentStatus to excludedRecordStatus (also in custom files)
        public static final JavatypedDatatypedProperty<List<RecordStatus>> FORBIDDEN_RECORD_STATUS = PropertyFactory.ofSearchProperty("forbiddenDocumentStatus", Texts.ofMessageCoded("record.RecordStatusesDisabled"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.RECORD_STATUS), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(RecordStatus.class)));

        public static final JavatypedDatatypedProperty<Boolean> INCLUDE_EXCLUDED = PropertyFactory.<Boolean>ofSearchProperty("includeExcluded", Texts.ofMessageCoded("record.IncludedEliminated"), CoreConstants.Datatype.BOOLEAN, TypeDescriptor.valueOf(Boolean.class))
                .withDefault(Boolean.FALSE);

        public static final JavatypedDatatypedProperty<List<String>> OPERATED_SUBKIND = PropertyFactory.ofSearchProperty("operatedSubkind", Texts.ofMessageCoded("record.ObjectOfProcessing"), cz.kpsys.portaro.datatype.Datatype.listOf(CoreConstants.Datatype.TEXT), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(String.class)));

        public static final JavatypedDatatypedProperty<List<Fond>> OPERATED_ROOT_FOND = PropertyFactory.ofSearchProperty("operatedRootFond", Texts.ofMessageCoded("commons.fondy"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.FOND), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Fond.class)));

        public static final JavatypedDatatypedProperty<List<Fond>> OPERATED_FOND = PropertyFactory.ofSearchProperty("operatedFond", Texts.ofMessageCoded("commons.fondy"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.FOND), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(Fond.class)));

        public static final JavatypedDatatypedProperty<List<RecordOperationType>> OPERATION_TYPE = PropertyFactory.ofSearchProperty("operationType", Texts.ofMessageCoded("record.TypePerformance"), cz.kpsys.portaro.datatype.Datatype.listOf(Datatype.RECORD_OPERATION_TYPE), TypeDescriptor.collection(List.class, TypeDescriptor.valueOf(RecordOperationType.class)));

        public static final JavatypedDatatypedProperty<Instant> OPERATION_DATE = PropertyFactory.ofSearchProperty("date", Texts.ofMessageCoded("record.Date"), CoreConstants.Datatype.DATETIME, TypeDescriptor.valueOf(Instant.class));

        public static final SimpleProperty<Junction<FieldTypeId>> RECORD_FIELD_VALUE_RESTRICTION = PropertyFactory.ofNotFrontendSearchProperty("recordFieldValueRestriction", Texts.ofNative("Record field value restriction"));

        public static final JavatypedDatatypedProperty<FieldTypeId> FIELD_ID = PropertyFactory.ofSearchProperty("fieldId", Texts.ofNative("Field identifier"), Datatype.FIELD_ID, TypeDescriptor.valueOf(FieldTypeId.class));

    }
}
