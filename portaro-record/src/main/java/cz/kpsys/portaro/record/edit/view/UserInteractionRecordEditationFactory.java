package cz.kpsys.portaro.record.edit.view;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.edit.*;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserInteractionRecordEditationFactory {

    @NonNull RecordFieldEditor recordFieldEditor;

    public Builder on(@NonNull Department ctx, @NonNull RecordEditation editation, @NonNull UserAuthentication currentAuth) {
        return new Builder(ctx, editation, recordFieldEditor, currentAuth);
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    public static class Builder {

        @NonNull @NonFinal Department ctx;
        @NonNull RecordEditation editation;
        @NonNull RecordFieldEditor recordFieldEditor;
        @NonNull UserAuthentication currentAuth;
        @NonFinal RecordAutoSavingPolicy autoSavingPolicy = null;
        @NonFinal boolean emptyFieldsAutoCompling = false;
        @NonFinal Fond rootFond;


        /**
         * Department is as preparement for potential refactoring of record editation without department. But maybe it would be bad idea
         */
        public Builder withRevisionSavingAfterEachChangeAlways(@NonNull Department department) {
            this.autoSavingPolicy = RecordAutoSavingPolicy.AFTER_EACH_CHANGE_ALWAYS;
            Assert.state(this.ctx.equals(department), "Currently, department for auto-saving must be same as record editation department, but is not");
            this.ctx = department;
            return this;
        }


        /**
         * Department is as preparement for potential refactoring of record editation without department. But maybe it would be bad idea
         */
        public Builder withRevisionSavingAfterEachChangeWhenDraft(@NonNull Department department) {
            this.autoSavingPolicy = RecordAutoSavingPolicy.AFTER_EACH_CHANGE_WHEN_DRAFT;
            Assert.state(this.ctx.equals(department), "Currently, department for auto-saving must be same as record editation department, but is not");
            this.ctx = department;
            return this;
        }


        public Builder withEmptyFieldsAutoCompling(boolean emptyFieldsAutoCompling) {
            this.emptyFieldsAutoCompling = emptyFieldsAutoCompling;
            return this;
        }

        public Builder withRootFond(Fond rootFond) {
            this.rootFond = rootFond;
            return this;
        }

        public UserInteractionRecordEditation build() {
            RecordEditation editation = this.editation;

            if (emptyFieldsAutoCompling) {
                editation = new EmptyFieldsComplingRecordEditationWrapper(editation, recordFieldEditor, ctx, currentAuth);
            }

            if (autoSavingPolicy != null) {
                editation = new AutoSaveableRecordEditationWrapper(editation, autoSavingPolicy, ctx, currentAuth);
            }

            return new UserInteractionRecordEditation(editation, rootFond);
        }
    }

}
