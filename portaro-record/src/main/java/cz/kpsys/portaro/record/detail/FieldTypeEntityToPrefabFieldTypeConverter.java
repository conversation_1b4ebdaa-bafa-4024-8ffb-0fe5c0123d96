package cz.kpsys.portaro.record.detail;

import cz.kpsys.portaro.collection.ListCutter;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataAccessException;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.NumberUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import cz.kpsys.portaro.record.detail.data.AuthorityFieldTypeEntity;
import cz.kpsys.portaro.record.detail.data.FieldTypeEntity;
import cz.kpsys.portaro.record.detail.fn.Formula;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.link.def.*;
import cz.kpsys.portaro.record.detail.source.FieldSource;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotEmpty;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.converter.Converter;
import org.springframework.dao.EmptyResultDataAccessException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cz.kpsys.portaro.CoreConstants.Datatype.*;
import static cz.kpsys.portaro.record.detail.fn.Formulas.*;
import static java.util.Objects.requireNonNull;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FieldTypeEntityToPrefabFieldTypeConverter implements Converter<List<? extends FieldTypeEntity>, List<PrefabFieldType>> {

    public static final String DECIMAL_NUMBER_PIC_VALUE = "DECIMAL";

    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull ByIdLoadable<TransferType, Integer> transferTypeLoader = TransferType.CODEBOOK;
    @NonNull Provider<@NonNull TransferType> defaultTransferTypeProvider = TransferType.DEFAULT_PROVIDER;
    @NonNull Provider<@NonNull String> rootSerialCodeProvider;

    @Override
    public List<PrefabFieldType> convert(@NonNull List<? extends FieldTypeEntity> entities) {

        List<PrefabFieldType> nativeFieldTypes = ListUtil.convertStrict(entities, this::convertWithExceptionNesting);

        return enhanceNativeTypesWithGeneratedVirtualGroupTypes(nativeFieldTypes);
    }

    private @NonNull List<PrefabFieldType> enhanceNativeTypesWithGeneratedVirtualGroupTypes(List<PrefabFieldType> nativeFieldTypes) {
        List<PrefabFieldType> result = new ArrayList<>(nativeFieldTypes.size() * 2);
        ListCutter<PrefabFieldType> subfieldTypesCutter = ListCutter.ofCopyOf(nativeFieldTypes);

        while (subfieldTypesCutter.hasRemainingItems()) {
            PrefabFieldType prefabFieldType = subfieldTypesCutter.cutFirst();

            if (prefabFieldType.shouldHaveVirtualParent()) {
                FieldTypeId parentVirtualFieldTypeId = prefabFieldType.id().withCode(FieldTypes.VIRTUAL_GROUP_FIELD_CODE);

                List<PrefabFieldType> otherGroupedSiblings = subfieldTypesCutter.cut(prefabFieldType::shouldBeInSameVirtualGroup);
                List<PrefabFieldType> allGroupedSiblings = ListUtil.createNewListPrepending(prefabFieldType, otherGroupedSiblings);

                List<PrefabFieldType> modifiedGroupedSiblings = ListUtil.convertStrict(allGroupedSiblings, groupedSibling -> groupedSibling.withModifiedParentId(parentVirtualFieldTypeId));
                result.add(createParentVirtualFieldType(parentVirtualFieldTypeId, modifiedGroupedSiblings));
                result.addAll(modifiedGroupedSiblings);

            } else {
                result.add(prefabFieldType);
            }
        }

        return result;
    }

    private PrefabFieldType createParentVirtualFieldType(FieldTypeId parentVirtualFieldTypeId, @NotEmpty List<PrefabFieldType> subfieldTypes) {
        String joinedNativeName = subfieldTypes.stream().map(PrefabFieldType::nativeName).collect(Collectors.joining(", "));

        TransferType bestTransferType = Stream.of(TransferType.ADD, TransferType.OVERWRITE, TransferType.REWRITE_WHEN_CREATING, TransferType.DELETE_WHEN_EMPTY, TransferType.NO_TRANSFER)
                .filter(transferType -> subfieldTypes.stream().anyMatch(subfieldType -> subfieldType.transferType() == transferType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("This should not happen - any transfer type was not found"));

        ParentVirtualGroupEntryfieldSiblingLinkDef firstSubfieldLinkDef = (ParentVirtualGroupEntryfieldSiblingLinkDef) subfieldTypes.getFirst().fieldRecordLinkDef();
        FieldRecordLinkDef fieldRecordLinkDef = new EntryfieldLinkDef(firstSubfieldLinkDef.linkedRecordFond());

        List<FieldTypeId> subfieldsTargetExportFieldTypeIds = subfieldTypes.stream()
                .map(PrefabFieldType::fieldExportSetting)
                .filter(FieldExportSetting::isEnabled)
                .map(FieldExportSetting::getTargetFieldTypeId)
                .toList();
        FieldExportSetting exportSetting = FieldExportSetting.toSubfields(subfieldsTargetExportFieldTypeIds);

        return new PrefabFieldType(
                parentVirtualFieldTypeId,
                null,
                joinedNativeName,
                true,
                false,
                true,
                fieldRecordLinkDef,
                FieldGeneration.PHYSICAL,
                FieldSource.common(),
                bestTransferType,
                null,
                exportSetting
        );
    }

    private PrefabFieldType convertWithExceptionNesting(@NonNull FieldTypeEntity entity) {
        try {
            return convert(entity);
        } catch (Exception e) {
            throw new DataAccessException("Cannot load %s: %s".formatted(entity.getFieldTypeId(), e.getMessage()), entity.getFieldTypeId(), e);
        }
    }

    private PrefabFieldType convert(@NonNull FieldTypeEntity entity) {
        boolean inAuthorityRecord = entity instanceof AuthorityFieldTypeEntity;

        @NonNull String exportFieldNumber = String.valueOf(requireNonNull(entity.getExportFieldNumber(), "Exported field number (CIS_TAG) is null"));
        @NullableNotBlank String exportSubfieldCode = StringUtil.notBlankTrimmedString(entity.getExportSubfieldCode());
        @NonNull FieldTypeId typeId = FieldTypeId.parse(entity.getFieldTypeId());
        @NonNull FieldRecordLinkType recordLinkType = FieldRecordLinkType.CODEBOOK.getById(entity.getAuthorityType());
        @NullableNotBlank String pic = StringUtil.notBlankTrimmedString(entity.getPic());
        @NullableNotBlank String phraseGroupId = StringUtil.notBlankTrimmedString(entity.getPhraseGroupId());
        Fond linkedRecordFond = NumberUtil.emptyIfNullOrZero(entity.getLinkedRecordFond()).map(fondLoader::getById).orElse(null);

        Formula formula = null;
        FieldTypeId specificLinkFieldTypeId = null;
        FieldTypeId linkedRecordFieldTypeId = null;
        boolean parentOfGenerated = false;

        if (rootSerialCodeProvider.get().equals("100007000999")) {
            if (typeId.value().equals("d1730.b")) {
                recordLinkType = FieldRecordLinkType.FORMULA;
                specificLinkFieldTypeId = null;
                linkedRecordFond = null;
                formula = sum(localRef(typeId, "d1730.a"), constDecimal2(BigDecimal.valueOf(1.25)));
            }
        }

        if (rootSerialCodeProvider.get().equals("100007000310") || rootSerialCodeProvider.get().equals("100007001019") || rootSerialCodeProvider.get().equals("100007001020")) { // sutor prod + sutor test + sutor import

            /// TODO CENÍK

            if (typeId.value().equals("d2205")) {
                parentOfGenerated = true;
            }
            // LINK FOND
            // Preneseni informace o FONDU z Katalogu cinnosti profesí a strojů
            if (typeId.value().equals("d2205.t")) {
                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
                specificLinkFieldTypeId = FieldTypeId.parse("d2200.main");
                linkedRecordFond = fondLoader.getById(14);
                linkedRecordFieldTypeId = FieldTypeId.parse("fond");
            }

            /// TODO POLOŽKY ZAZÁZKY

//            if (typeId.value().equals("d1601")) {
//                parentOfGenerated = true;
//            }
//            // JEDNODUCHA
//            // Preneseni zakaznika z fondu 1 do polozek zakazky fondu 12
//            if (typeId.value().equals("d1601.a")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d1001.main");
//                linkedRecordFond = fondLoader.getById(1);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d1010.a");
//            }
//
//            // VYPOCET Polozek zakazek a jejich predpokladane nakladove ceny
//            if (typeId.value().equals("d1650")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d1650.c")) { // celkova nakladova cena
//                recordLinkType = FieldRecordLinkType.FORMULA;
//                specificLinkFieldTypeId = null;
//                linkedRecordFond = null;
//                Formula celkovaCenikovka = round2(orZero(multiply(
//                        localRef(typeId, "d1612.c"), // nakladova cena cenikova
//                        localRef(typeId, "d1619.m"), // pocet hodin
//                        localRef(typeId, "d1611.m") // mnozstvi
//                )));
//                Formula celkovaSubdodavka = round2(orZero(multiply(
//                        localRef(typeId, "d1614.c"), // nakladova cena subdodavky
//                        localRef(typeId, "d1611.m") // mnozstvi
//                )));
//                formula = sum(celkovaCenikovka, celkovaSubdodavka);
//            }
//
//            // VYPOCET Polozek zakazek a jejich predpokladane prodejni ceny
//            if (typeId.value().equals("d1651")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d1651.c")) { // celkova prodejni cena
//                recordLinkType = FieldRecordLinkType.FORMULA;
//                specificLinkFieldTypeId = null;
//                linkedRecordFond = null;
//                Formula celkovaCenikovka = round2(orZero(multiply(
//                        localRef(typeId, "d1613.c"), // prodejni cena cenikova
//                        localRef(typeId, "d1619.m"), // pocet hodin
//                        localRef(typeId, "d1611.m") // mnozstvi
//                )));
//                Formula celkovaSubdodavka = round2(orZero(multiply(
//                        localRef(typeId, "d1615.c"), // prodejni cena subdodavky
//                        localRef(typeId, "d1611.m") // mnozstvi
//                )));
//                formula = round2(sum(celkovaCenikovka, celkovaSubdodavka));
//            }

            // TOTO SE MUSÍ ZREVIDOVAT

//            if (typeId.value().equals("d1612")) {
//                parentOfGenerated = true;
//            }
//            // LINK mezi cenikem a polozkama zakazky
//            if (typeId.value().equals("d1612.c")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d1610.main");
//                linkedRecordFond = fondLoader.getById(12);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2211.c");
//            }
//            if (typeId.value().equals("d1613")) {
//                parentOfGenerated = true;
//            }
//            // LINK mezi cenikem a polozkama zakazky
//            if (typeId.value().equals("d1613.c")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d1610.main");
//                linkedRecordFond = fondLoader.getById(12);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.c");
//            }
//
//            /// TODO NÁKLADY
//
//            /// NAKLADOVY CENIK - TYP CINNOSTI
//            if (typeId.value().equals("d2054")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d2054.b")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFond = fondLoader.getById(18);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// NAKLADOVY CENIK - CENY
//            if (typeId.value().equals("d2055")) {
//                parentOfGenerated = true;
//            }
//            // LINK mezi cenou prodejního ceníku (fond 19) a použitým prodávaným ceníkem (fond 21)
//            if (typeId.value().equals("d2055.c")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFond = fondLoader.getById(18);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.c");
//            }
//            if (typeId.value().equals("d2055.d")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFond = fondLoader.getById(18);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.d");
//            }
//            if (typeId.value().equals("d2055.e")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFond = fondLoader.getById(18);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.e");
//            }
//            if (typeId.value().equals("d2055.f")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2054.main");
//                linkedRecordFond = fondLoader.getById(18);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.f");
//            }
//
//            /// PRODEJNI CENIK - TYP CINNOSTI
//            if (typeId.value().equals("d2040")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d2040.b")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFond = fondLoader.getById(19);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2205.t");
//            }
//
//            /// PRODEJNI CENIK - CENY
//            if (typeId.value().equals("d2041")) {
//                parentOfGenerated = true;
//            }
//            // LINK mezi cenou prodejního ceníku (fond 19) a použitým prodávaným ceníkem (fond 21)
//            if (typeId.value().equals("d2041.c")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFond = fondLoader.getById(19);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.c");
//            }
//            if (typeId.value().equals("d2041.d")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFond = fondLoader.getById(19);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.d");
//            }
//            if (typeId.value().equals("d2041.e")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFond = fondLoader.getById(19);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.e");
//            }
//            if (typeId.value().equals("d2041.f")) {
//                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
//                specificLinkFieldTypeId = FieldTypeId.parse("d2040.main");
//                linkedRecordFond = fondLoader.getById(19);
//                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.f");
//            }
//
////            if (typeId.value().equals("d2046")) {
////                parentOfGenerated = true;
////            }
////            // KOMPLEXNI
////            // LINK mezi prodejní cenou profese (fond 12) a prodávanym majetkem (fond 22)
////            if (typeId.value().equals("d2046.c")) {
////                recordLinkType = FieldRecordLinkType.SPECIFIC_FIELD_LINK;
////                specificLinkFieldTypeId = FieldTypeId.parse("d2045.main");
////                linkedRecordFond = fondLoader.getById(12);
////                linkedRecordFieldTypeId = FieldTypeId.parse("d2221.c");
////            }
////
//            // VYPOCET nakladu a jejich vykazane nakladove ceny
//            if (typeId.value().equals("d2071")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d2071.c")) { // Vykazana cena
//                recordLinkType = FieldRecordLinkType.FORMULA;
//                specificLinkFieldTypeId = null;
//                linkedRecordFond = null;
//                Formula pracovniDoba = round2(orZero(multiply(
//                        localRef(typeId, "d2055.c"), // nakladova cena
//                        localRef(typeId, "d2070.m") // pocet hodin vykazanych
//                )));
//                Formula prescas = round2(orZero(multiply(
//                        localRef(typeId, "d2055.d"), // nakladova cena
//                        localRef(typeId, "d2070.n") // pocet hodin prescasovych
//                )));
//                formula = sum(pracovniDoba, prescas);
//            }
//
//            // VYPOCET nakladu a jejich uctovane nakladove ceny
//            if (typeId.value().equals("d2081")) {
//                parentOfGenerated = true;
//            }
//            if (typeId.value().equals("d2081.c")) { // Uctovana cena
//                recordLinkType = FieldRecordLinkType.FORMULA;
//                specificLinkFieldTypeId = null;
//                linkedRecordFond = null;
//                Formula pracovniDoba = round2(orZero(multiply(
//                        localRef(typeId, "d2041.c"), // prodejni cena
//                        localRef(typeId, "d2080.m") // pocet hodin uctovanych
//                )));
//                Formula prescas = round2(orZero(multiply(
//                        localRef(typeId, "d2041.d"), // prodejni cena
//                        localRef(typeId, "d2080.n") // pocet hodin uctovanych
//                )));
////                Formula mnozstviMajetek = round2(orZero(multiply(
////                        localRef(typeId, "d2046.c"), // prodejni cena
////                        localRef(typeId, "d2083.m") // pocet hodin uctovanych stroje
////                )));
//                formula = sum(pracovniDoba, prescas/*, mnozstviMajetek*/);
//            }
        }

        FieldRecordLinkDef fieldRecordLinkDef = getFieldRecordLinkDef(
                recordLinkType,
                formula,
                specificLinkFieldTypeId,
                linkedRecordFieldTypeId,
                linkedRecordFond,
                StringUtil.notBlankTrimmedString(entity.getLinkedRecordEntryFieldSubfieldCode())
        );

        return new PrefabFieldType(
                typeId,
                typeId,
                ObjectUtil.firstNotNull(StringUtil.notBlankTrimmedString(entity.getName()), typeId.toString()),
                false,
                !entity.getNotRepeatable(),
                true,
                fieldRecordLinkDef,
                getFieldValueGeneration(parentOfGenerated, recordLinkType),
                FieldSource.common(),
                getTransferType(entity),
                getExplicitDatatype(typeId, recordLinkType, pic, phraseGroupId),
                typeId.isRoot()
                        ? getFieldExportSetting(inAuthorityRecord, exportFieldNumber)
                        : getSubfieldExportSetting(inAuthorityRecord, exportFieldNumber, exportSubfieldCode, typeId)
        );
    }

    private @NonNull FieldGeneration getFieldValueGeneration(boolean parentOfGenerated, FieldRecordLinkType recordLinkType) {
        if (parentOfGenerated) {
            return FieldGeneration.GENERATED_FIELD_PARENT;
        }
        return switch (recordLinkType) {
            case NO_LINK -> FieldGeneration.PHYSICAL;
            case THIS_FIELD_ENTRYFIELD_LINK -> FieldGeneration.GENERATED_VALUE;
            case THIS_FIELD_RECORD, PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK, SPECIFIC_FIELD_LINK, FORMULA -> FieldGeneration.GENERATED_WHOLE;
        };
    }

    private @NonNull FieldRecordLinkDef getFieldRecordLinkDef(@NonNull FieldRecordLinkType recordLinkType,
                                                              @Nullable Formula formula,
                                                              @Nullable FieldTypeId specificLinkFieldTypeId,
                                                              @Nullable FieldTypeId linkedRecordFieldTypeId,
                                                              @Nullable Fond linkedRecordFond,
                                                              @Nullable String linkedRecordEntryFieldSubfieldCode) {
        return switch (recordLinkType) {
            case NO_LINK -> new NoLinkDef();
            case FORMULA -> new FormulaDef(requireNonNull(formula, () -> "Record link type defined as %s but formula is null".formatted(recordLinkType)));
            case THIS_FIELD_RECORD -> new ThisFieldRecordDef(requireNonNull(linkedRecordFieldTypeId));
            case SPECIFIC_FIELD_LINK -> new SpecificFieldLinkDef(
                    requireNonNull(specificLinkFieldTypeId),
                    requireNonNull(linkedRecordFond),
                    requireNonNull(linkedRecordFieldTypeId)
            );
            case PARENT_VIRTUAL_GROUP_ENTRYFIELD_LINK -> new ParentVirtualGroupEntryfieldSiblingLinkDef(
                    requireNonNull(linkedRecordFond, () -> "Record link type defined as %s but linked record fond is null".formatted(recordLinkType)),
                    requireNonNull(linkedRecordEntryFieldSubfieldCode, () -> "Record link type defined as %s but linked record entry field subfield code is null".formatted(recordLinkType))
            );
            case THIS_FIELD_ENTRYFIELD_LINK -> new SimpleEntryfieldSiblingLinkDef(
                    requireNonNull(linkedRecordFond, () -> "Record link type defined as %s but linked record fond is null".formatted(recordLinkType)),
                    requireNonNull(linkedRecordEntryFieldSubfieldCode, () -> "Record link type defined as %s but linked record entry field subfield code is null".formatted(recordLinkType))
            );
        };
    }

    private FieldExportSetting getFieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber) {
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toField(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber));
    }

    private FieldExportSetting getSubfieldExportSetting(boolean inAuthorityRecord, @NonNull String exportFieldNumber, @Nullable String exportSubfieldCode, @NonNull FieldTypeId subfieldTypeId) {
        String resolvedCode = ObjectUtil.firstNotNull(exportSubfieldCode, subfieldTypeId.getCode());
        if (exportFieldNumber.equals(PrefabFieldType.TARGET_EXPORT_FIELD_NOT_TO_EXPORT) || resolvedCode.equals(PrefabFieldType.TARGET_EXPORT_SUBFIELD_NOT_TO_EXPORT)) {
            return FieldExportSetting.disabled();
        }
        return FieldExportSetting.toSubfield(FieldTypeId.recordField(inAuthorityRecord, exportFieldNumber).sub(resolvedCode));
    }

    private TransferType getTransferType(FieldTypeEntity dto) {
        try {
            return transferTypeLoader.getById(dto.getTransferTypeId());
        } catch (EmptyResultDataAccessException | ItemNotFoundException e) {
            return defaultTransferTypeProvider.get();
        }
    }

    private @Nullable ScalarDatatype getExplicitDatatype(@NonNull FieldTypeId typeId, @NonNull FieldRecordLinkType recordLinkType, @NullableNotBlank String pic, @NullableNotBlank String phraseGroupId) {
        if (pic != null && phraseGroupId != null) {
            log.warn("Invalid fdef[aut] configuration for field {}: both pic ('{}') and fk_frazeskup ('{}') filled", typeId, pic, phraseGroupId);
        }

        if (recordLinkType != FieldRecordLinkType.NO_LINK) {
            if (pic != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: pic filled ('{}') when auttyp is {} ({})", typeId, pic, recordLinkType.getId(), recordLinkType.name());
            }
            if (phraseGroupId != null) {
                log.warn("Invalid fdef[aut] configuration for field {}: fk_frazeskup filled ('{}') when auttyp is {} ({})", typeId, phraseGroupId, recordLinkType.getId(), recordLinkType.name());
            }
            return null;
        }

        if (pic != null) {
            if (pic.equals(DECIMAL_NUMBER_PIC_VALUE)) {
                return NUMBER_DECIMAL_2;
            }
            return Datatype.scalar(pic);
        }
        if (phraseGroupId != null) {
            return Datatype.scalar(DATATYPE_PREFIX_PHRASE + phraseGroupId);
        }
        return TEXT;
    }

}
