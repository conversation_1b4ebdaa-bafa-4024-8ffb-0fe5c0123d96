package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.detail.dflt.DefaultFieldValueCommandResolver;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.experimental.FieldDefaults;

import java.util.Collection;
import java.util.List;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultInitialValuesFillingRecordEditationWrapper implements RecordEditation {

    @Delegate @NonNull RecordEditation target;
    @NonNull RecordFieldEditor recordFieldEditor;
    @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver;
    @NonNull Department ctx;
    @NonNull UserAuthentication currentAuth;

    public DefaultInitialValuesFillingRecordEditationWrapper(@NonNull RecordEditation target, @NonNull RecordFieldEditor recordFieldEditor, @NonNull DefaultFieldValueCommandResolver defaultFieldValueCommandResolver, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        this.target = target;
        this.recordFieldEditor = recordFieldEditor;
        this.defaultFieldValueCommandResolver = defaultFieldValueCommandResolver;
        this.ctx = ctx;
        this.currentAuth = currentAuth;
        createFieldsWithInitialValues(target.getSubfieldTypes());
    }

    /// Fieldy se vytvori pouze pokud v ramci topfieldu je alespon jeden subfield autonomni (tzn. nevytvarime field pokud defaulty maji jen jeho indikatory)
    private void createFieldsWithInitialValues(List<? extends EditableFieldType> subfieldTypesOfTarget) {
        for (EditableFieldType fieldType : subfieldTypesOfTarget) {
            if (hasInitialAutonomousNested(fieldType)) {
                createFieldAndAllInitialSubfields(fieldType);
            }
        }
    }

    private void createFieldAndAllInitialSubfields(EditableFieldType fieldType) {
        FieldCreationCommand cmd = FieldCreationCommand.of(this.target.getRecord().idFondPair(), fieldType.getFieldTypeId().toFieldIdWithAllFirstIndices())
                .reuseExisting()
                .createMissingHierarchy()
                .initialValueCreating(ctx, currentAuth);
        recordFieldEditor.getOrCreateField(this.target, cmd);

        for (EditableFieldType subfieldType : fieldType.getSubfieldTypes()) {
            if (hasInitialNested(subfieldType)) {
                createFieldAndAllInitialSubfields(subfieldType);
            }
        }
    }

    private boolean isAnyInitialAutonomousNested(Collection<? extends EditableFieldType> subfieldTypes) {
        return subfieldTypes.stream().anyMatch(this::hasInitialAutonomousNested);
    }

    private boolean hasInitialAutonomousNested(EditableFieldType subfieldType) {
        if (!subfieldType.isAutonomous()) {
            return false;
        }
        if (hasInitial(subfieldType)) {
            return true;
        }
        return isAnyInitialAutonomousNested(subfieldType.getSubfieldTypes());
    }

    private boolean isAnyInitialNested(Collection<? extends EditableFieldType> subfieldTypes) {
        return subfieldTypes.stream().anyMatch(this::hasInitialNested);
    }

    private boolean hasInitialNested(EditableFieldType subfieldType) {
        if (hasInitial(subfieldType)) {
            return true;
        }
        return isAnyInitialNested(subfieldType.getSubfieldTypes());
    }

    private boolean hasInitial(EditableFieldType subfieldType) {
        return defaultFieldValueCommandResolver.hasDefault(subfieldType.getFieldTypeId(), target.getFond(), ctx);
    }

}
