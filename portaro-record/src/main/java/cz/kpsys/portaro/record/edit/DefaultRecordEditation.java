package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.FieldsNotFilledValidationException;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.By;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.value.FieldPayload;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.holding.RecordHoldingUpsertCommand;
import cz.kpsys.portaro.record.holding.RecordHoldingUpserter;
import cz.kpsys.portaro.record.load.RecordFieldsLoader;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

import static cz.kpsys.portaro.record.Record.TYPE_AUTHORITY;
import static cz.kpsys.portaro.record.Record.TYPE_DOCUMENT;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class DefaultRecordEditation extends BasicIdentified<String> implements RecordEditation {

    @NonNull RecordValidator recordValidator;
    @NonNull RecordSaver recordSaver;
    @NonNull List<Department> holdings;
    boolean machineEditation;
    @NonNull @NonFinal Record record;
    @NonNull EditedRecordDetail editedRecordDetail;
    @NonNull ContextualProvider<Department, RecordCatalogizationPhase> publishingDocumentCatalogizationPhaseProvider;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull RecordFieldsLoader recordFieldsLoader;

    @NonNull List<RecordEditationEvent> unsavedModifications = new ArrayList<>();
    @NonNull ReentrantLockLocker locker = new ReentrantLockLocker();

    DefaultRecordEditation(@NonNull Record record,
                           @NonNull Department ctx,
                           @NonNull List<Department> holdings,
                           boolean machineEditation,
                           @NonNull RecordFieldTypesLoader missingAddingRecordEditableFieldTypesLoader,
                           @NonNull RecordValidator recordValidator,
                           @NonNull RecordSaver recordSaver,
                           @NonNull FieldTypesByFondLoader unknownSupportingEditableTopfieldTypeLoader,
                           @NonNull ContextualProvider<Department, RecordCatalogizationPhase> publishingDocumentCatalogizationPhaseProvider,
                           @NonNull RecordHoldingUpserter recordHoldingUpserter,
                           @NonNull RecordFieldsLoader recordFieldsLoader) {
        super(UuidGenerator.forIdentifierShortcut());
        this.record = record;
        this.holdings = holdings;
        this.machineEditation = machineEditation;
        this.recordValidator = recordValidator;
        this.recordSaver = recordSaver;
        this.publishingDocumentCatalogizationPhaseProvider = publishingDocumentCatalogizationPhaseProvider;
        this.recordHoldingUpserter = recordHoldingUpserter;
        this.recordFieldsLoader = recordFieldsLoader;
        this.editedRecordDetail = new EditedRecordDetail(missingAddingRecordEditableFieldTypesLoader, unknownSupportingEditableTopfieldTypeLoader, record);

        if (!DataUtils.isPersistedId(record.getKindedId())) {
            unsavedModifications.add(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_DRAFT_CREATION));
        }
    }

    @Override
    public boolean isDraft() {
        return !record.isActive();
    }

    @Override
    public boolean isDeleted() {
        return record.isDeleted();
    }

    @Override
    public boolean isRevisionSaved() {
        return !RecordEditationEvent.containsAnyPersistableChange(unsavedModifications);
    }


    @Override
    public boolean isAuthority() {
        return record.getType().equals(TYPE_AUTHORITY);
    }


    @NonNull
    @Override
    public Fond getFond() {
        return record.getFond();
    }


    @NonNull
    @Override
    public Record getRecord() {
        return record;
    }

    @Override
    public @NonNull List<Field<?>> getFields() {
        return editedRecordDetail.getFields();
    }

    @Override
    public @NonNull Stream<Field<?>> streamFields() {
        return editedRecordDetail.streamFields();
    }

    @Deprecated
    @NonNull
    @Override
    public EditableFieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull FieldTypeId fieldTypeId) {
        return editedRecordDetail.getSubfieldTypeOrParentVirtualGroupTypeFor(fieldTypeId);
    }

    @NonNull
    @Override
    public EditableFieldType getSubfieldTypeOrParentVirtualGroupTypeFor(@NonNull String subfieldCode) {
        return editedRecordDetail.getSubfieldTypeOrParentVirtualGroupTypeFor(subfieldCode);
    }

    @NonNull
    @Override
    public EditableFieldType getSubfieldTypeFor(@NonNull FieldTypeId fieldTypeId) {
        return editedRecordDetail.getSubfieldTypeFor(fieldTypeId);
    }

    @NonNull
    @Override
    public List<? extends EditableFieldType> getSubfieldTypes() {
        return editedRecordDetail.getSubfieldTypes();
    }

    @Override
    public Field<?> createField(@NonNull EmptyFieldCreation command) {
        return editedRecordDetail.createField(command);
    }

    @Override
    public RecordEditation replaceAllFields(FieldContainer newFields) {
        editedRecordDetail.replaceAllFields(newFields);
        return this;
    }

    @Override
    public RecordEditation moveFields(FieldMovementCommand command) {
        new RecordFieldMover().moveField(getRecord().getDetail(), command);
        unsavedModifications.add(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.FIELD_MOVEMENT));
        return this;
    }

    @Override
    public RecordEditation changeFond(Fond fond) {
        if (editedRecordDetail.changeFond(fond)) {
            unsavedModifications.add(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_FOND_CHANGE));
        }
        return this;
    }

    @Override
    public RecordEditation changeCatalogizationPhase(RecordCatalogizationPhase phase) {
        RecordStatus newStatus = mapPhaseToStatus(phase);
        if (!record.getStatus().equals(newStatus)) {
            record.setStatus(newStatus);
            unsavedModifications.add(RecordEditationEvent.createRecordEvent(mapStatusToRecordEditationEventType(newStatus)));
        }
        return this;
    }

    @Override
    public void copyTopfield(Field<?> targetField, Field<?> sourceTopfield) {
        // TODO: this whole method should be refactored and maybe moved somewhere else
        targetField.setPayload(sourceTopfield.getPayload());

        sourceTopfield.streamFields()
                .forEach(sourceSubfield -> {
                    Optional<Field<?>> firstField = targetField.getFirstField(By.codeAndRepetition(sourceSubfield.getCode(), sourceSubfield.getRepetition()));
                    Field<?> subfield = firstField.isPresent()
                            ? firstField.get()
                            : targetField.createField(EmptyFieldCreation.toEnd(sourceSubfield.getType().getFieldTypeId()));

                    FieldPayload<?> sourcePayload = Objects.requireNonNull(sourceSubfield.getPayload(), () -> "Cannot copy source subfield to target subfield, because source subfield value is null (source field %s, target field %s)".formatted(sourceSubfield, subfield));
                    subfield.setPayload(sourcePayload);
                });

        unsavedModifications.add(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.FIELD_VALUE_EDITATION));
    }

    @Override
    public void notifyChange(RecordEditationEvent event) {
        unsavedModifications.add(event);
        log.debug("Received record editation event {}", event);
    }

    /**
     * Sets record status to published (concrete enum value depends on record kind) and saves record.
     */
    @Override
    public RecordEditation publish(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) throws IllegalStateException {
        if (!isDraft()) {
            throw new IllegalStateException(String.format("Cannot set record as published, record %s is not a draft (status is %s)", record, record.getStatus()));
        }
        refreshFields();
        validate();

        unsavedModifications.add(RecordEditationEvent.createRecordEvent(RecordEditationEvent.Type.RECORD_PUBLICATION));

        if (record.getType().equals(TYPE_DOCUMENT)) {
            changeCatalogizationPhase(publishingDocumentCatalogizationPhaseProvider.getOn(ctx));
        } else {
            changeCatalogizationPhase(RecordCatalogizationPhase.CATALOGED);
        }

        return saveIfModified(false, ctx, currentAuth);
    }


    @Override
    public RecordEditation saveIfModified(@NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        return saveIfModified(true, ctx, currentAuth);
    }

    private RecordEditation saveIfModified(boolean refreshFields, @NonNull Department ctx, @NonNull UserAuthentication currentAuth) {
        locker.lock(() -> {
            if (isRevisionSaved()) {
                log.info("Not saving record, there is no modification");
                return;
            }

            if (refreshFields) {
                refreshFields();
            }

            if (!isDraft()) {
                validate();
            }

            record = recordSaver.save(RecordSaveRequest.createStandard(record, ctx, List.copyOf(unsavedModifications), machineEditation));
            for (var holdingDepartment : holdings) {
                recordHoldingUpserter.upsert(new RecordHoldingUpsertCommand(record, holdingDepartment, false, ctx, currentAuth));
            }
            unsavedModifications.clear();
        });
        return this;
    }

    private void refreshFields() {
        locker.lock(() -> {
            FieldContainer refreshedFields = recordFieldsLoader.refresh(getRecord());
            replaceAllFields(refreshedFields);
        });
    }

    private void validate() {
        var validation = recordValidator.validate(record.getFond(), record.getDetail());
        validation.forEachFailure(failure -> {
            switch (failure.reason()) {
                case MISSING_ENTRY_FIELD -> throw new FieldsNotFilledValidationException(
                        "Entry field %s must be filled, but is empty.".formatted(failure.offendingField()),
                        Texts.ofNative(String.format("Vstupní prvek %s musí být vyplněný", failure.offendingField())));
                case MISSING_REQUIRED_FIELD -> throw new FieldsNotFilledValidationException(
                        "Required field %s must be filled, but is empty.".formatted(failure.offendingField()),
                        Texts.ofNative(String.format("Pole %s musí být vyplněné", failure.offendingField())));
                default -> throw new RecordValidator.FieldInvalidException(
                        "Invalid field %s because of error %s".formatted(failure.offendingField(), failure.reason()),
                        Texts.ofNative(String.format("Pole %s je chybné. Chyba %s", failure.offendingField(), failure.reason())));
            }
        });
    }

    private RecordStatus mapPhaseToStatus(RecordCatalogizationPhase phase) {
        return switch (phase) {
            case STUB -> RecordStatus.BACKWARD_CATALOGING;
            case CATALOGING -> RecordStatus.STANDARD_DOCUMENT;
            case CATALOGED -> {
                if (record.getType().equals(TYPE_AUTHORITY)) {
                    yield RecordStatus.NATIONAL;
                }
                if (record.getType().equals(TYPE_DOCUMENT)) {
                    yield RecordStatus.FINISHED_CATALOGING;
                }
                throw new IllegalStateException();
            }
        };
    }

    private RecordEditationEvent.Type mapStatusToRecordEditationEventType(RecordStatus status) {
        return switch (status) {
            case STANDARD_DOCUMENT -> RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_UNFINISHED_CATALOGING;
            case FINISHED_CATALOGING -> RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_FINISHED_CATALOGING;
            case SENT_TO_CASLIN -> RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_SENT;
            case ACCEPTED_BY_CASLIN -> RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_CASLIN_ACCEPTED;
            default -> RecordEditationEvent.Type.RECORD_STATUS_CHANGE_TO_OTHER;
        };
    }
}
