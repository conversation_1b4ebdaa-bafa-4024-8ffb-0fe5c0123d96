package cz.kpsys.portaro.record.detail.link;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.spec.RecordFieldId;
import cz.kpsys.portaro.record.detail.spec.RecordSpec;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.util.Objects;

public record LookupDefinition(

        @NonNull
        LinkFieldSpec linkFieldSpec,

        @Nullable
        Fond linkedRecordFond,

        @NonNull
        FieldTypeId linkedFieldTypeId

) {

    public static LookupDefinition ofSimpleLink(@NonNull FieldTypeId thisFieldTypeId, @NonNull Fond linkedRecordFond, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofLinkOwning(thisFieldTypeId), linkedR<PERSON><PERSON>Fond, linkedRecordFieldTypeId);
    }

    public static LookupDefinition forComplexSubfield(@NonNull FieldTypeId virtualGroupTypeId, @NonNull Fond linkedRecordFond, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofComplexLink(virtualGroupTypeId), linkedRecordFond, linkedRecordFieldTypeId);
    }

    public static LookupDefinition forVirtualGroupField(@NonNull FieldTypeId virtualGroupTypeId, @NonNull Fond linkedRecordFond, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofLinkOwning(virtualGroupTypeId), linkedRecordFond, linkedRecordFieldTypeId);
    }

    public static LookupDefinition ofSpecificFieldLink(@NonNull FieldTypeId linkFieldTypeId, @NonNull Fond linkedRecordFond, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofSpecificFieldLink(linkFieldTypeId), linkedRecordFond, linkedRecordFieldTypeId);
    }

    public static LookupDefinition ofSelfFieldRecord(@NonNull FieldTypeId thisFieldTypeId, @NonNull FieldTypeId linkedRecordFieldTypeId) {
        return new LookupDefinition(LinkFieldSpec.ofSelfFieldRecord(thisFieldTypeId), null, linkedRecordFieldTypeId);
    }

    public boolean isSelfRecordLink() {
        return linkFieldSpec.linkFieldSearchMode() == LinkFieldTargetSearchMode.SELF_RECORD;
    }

    public @NonNull FieldTypeId existingLinkedFieldTypeId() {
        return linkedFieldTypeId;
    }

    public @NonNull Fond existingLinkedRecordFond() {
        return Objects.requireNonNull(linkedRecordFond, () -> "This field link definition has no linked record fond. It has only link field spec %s".formatted(linkFieldSpec));
    }

    @NonNull
    public RecordFieldId toLinkedFieldSpecOnFirstFieldOfType(@NonNull RecordIdFondPair recordIdFondPair) {
        return RecordSpec.ofRecordAndField(recordIdFondPair, existingLinkedFieldTypeId().toFieldIdWithAllFirstIndices());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof LookupDefinition that)) {
            return false;
        }
        return linkFieldSpec.equals(that.linkFieldSpec) && Objects.equals(linkedFieldTypeId, that.linkedFieldTypeId);
    }

    @Override
    public int hashCode() {
        int result = linkFieldSpec.hashCode();
        result = 31 * result + Objects.hashCode(linkedFieldTypeId);
        return result;
    }

    @Override
    public String toString() {
        return "{" + linkFieldSpec + ":link}:" + linkedFieldTypeId;
    }
}
