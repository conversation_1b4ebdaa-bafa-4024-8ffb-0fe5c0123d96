package cz.kpsys.portaro.record.authority;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.concurrent.ReentrantLockLocker;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.LazyLoaded;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.IdentifiedFieldContainer;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.UUID;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BasicAuthorityWithDetailLazyLoaded extends BasicAuthority implements Authority, LazyLoaded {

    @NonNull AllByIdsLoadable<IdentifiedFieldContainer, Record> authorityDetailLoader;
    @NonNull ReentrantLockLocker locker = new ReentrantLockLocker();

    public BasicAuthorityWithDetailLazyLoaded(@NonNull RecordIdFondPair recordIdFondPair,
                                              @Nullable Integer kindedId,
                                              @NonNull UUID creationEventId,
                                              boolean external,
                                              boolean withForeignOccurrences,
                                              @NullableNotBlank String name,
                                              @NonNull AllByIdsLoadable<IdentifiedFieldContainer, Record> authorityDetailLoader) {
        super(recordIdFondPair, kindedId, creationEventId, external, withForeignOccurrences, name, null, ObjectProperties.empty());
        this.authorityDetailLoader = authorityDetailLoader;
    }

    public BasicAuthorityWithDetailLazyLoaded(@NonNull BasicAuthorityWithDetailLazyLoaded original, @NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        super(original, newRecordIdFondPair, kindedId);
        this.authorityDetailLoader = original.authorityDetailLoader;
    }

    @JsonIgnore
    @Override
    public FieldContainer getDetail() {
        locker.lock(() -> {
            if (!isLoaded()) {
                var detail = authorityDetailLoader.getAllByIds(List.of(this)).stream().findFirst().orElseThrow();
                setDetail(detail);
            }
        });
        return super.getDetail();
    }

    @Override
    public boolean isLoaded() {
        return super.getDetail() != null;
    }

    @JsonIgnore
    @Override
    public BasicAuthorityWithDetailLazyLoaded copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId) {
        ObjectUtil.assertCalledClassIsExactly(this, BasicAuthorityWithDetailLazyLoaded.class);
        return new BasicAuthorityWithDetailLazyLoaded(this, newRecordIdFondPair, kindedId);
    }

}
