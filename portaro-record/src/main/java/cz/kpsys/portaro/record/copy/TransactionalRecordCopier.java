package cz.kpsys.portaro.record.copy;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.ToByteArraySavingFileStreamConsumer;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.*;
import cz.kpsys.portaro.file.directory.BasicParentableDirectory;
import cz.kpsys.portaro.file.directory.DirectoryLoader;
import cz.kpsys.portaro.file.directory.ParentableDirectory;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldTypes;
import cz.kpsys.portaro.record.detail.SimpleFieldContainer;
import cz.kpsys.portaro.record.edit.FieldDeletionCommand;
import cz.kpsys.portaro.record.edit.RecordEditation;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordFieldEditor;
import cz.kpsys.portaro.record.file.RecordWithAttachmentSaveCommand;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.PageSearchLoader;
import cz.kpsys.portaro.search.RangePaging;
import cz.kpsys.portaro.search.StaticParamsModifier;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.Objects;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TransactionalRecordCopier {

    private static final int ARBITRARY_MAXIMAL_FILE_COUNT = 10; // Exception when copying too many files, over the limit

    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull DirectoryLoader directoryLoader;
    @NonNull PageSearchLoader<MapBackedParams, IdentifiedFile, RangePaging> fileSearchLoader;
    @NonNull FileDataStreamer fileDataStreamer;
    @NonNull Saver<RecordWithAttachmentSaveCommand, ?> recordWithAttachmentSaver;
    @NonNull TransactionTemplate transactionTemplate;
    @NonNull RecordFieldEditor recordFieldEditor;

    public @NonNull Record copyRecord(@NonNull RecordCopyCommand command) {
        return Objects.requireNonNull(transactionTemplate.execute(_ -> copyRecordImpl(command)));
    }

    private @NonNull Record copyRecordImpl(@NonNull RecordCopyCommand command) {
        Provider<List<IdentifiedFile>> srcFiles = Provider.of(
                () -> findRecordFiles(command.source())
        ).cached();

        // Checks before creating new objects
        if (command.copyFiles()) {
            ParentableDirectory srcDir = directoryLoader.getById(command.source().getDirectoryId());
            var childrenDirs = directoryLoader.getAllChildrenByParent(srcDir.getId());
            if (! childrenDirs.isEmpty()) {
                throw new IllegalArgumentException("Cannot copy record with subdirectories!");
            }

            if (srcFiles.get().size() > ARBITRARY_MAXIMAL_FILE_COUNT) {
                throw new IllegalArgumentException(String.format(
                        "Tried to copy record with %d files, but cannot copy more than %d files!",
                        srcFiles.get().size(), ARBITRARY_MAXIMAL_FILE_COUNT));
            }
        }

        FieldContainer detail = command.copyMetadata()
                ? command.source().getDetail()
                : new SimpleFieldContainer();

        RecordEditation editation = recordEditationFactory
                .on(command.ctx())
                .ofNew(command.targetFond(), detail)
                .build(command.currentAuth());
        FieldDeletionCommand deleteField1Command = FieldDeletionCommand.of(editation.getRecord(), FieldTypes.FIELD_001_FIELD_TYPE_ID.toFieldIdWithAllFirstIndices())
                .deleteAlsoEmptyHierarchy()
                .ignoreMissingHierarchy();
        recordFieldEditor.deleteField(editation, deleteField1Command);

        if (command.publishCopiedRecord()) {
            editation.publish(command.ctx(), command.currentAuth());
        } else {
            editation.saveIfModified(command.ctx(), command.currentAuth());
        }

        Record copiedRecord = editation.getRecord();

        if (command.copyFiles()) {
            IDdFile coverFile = command.source().getCover();
            copyFilesForRecord(copiedRecord, coverFile, srcFiles.get(), command.currentAuth(), command.ctx());
        }

        return copiedRecord;
    }

    /// Find all files in record's primary directory only. No recursion yet!
    private List<IdentifiedFile> findRecordFiles(Record srcRecord) {
        MapBackedParams params = MapBackedParams.build(StaticParamsModifier.of(
                FileSearchParams.DIRECTORY, List.of(srcRecord.getDirectoryId())
        ));
        return fileSearchLoader.getPage(RangePaging.forAll(), params).getItems();
    }

    private void copyFilesForRecord(Record targetRecord, IDdFile coverFile, List<IdentifiedFile> srcFiles, UserAuthentication currentAuth, Department ctx) {
        // Copy all files from directory
        for (var srcFile : srcFiles) {
            var newFileHeader = IdentifiedFileImpl.newFrom(srcFile);
            newFileHeader.setDirectory(BasicParentableDirectory.createOnlyWithId(targetRecord.getDirectoryId()));
            newFileHeader.setFileProcessingState(FileProcessingState.READY_TO_PROCESSING);

            ToByteArraySavingFileStreamConsumer byteSavingConsumer = new ToByteArraySavingFileStreamConsumer();
            fileDataStreamer.streamData(srcFile.getId(), null, byteSavingConsumer);

            // Ignoring setting preview and text data for now, saving only original data and let generate rest
            var newFile = new LoadedIdentifiedFileImpl(newFileHeader, byteSavingConsumer.getData());
            if (ObjectUtil.equals(srcFile, coverFile, IDdFile.class, IDdFile::getId)) {
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofCover(targetRecord, newFile, currentAuth, ctx));
            } else {
                recordWithAttachmentSaver.save(RecordWithAttachmentSaveCommand.ofFile(targetRecord, newFile, currentAuth, ctx));
            }
        }
    }

}
