package cz.kpsys.portaro.record.authority;

import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.StringUtil;
import cz.kpsys.portaro.database.RangePagingResultSetExtractor;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.sorting.Sorting;
import cz.kpsys.portaro.sql.generator.Brackets;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import static cz.kpsys.portaro.commons.db.QueryUtils.*;
import static cz.kpsys.portaro.databasestructure.RecordDb.*;
import static cz.kpsys.portaro.record.RecordKeyType.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SpringDbAuthoritySearchLoader extends AbstractSpringDbSearchLoader<MapBackedParams, Record, RangePaging> {

    @NonNull RowMapper<Record> authorityRowMapper;


    public SpringDbAuthoritySearchLoader(@NonNull NamedParameterJdbcOperations jdbcTemplate,
                                         @NonNull QueryFactory queryFactory,
                                         @NonNull RowMapper<Record> authorityRowMapper) {
        super(jdbcTemplate, queryFactory);
        this.authorityRowMapper = authorityRowMapper;
    }


    @Override
    protected void select(SelectQuery sq, MapBackedParams p, Sorting customSorting) {
        sq.selectDistinct(
                TC(RECORD.TABLE, RECORD.ID),
                TC(RECORD.TABLE, RECORD.MASTER_RECORD_ID),
                TC(RECORD.TABLE, RECORD.FOND_ID),
                TC(RECORD.TABLE, RECORD.RECORD_STATUS_ID),
                TC(RECORD.TABLE, RECORD.CREATION_EVENT_ID),
                TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID),
                TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID),
                TC(RECORD.TABLE, RECORD.PRIMARY_FILE_ID),
                TC(RECORD.TABLE, RECORD.DIRECTORY_ID),

                AS(TC(KATAUT_4.TABLE, KATAUT_4.NAZEV), COMMON_KINDED_NAME),
                TC(KATAUT_4.TABLE, KATAUT_4.ID_AUT)
        );
    }


    @Override
    protected void selectCount(SelectQuery sq, MapBackedParams p) {
        sq.selectCountDistinct(TC(RECORD.TABLE, RECORD.ID));
    }


    @Override
    protected ResultSetExtractor<Chunk<Record, RangePaging>> createResultSetExtractor(SelectQuery sq, MapBackedParams p, RangePaging paging) {
        return new RangePagingResultSetExtractor<>(authorityRowMapper, paging);
    }


    @Override
    protected boolean fillQuery(boolean count, SelectQuery sq, MapBackedParams p, Sorting customSorting) {
        sq.from(RECORD.TABLE);
        sq.joins().add(KATAUT_4.TABLE, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KATAUT_4.TABLE, KATAUT_4.RECORD_ID)));


        if (p.has(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND)) {
                return false;
            }
            sq.joins().add(KAT1_7.KAT1_7, COLSEQ(TC(RECORD.TABLE, RECORD.ID), TC(KAT1_7.KAT1_7, KAT1_7.TARGET_RECORD_ID)))
                    .add(AS(RECORD.TABLE, "related_record"), new Brackets(sq).eqRaw(TC(KAT1_7.KAT1_7, KAT1_7.SOURCE_RECORD_ID), TC("related_record", RECORD.ID))
                            .and().in(TC("related_record", RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.RECORD_RELATED_RECORD_FOND))).toString()); //musime vyfiltrovat ty autority, ktere jsou pouzite jen v povolenych fondech dokumentu
        }

        if (!p.has(CoreSearchParams.INCLUDE_DRAFT) || !p.get(CoreSearchParams.INCLUDE_DRAFT)) {
            sq.where().and().isNotNull(TC(RECORD.TABLE, RECORD.ACTIVATION_EVENT_ID));
        }

        if (!p.has(CoreSearchParams.INCLUDE_DELETED) || !p.get(CoreSearchParams.INCLUDE_DELETED)) {
            sq.where().and().isNull(withoutIndexString(TC(RECORD.TABLE, RECORD.DELETION_EVENT_ID)));
        }

        if (p.has(RecordConstants.SearchParams.FOND)) {
            if (!p.hasLength(RecordConstants.SearchParams.FOND)) {
                return false;
            }
            sq.where().and().in(TC(RECORD.TABLE, RECORD.FOND_ID), ListUtil.getListOfIds(p.get(RecordConstants.SearchParams.FOND)));
        }

        if (p.has(RecordConstants.SearchParams.PREFIX)) {
            if (p.get(RecordConstants.SearchParams.PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }
            String prefixTable = "prefix";
            String uppercasedFlattenPrefix = StringUtil.flatString(p.get(RecordConstants.SearchParams.PREFIX)).toUpperCase();
            Brackets brackets = new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(prefixTable, RECORD_KEY.RECORD_ID));
            Brackets bracketsInner = brackets.and().brackets();
            bracketsInner
                    .eq(TC(prefixTable, RECORD_KEY.NAME), FLAT_PRIMARY_NAME.getId()).and().like(TC(prefixTable, RECORD_KEY.VAL), uppercasedFlattenPrefix, false, true, Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH)
                    .or()
                    .eq(TC(prefixTable, RECORD_KEY.NAME), FLAT_ALTERNATIVE_NAME.getId()).and().like(TC(prefixTable, RECORD_KEY.VAL), uppercasedFlattenPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH);
            sq.joins().add(AS(RECORD_KEY.TABLE, prefixTable), brackets.toString());
        }

        if (p.has(RecordConstants.SearchParams.DIACRITICAL_PREFIX)) {
            if (p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).length() > Record.RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH) {
                return false;
            }

            String diacriticalPrefixTable = "diaPrefix";
            String uppercasedPrefix = p.get(RecordConstants.SearchParams.DIACRITICAL_PREFIX).toUpperCase();
            Brackets brackets = new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(diacriticalPrefixTable, RECORD_KEY.RECORD_ID));
            Brackets bracketsInner = brackets.and().brackets();
            bracketsInner
                    .eq(TC(diacriticalPrefixTable, RECORD_KEY.NAME), ORIG_PRIMARY_NAME.getId()).and().like(upper(TC(diacriticalPrefixTable, RECORD_KEY.VAL)), uppercasedPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH)
                    .or()
                    .eq(TC(diacriticalPrefixTable, RECORD_KEY.NAME), ORIG_ALTERNATIVE_NAME.getId()).and().like(upper(TC(diacriticalPrefixTable, RECORD_KEY.VAL)), uppercasedPrefix, false, true, Record.RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH);
            sq.joins().add(AS(RECORD_KEY.TABLE, diacriticalPrefixTable), brackets.toString());
        }

        if (p.has(RecordConstants.SearchParams.ICO)) {
            if (p.get(RecordConstants.SearchParams.ICO).isBlank()) {
                return false;
            }
            String icoTable = "ico";
            sq.joins().add(AS(RECORD_KEY.TABLE, icoTable), new Brackets(sq).eqRaw(TC(RECORD.TABLE, RECORD.ID), TC(icoTable, RECORD_KEY.RECORD_ID))
                    .and().eq(TC(icoTable, RECORD_KEY.NAME), CNA_OR_ICO.getId())
                    .and().like(TC(icoTable, RECORD_KEY.VAL), p.get(RecordConstants.SearchParams.ICO), false, true, Record.RECORD_KEY_VAL_MAX_LENGTH).toString());
        }

        if (!count) {
            sq.orderBy().addAsc(TC(RECORD.TABLE, RECORD.SORTING_KEY));
        }

        return true;
    }

}
