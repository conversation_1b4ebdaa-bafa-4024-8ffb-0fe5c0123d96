package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.Map.Entry;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbEditableFieldTypesByFondLoader implements FieldTypesByFondLoader, CacheCleaner {

    @NonNull HierarchicalFieldTypeDtoByFondLoader hierarchicalFieldTypeDtoByFondLoader;
    @NonNull FieldTypeLoader fieldTypeLoader;
    @NonNull UnknownEditableFieldTypeConverter unknownEditableFieldTypeConverter;
    @NonNull Map<Fond, List<EditableFieldType>> map = new ConcurrentHashMap<>();


    @Override
    public List<EditableFieldType> getTopfieldTypesByFond(@NonNull Fond fond) {
        List<EditableFieldType> fieldTypesByFond = map.computeIfAbsent(fond, this::loadStyl);
        log.debug("Loaded field types (style) for fond {}: {}", fond, fieldTypesByFond);
        return fieldTypesByFond;
    }

    @Override
    public Optional<EditableFieldType> findTopfieldTypeByFondAndId(@NonNull Fond fond, @NonNull FieldTypeId topfieldId, @NonNull WhenMissing whenMissing) {
        Assert.isTrue(topfieldId.getLevel() == FieldTypeId.LEVEL_TOPFIELD, () -> "FieldTypeId " + topfieldId + " is not topfield!");

        Optional<EditableFieldType> actual = ListUtil.findById(getTopfieldTypesByFond(fond), topfieldId.toString());

        if (actual.isEmpty() && whenMissing == WhenMissing.CREATE_UNKNOWN) {
            actual = Optional.of(unknownEditableFieldTypeConverter.convertToEditable(fieldTypeLoader.getTopfieldTypeById(topfieldId), fond));
        }

        actual = throwWhenMode(fond, topfieldId, whenMissing, actual);

        return actual;
    }

    private List<EditableFieldType> loadStyl(@NonNull Fond fond) {
        Map<FieldTypeId, FieldSettings> settings = hierarchicalFieldTypeDtoByFondLoader.hierarchicallyLoadSettings(fond);

        Map<FieldTypeId, FieldSettings> typeIdToEditableSubfieldSettingMap = settings.entrySet().stream()
                .filter(dto -> dto.getKey().getLevel() != FieldTypeId.LEVEL_TOPFIELD)
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
        SubfieldTypeToEditableConverter editableSubfieldTypeConverter = new SubfieldTypeToEditableConverter(fond, typeIdToEditableSubfieldSettingMap);

        return settings.entrySet().stream()
                .filter(dto -> dto.getKey().getLevel() == FieldTypeId.LEVEL_TOPFIELD)
                .sorted(Entry.comparingByKey(FieldTypeId.NUMERICALLY_COMPATIBLE_SORTER))
                .map(entry -> mapTopfieldType(entry.getKey(), entry.getValue(), fond, editableSubfieldTypeConverter))
                .toList();
    }


    private EditableFieldType mapTopfieldType(FieldTypeId originalFieldTypeId, FieldSettings settings, @NonNull Fond fond, SubfieldTypeToEditableConverter editableSubfieldTypeConverter) {
        FieldType originalFieldType = fieldTypeLoader.getTopfieldTypeById(originalFieldTypeId);
        return new EditableFieldType(originalFieldType, fond, settings, editableSubfieldTypeConverter);
    }


    @Override
    public void clearCache() {
        map.clear();
    }

}
