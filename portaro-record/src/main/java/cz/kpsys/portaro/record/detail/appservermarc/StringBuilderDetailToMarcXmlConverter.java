package cz.kpsys.portaro.record.detail.appservermarc;

import cz.kpsys.portaro.bool.BooleanToBoolStringConverter;
import cz.kpsys.portaro.commons.date.InstantToStringConverter;
import cz.kpsys.portaro.commons.date.LocalDateToStringConverter;
import cz.kpsys.portaro.commons.object.BasicIdentified;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringEscapeUtil;
import cz.kpsys.portaro.marcxml.MarcConstants;
import cz.kpsys.portaro.number.BigDecimalToStringConverter;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.RecordIdentifier;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.*;
import cz.kpsys.portaro.record.detail.convert.DetailToMarcXmlConverter;
import cz.kpsys.portaro.record.detail.value.*;
import cz.kpsys.portaro.record.fond.Fond;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class StringBuilderDetailToMarcXmlConverter implements DetailToMarcXmlConverter {

    @NonNull BooleanToBoolStringConverter booleanToMarcXmlSubfieldStringConverter = new BooleanToBoolStringConverter();
    @NonNull BigDecimalToStringConverter numberToMarcXmlSubfieldStringConverter = new BigDecimalToStringConverter();
    @NonNull LocalDateToStringConverter localDateToMarcXmlSubfieldStringConverter = new LocalDateToStringConverter(MarcConstants.MARCXML_LOCAL_DATE_FORMATTER);
    @NonNull InstantToStringConverter instantToMarcXmlSubfieldStringConverter;

    @Override
    public String convert(boolean forAuthority, @NonNull FieldContainer detail, Fond fond, @Nullable Integer recordKindedId, @NonNull UUID recordId, boolean active, RecordStatus status, @Nullable Fond sourceRecordAuthorityFond) {
        if (status == null) {
            log.error("Record status is null while saving record {}", recordId);
        }

        Map<String, Object> params = new HashMap<>();
        params.put(AppserverMarcXmlDeserializer.RECORD_ID, recordId);
        if (recordKindedId != null) {
            params.put(AppserverMarcXmlDeserializer.ID, recordKindedId);
        }
        if (fond != null) {
            params.put(AppserverMarcXmlDeserializer.FOND, fond.getId());
        }
        if (sourceRecordAuthorityFond != null) {
            params.put("zdroj_dok", sourceRecordAuthorityFond.getId());
        }
        params.put("active", active);
        if (status != null) {
            params.put("status", forAuthority ? status.toLegacyAuthorityStatusId() : status.getId());
            params.put("record_status", status.getId());
        }

        //budto je to kontrolni pole s hodnotou, nebo datove pole s alespon jednim neprazdnym podpolem
        String value = detail.streamFields()
                .filter(Field::hasValue)
                .filter(By.anyCode(FieldTypes.FOND_FIELD_CODE, FieldTypes.TOC_FIELD_CODE, FieldTypes.USER_FIELD_CODE).negate())
                .map(this::appendField)
                .filter(Objects::nonNull)
                .collect(Collectors.joining());

        return createXmlElement(AppserverMarcXmlDeserializer.RECORD, params, value);
    }

    @Nullable
    private String appendField(Field<?> field) {
        if (By.controlfield().test(field)) {
            // CONTROLFIELD
            if (field.getCode().equals(FieldTypes.AUTHORITY_LEADER_FIELD_CODE) || field.getCode().equals(FieldTypes.DOCUMENT_LEADER_FIELD_CODE)) {
                return createXmlElement(AppserverMarcXmlDeserializer.LEADER, Map.of(), escape(field.getRaw()));
            }
            return createXmlElement(AppserverMarcXmlDeserializer.CONTROLFIELD, Map.of(AppserverMarcXmlDeserializer.NUMBER, FieldHelper.fieldCodeToMarc21Tag(field.getCode())), escape(field.getRaw()));
        }

        // DATAFIELD
        Map<String, Object> params = Map.of(
                AppserverMarcXmlDeserializer.NUMBER, FieldHelper.fieldCodeToMarc21Tag(field.getCode()),
                AppserverMarcXmlDeserializer.IND1, toMarcXmlDatafieldIndicator(field, FieldTypes.IND_1_FIELD_CODE),
                AppserverMarcXmlDeserializer.IND2, toMarcXmlDatafieldIndicator(field, FieldTypes.IND_2_FIELD_CODE)
        );
        List<SubfieldAppserverMarcResponse> appserverMarcResponses = field.streamFields()
                .filter(By.anyCode(FieldTypes.INDICATORS_FIELD_CODES).negate())
                .flatMap(this::mapSubfieldToXmlObject)
                .toList();
        if (appserverMarcResponses.isEmpty()) {
            return null;
        }
        String value = appserverMarcResponses.stream()
                .map(this::convertMarcSubfieldToXmlString)
                .collect(Collectors.joining());
        return createXmlElement(AppserverMarcXmlDeserializer.DATAFIELD, params, value);
    }

    private Stream<SubfieldAppserverMarcResponse> mapSubfieldToXmlObject(Field<?> sf) {
        if (!sf.hasValue()) {
            return Stream.empty();
        }

        if (sf.getType().isVirtualGroup()) {
            return sf.streamFields().flatMap(this::mapSubfieldToXmlObject);
        }

        String value = switch (sf.getValueHolder()) {
            case BooleanFieldValue val -> booleanToMarcXmlSubfieldStringConverter.convert(val.value());
            case NumberFieldValue val -> numberToMarcXmlSubfieldStringConverter.convert(val.value());
            case LocalDateFieldValue val -> localDateToMarcXmlSubfieldStringConverter.convert(val.value());
            case InstantFieldValue val -> instantToMarcXmlSubfieldStringConverter.convert(val.value());
            case AcceptableValueFieldValue<?> val -> String.valueOf(val.value().getId());
            case StringFieldValue val -> val.value();
            case null -> throw new UnsupportedOperationException(String.format("Subfield (%s) value holder is null", sf.getFieldId()));
        };
        RecordIdFondPair recordLink = sf.hasRecordLink() ? sf.getExistingRecordLink() : null;
        UUID recordId = ObjectUtil.elvis(recordLink, RecordIdFondPair::id, RecordIdentifier::id);
        Integer fondId = ObjectUtil.elvis(recordLink, RecordIdFondPair::fond, BasicIdentified::getId);
        return Stream.of(new SubfieldAppserverMarcResponse(sf.getCode(), value, recordId, fondId));
    }

    private String convertMarcSubfieldToXmlString(SubfieldAppserverMarcResponse marcSubfield) {
        Map<String, Object> params = Map.of("code", marcSubfield.code());
        if (marcSubfield.recordId() != null) {
            params = ListUtil.createNewMapAppending(params, AppserverMarcXmlDeserializer.SUBFIELD_RECORD_ID, marcSubfield.recordId());
        }
        return createXmlElement(AppserverMarcXmlDeserializer.SUBFIELD, params, escape(marcSubfield.value()));
    }

    private static String createXmlElement(String element, Map<String, Object> params, String val) {
        StringBuilder sb = new StringBuilder();
        sb.append("<").append(element);
        for (Map.Entry<String, Object> param : params.entrySet()) {
            sb.append(" ").append(param.getKey()).append("=\"").append(param.getValue()).append("\"");
        }
        sb.append(">");
        sb.append(val);
        sb.append("</").append(element).append(">");
        return sb.toString();
    }

    private static String escape(String s) {
        return StringEscapeUtil.escapeXml10(s);
    }

    private static String toMarcXmlDatafieldIndicator(@NonNull Field<?> parentField, @NonNull String fieldCode) {
        Field<?> indField = parentField.getFirstField(By.code(fieldCode)).orElse(null);
        return escape(IndicatorType.toMarcXmlIndicatorValue(indField, true));
    }

}
