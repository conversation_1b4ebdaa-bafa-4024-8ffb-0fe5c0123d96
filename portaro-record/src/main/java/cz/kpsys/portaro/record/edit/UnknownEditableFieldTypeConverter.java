package cz.kpsys.portaro.record.edit;

import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.fond.Fond;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UnknownEditableFieldTypeConverter {

    public @NonNull EditableFieldType convertToEditable(@NonNull FieldType topfieldType, @NonNull Fond fond) {
        return new EditableFieldType(topfieldType, fond, FieldSettings.unknown(), SubfieldTypeToEditableConverter.createEmpty(fond));
    }

}
