package cz.kpsys.portaro.record;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.commons.validation.nullablenotblank.NullableNotBlank;
import cz.kpsys.portaro.id.IDdFile;
import cz.kpsys.portaro.id.UuidGenerator;
import cz.kpsys.portaro.prop.ObjectProperties;
import cz.kpsys.portaro.record.authority.BasicAuthority;
import cz.kpsys.portaro.record.detail.Field;
import cz.kpsys.portaro.record.detail.FieldContainer;
import cz.kpsys.portaro.record.detail.FieldStreamSource;
import cz.kpsys.portaro.record.detail.RecordFieldFinder;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.document.BasicDocument;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.query.ValFieldGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.NonNull;
import org.jspecify.annotations.Nullable;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Supplier;

@Schema(
        description = "Record specification",
        type = "string",
        format = "uuid",
        example = Record.SCHEMA_EXAMPLE_DOCUMENT_ID
)
public interface Record extends Serializable, RecordDescriptor, FieldStreamSource<Field<?>> {

    String TYPE_DOCUMENT = "document";
    String TYPE_AUTHORITY = "authority";

    int RECORD_KEY_FLAT_PRIMARY_NAME_VAL_MAX_LENGTH = 100;
    int RECORD_KEY_FLAT_ALTERNATIVE_NAME_VAL_MAX_LENGTH = 100;
    int RECORD_KEY_VAL_MAX_LENGTH = 250;

    String SCHEMA_EXAMPLE_DOCUMENT_ID = "bbc957aa-8a27-4b05-91f6-ce20634b458b";


    /**
     * Vytvori novou autoritu z patternu (zkopiruje vse krome id). Napriklad pro vytvoreni nove autority ze z-authority
     */
    static Record createNewByPattern(@NonNull Record pattern) {
        Assert.isTrue(pattern.getFond().isOfAuthority(), () -> "Pattern record fond " + pattern + " must be of authority");
        RecordIdFondPair recordIdFondPair = RecordIdFondPair.of(UuidGenerator.forIdentifier(), pattern.getFond());
        return new BasicAuthority(recordIdFondPair, null, null, false, false, pattern.getName(), pattern.getDetail(), ObjectProperties.empty());
    }

    static Record createDraft(@NonNull RecordIdFondPair recordIdFondPair, boolean external, @NonNull FieldContainer detail) {
        BasicRecord record = FondTypeResolver.isDocumentFond(recordIdFondPair.fond())
                ? new BasicDocument(recordIdFondPair, null, null, external, false, null, detail, ObjectProperties.empty())
                : new BasicAuthority(recordIdFondPair, null, null, external, false, null, detail, ObjectProperties.empty());
        record.setStatus(RecordStatus.DRAFT);
        return record;
    }


    @Override
    UUID getId();

    void setKindedId(Integer id);

    @Nullable
    Integer getKindedId();

    @Override
    @NullableNotBlank
    String getName();

    @JsonIgnore
    @NonNull
    RecordIdFondPair idFondPair();

    @JsonIgnore
    default boolean isMerged() {
        return idFondPair().id().masterId() != null;
    }

    @NonNull
    String getType();

    void setName(String name);

    /**
     * Deprecated - use RecordViewFunctions (e.g. RecordViewFunctions.normalizedIsbns). Use ObjectProperties only for frontend
     */
    @Deprecated
    ObjectProperties getProps();

    FieldContainer getDetail();

    void setDetail(FieldContainer detail);

    @NonNull
    Fond getFond();

    void setFond(Fond fond);

    RecordStatus getStatus();

    void setStatus(RecordStatus status);

    boolean isExternal();

    boolean isValid();

    void setValid(boolean valid);

    Integer getDirectoryId();

    void setDirectoryId(Integer directoryId);

    IDdFile getCover();

    void setCover(IDdFile cover);

    @JsonIgnore
    @Nullable UUID getCreationEventId();

    void setCreationEventId(@NonNull UUID creationEventId);

    @JsonIgnore
    @Nullable UUID getActivationEventId();

    void setActivationEventId(@Nullable UUID activationEventId);

    @JsonIgnore
    @Nullable UUID getDeletionEventId();

    void setDeletionEventId(@Nullable UUID deletionEventId);


    List<Field<?>> getFields();

    /**
     * Vrati vsechna opakovani poli daneho cisla.
     */
    List<Field<?>> getFields(String fieldNumber);

    ValFieldGroup query(String q);

    @JsonIgnore
    Record copy(@NonNull RecordIdFondPair newRecordIdFondPair, @Nullable Integer kindedId);

    default boolean isActive() {
        return getActivationEventId() != null;
    }

    default boolean isDeleted() {
        return getDeletionEventId() != null;
    }

    default boolean isWithForeignOccurrences() {
        return false;
    }

    /**
     * Vrati, zda ma dokument (jen nebo i) elektronickou podobu.
     */
    default boolean isDownloadable() {
        return false;
    }

    default boolean isPeriodical() {
        return getFond().isPeriodical();
    }

    default boolean isVolumeable() {
        return isPeriodical();
    }

    /**
     * Zda ma dokument nejake exemplare (nebo svazky u periodik).
     */
    default boolean isExemplarable() {
        return getFond().isWithExemplars();
    }

    /**
     * Zda ma dokument nejake clanky. <br/>
     * Clanky to jsou asi jen u periodik, u monografii to jsou treba recenze. <br/>
     * Bacha na to ze clanky nemaj nic spolecnyho s exemplarema!
     * Deprecated - migrate to recordProperties
     */
    @Deprecated
    default boolean canHaveArticles() {
        return FondTypeResolver.isDocumentFond(getFond());
    }

    /**
     * Zda je dokument cast nejakeho vicedilneho dila.
     * Deprecated - migrate to recordProperties
     */
    @Deprecated
    default boolean canHaveParts() {
        return FondTypeResolver.isDocumentFond(getFond());
    }

    default <T extends ScalarFieldValue<?>, R> Optional<R> findFirst(@NonNull RecordFieldFinder<T, R> finder) {
        return finder.findFirstIn(this);
    }

    default <T extends ScalarFieldValue<?>, R> R getFirst(@NonNull RecordFieldFinder<T, R> finder) {
        return finder.getFirstIn(this);
    }

    static <R extends Record> R getByRecordId(@NonNull Collection<R> records, @NonNull UUID id, boolean tryFindInMasterRecordId, @NonNull Supplier<String> notFoundErrorMessageSupplier) {
        Optional<R> idMatching = ListUtil.findFirstMatching(records, r -> r.getId().equals(id));
        if (idMatching.isPresent()) {
            return idMatching.get();
        }
        if (tryFindInMasterRecordId) {
            Optional<R> masterIdMatching = ListUtil.findFirstMatching(records, r -> id.equals(r.idFondPair().id().masterId()));
            if (masterIdMatching.isPresent()) {
                return masterIdMatching.get();
            }
        }
        String notFoundErrorMessage = notFoundErrorMessageSupplier.get();
        throw new ItemNotFoundException(Record.class, "record uuid %s".formatted(id), notFoundErrorMessage, Texts.ofNative(notFoundErrorMessage));
    }

    static List<UUID> getListOfUuids(@NonNull Collection<? extends Record> records) {
        return ListUtil.convert(records, Record::getId);
    }
    
}
