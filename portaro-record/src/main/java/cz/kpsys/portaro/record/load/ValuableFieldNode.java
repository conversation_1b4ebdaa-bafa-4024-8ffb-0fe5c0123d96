package cz.kpsys.portaro.record.load;

import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.link.FieldGeneration;
import cz.kpsys.portaro.record.detail.spec.RecordDatableId;
import cz.kpsys.portaro.record.detail.value.AbsentDataResult;
import cz.kpsys.portaro.record.detail.value.FailedResult;
import cz.kpsys.portaro.record.detail.value.ScalarFieldValue;
import cz.kpsys.portaro.record.detail.value.VectorFieldValue;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import lombok.NonNull;

public interface ValuableFieldNode {

    @NonNull RecordIdFondPair record();

    @NonNull RecordDatableId id();

    @NonNull
    EditableFieldType fieldType();

    default boolean isValueOrLinkGenerationCapable() {
        if (this instanceof Multifield) {
            return fieldType().isWholeFieldGenerated();
        }
        return true;
    }

    boolean hasRecordLink();

    @NonNull RecordIdFondPair getExistingRecordLink();

    void setGeneratedRecordLink(@NonNull RecordIdFondPair recordLink);


    boolean hasVectorValue();

    @NonNull
    VectorFieldValue<?> existingVectorValue();

    void setGeneratedVectorValue(@NonNull VectorFieldValue<?> value);


    boolean hasScalarValue();

    @NonNull
    ScalarFieldValue<?> existingScalarValue();

    void setGeneratedScalarValue(@NonNull ScalarFieldValue<?> value);


    boolean hasError();

    @NonNull FailedResult getExistingError();

    default boolean isNonAbsentingError() {
        return hasError() && !(getExistingError() instanceof AbsentDataResult);
    }

    void setGeneratedError(@NonNull FailedResult failedResult);



    default boolean isBlankFieldGenerationCapable() {
        if (this instanceof Multifield) {
            return fieldType().getFieldGeneration() == FieldGeneration.GENERATED_FIELD_PARENT;
        }
        return false;
    }

    default boolean hasGeneratedBlankField() {
        return isBlankFieldGenerationCapable();
    }

    default void setGeneratedNoValueField() {
        throw new IllegalStateException("Cannot set generated blank field to field " + id() + ", field slot is never blank-subfield-generatable");
    }

}
