FROM postgres:17
RUN localedef -i cs_CZ -c -f UTF-8 -A /usr/share/locale/locale.alias cs_CZ.UTF-8

ENV LANG cs_CZ.UTF-8
ENV TZ Europe/Prague
ENV PGDATA /data/v17

RUN apt-get update && \
    apt-get install -y dos2unix

COPY init.sql /docker-entrypoint-initdb.d/docker_postgres_init.sql

RUN dos2unix /docker-entrypoint-initdb.d/docker_postgres_init.sql && \
    apt-get --purge remove -y dos2unix && \
    rm -rf /var/lib/apt/lists/*

CMD ["postgres", \
    "-c", "max_connections=512", \
    "-c", "shared_buffers=256MB", \
    "-c", "wal_buffers=64MB", \
    "-c", "work_mem=4MB", \
    "-c", "effective_io_concurrency=200", \
    "-c", "random_page_cost=1.1"]