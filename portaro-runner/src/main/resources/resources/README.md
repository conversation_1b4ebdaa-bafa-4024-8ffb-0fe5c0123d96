
# Portaro Front End

## Build Scripts

We use ts-node, so we can use typescript to run ts scripts instead of js directly on node.js.

Main build tool is webpack with custom configuration. Config is written in typescript. 

For testing we use Karma test runner + jasmine test framework.

### BUILD

- `build` Development build. No minification, no optimizations.
- `build:prod` Production build. Before build, it executes es-lint, svelte-check and unit tests on local chromium. Minification enabled for JS and CSS.
- `build:prod-without-tests` Production build without test, lint and svelte-check. Used only for debugging production build bundles.
- `build:develWithGradle` Development build with watching of changes. If some file is modified, build is triggered and after then, gradle triggers task `processResources` without dependencies.
- `build:prodWithGradle` Production build with watching of changes. Minification is turned on and source maps are full. If some file is modified, build is triggered and after then, gradle triggers task `processResources` without dependencies.
- `profile` Development build with profile report, which is generated into file `stats.json`. Use [Webpack Analyzer](https://webpack.github.io/analyse/) or [Statoscope](https://statoscope.tech/) to analyze bundle.
- `profile:prod` Production build with profile report.
	
### DOCUMENTATION (WIP)

- `storybook` Run storybook on [localhost:6006](localhost:6006). Storybook is interactive catalogue of svelte components. It contains live examples and documentation.
- ~~`docs:build`~~ Build documentation to `/docs` directory.
- ~~`docs:serve`~~ Run server with documentation on [localhost:3002](localhost:3002).
- ~~`docs:watch`~~
- ~~`docs:examples-dist`~~
		
### TESTING

All tests generate test coverage report to folder `/reports/coverage`.

- `test` Run unit tests - defaults to `test:chromium`
- `test:chromium` Run unit test on local chromium browser.
- `test:firefox` Run unit test on local firefox browser.

### LINT and CHECKING
  
- `svelte-check` Run svelte-check on svelte components - displays errors and warnings 
- `svelte-check:watch` Run svelte-check in watch mode - checks code after every modification
- `svelte-check:hints` Run svelte-check - displays errors, warnings and hints
- `lint` Run ESLint check - displays errors and warnings
- `lint:fix` Run ESLint check and fix fixable errors and warnings
- ~~`quality`~~ Generates code quality report to folder `/reports/quality`.

### INTERNAL

- `gradle` Internal task for execute gradle processResources task. Gradle has to be installed. Works on unix OS.
- `gradle-win` Internal task for execute gradle processResources task. Gradle has to be installed. Works on Windows OS.
- `build-storybook` Internal task for storybook


## Structure

`portaro-runner/src/main/resources` folder
- `freemarker` freemarker templates
- `velocity` velocity templates
- `resources` frontend project root

### Project root (`portaro-runner/src/main/resources/resources`)
- `storybook` storybooks config
- `dist` generated build output - js, css, other assets 
- `fonts` global fonts
- `img` global images folder, images used on multiple places
- `node_modules` npm libraries
- `reports` coverage reports generated by unit tests ~~& code quality reports~~
- `src` 
  application code
  - `core` core module
  - `features` feature modules
  - `modals` modal windows modules
  - `shared` shared modules - common utils and components
  - `svelte-playground` playground for quick testing and prototyping
  - `test-utils` utilities for unit testing
  - `typings` type definition files
  - `app.ts` main entrypoint
  - `portaro.ts` root module
- `styles` global stylesheets, mixins and variables
- `test` test config
- `webpack` webpack config, build configs, custom loaders and plugins

## Other

[General frontend conventions & Angular code style](frontend-conventions.md)

[Localizations 101](localizations.md)

[Svelte Code Style & Svelte Migration Guide](svelte-style-guide.md)

[Custom files and INI templates](custom-files-migration.md)