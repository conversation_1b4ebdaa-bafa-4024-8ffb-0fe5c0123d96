<script lang="ts">
    import type {FilesSelectionModalModel} from './types';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {FilesSelectionModalService} from './files-selection-modal.service';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {byteFormatter, loc} from 'shared/utils/pipes';
    import {createPayloadActionResponse} from '../modal-utils';
    import {viewableFileContainsViewForm} from '../../features/media-viewer/lib/mv-utils';
    import {FileViewForm} from 'shared/constants/portaro.constants';
    import {exists} from 'shared/utils/custom-utils';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';

    export let model: FilesSelectionModalModel;
    export let modalWindowActions: ModalWindowActions;

    const service = getInjector().getByToken<FilesSelectionModalService>(FilesSelectionModalService.serviceName);
    const localize = getLocalization();

    let loading = true;
    let files: ViewableFile[] = [];
    let selectedFiles: ViewableFile[] = [];

    onMount(async () => {
        try {
            files = await service.getAllFiles(model.parentDirectories, model.fileViewForms);
        } finally {
            loading = false;
        }
    });

    const handleFileClick = (file: ViewableFile) => {
        if (!model.multiselect) {
            modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse([file])));
            return;
        }

        if (selectedFiles.includes(file)) {
            selectedFiles = selectedFiles.filter((f) => f.id !== file.id);
            return;
        }

        selectedFiles = [...selectedFiles, file];
    }

    const handleSelectFilesClick = () => {
        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(selectedFiles)));
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="files-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {model.multiselect
                ? localize(/* @kp-localization file.ChooseFiles */ 'file.ChooseFiles')
                : localize(/* @kp-localization file.ChooseFile */ 'file.ChooseFile')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if loading}
            <KpLoadingBlock/>
        {:else}
            <ul class="files-result-list">
                {#each files as file(file.id)}
                    {@const fileSelected = selectedFiles.includes(file)}

                    <li>
                        <button class="file-card-button"
                                on:click={() => handleFileClick(file)}
                                class:file-selected={fileSelected}>

                            {#if model.multiselect}
                                <input class="file-select-checkbox"
                                       type="checkbox"
                                       checked="{fileSelected}"/>
                            {/if}

                            {#if viewableFileContainsViewForm(file, FileViewForm.IMAGE)}
                                <img class="thumbnail"
                                     loading="lazy"
                                     alt="File {file.text}"
                                     src="/files/{file.id}?width=32"/>
                            {/if}

                            <span class="texts-container">
                                <span class="display-name">{pipe(file, loc())}</span>
                                <small class="text-muted">{file.filename}</small>
                            </span>

                            {#if exists(file.size) && file.size > 0}
                                <small class="file-size text-muted">
                                    {pipe(file.size, byteFormatter())}
                                </small>
                            {/if}
                        </button>
                    </li>
                {/each}
            </ul>

            {#if files.length === 0}
                <span class="text-center text-muted">
                    {localize(/* @kp-localization commons.ZadnePolozky */ 'commons.ZadnePolozky')}:
                </span>
            {/if}
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        {#if model.multiselect}
            <KpButton on:click={handleSelectFilesClick} isDisabled="{selectedFiles.length === 0}">
                {localize(/* @kp-localization file.SelectFiles */ 'file.SelectFiles')}:
                {selectedFiles.length}
            </KpButton>
        {/if}

        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .files-result-list {
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: @spacing-m 0;

        .file-card-button {
            width: 100%;
            display: flex;
            align-items: center;
            gap: @spacing-ml;
            padding: @spacing-sm @spacing-ml;
            background: none;
            border: none;
            outline: none;
            cursor: pointer;
            transition: background-color 0.2s ease-in-out;

            &:hover {
                background-color: @themed-panel-bg;
            }

            &.file-selected {
                background-color: @themed-body-bg-blue-highlighted;
            }

            .file-select-checkbox {
                pointer-events: none;
            }

            .thumbnail {
                padding: 0;
                margin: 0;
                border: 1px solid @themed-border-default;
                border-radius: @border-radius-small;
                height: 64px;
            }

            .texts-container {
                display: flex;
                align-items: start;
                flex-direction: column;

                .display-name {
                    font-weight: 500;
                }
            }

            .file-size {
                margin-left: auto;
            }
        }
    }

    :global {
        .files-selection-modal {
            .modal-body {
                padding: 0;
            }
        }
    }
</style>