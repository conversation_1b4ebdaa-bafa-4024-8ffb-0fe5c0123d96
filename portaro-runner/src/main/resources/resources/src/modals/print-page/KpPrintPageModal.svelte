<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import type {PrintPageResponse} from './types';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';

    export let model: PrintPageResponse;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
</script>


<KpModalContent {modalWindowActions} additionalClasses="print-page-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
        </KpModalTitle>
    </svelte:fragment>
    <svelte:fragment slot="body">
        <div class="text-center">
            <iframe src="{model.url}"
                    title={localize(/* @kp-localization commons.tisk */ 'commons.tisk')}
                    style="width: 100%; height: 100%"/>
        </div>
    </svelte:fragment>
    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton action="acknowledge"/>
    </svelte:fragment>
</KpModalContent>

