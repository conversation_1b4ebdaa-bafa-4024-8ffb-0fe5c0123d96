<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector} from 'core/svelte-context/context';
    import WalkerService from 'shared/services/walker.service';
    import type {SubmittingFormActionModel} from './types';
    import KpUniversalFormModal from 'src/modals/universal-form-modal/KpUniversalFormModal.svelte';
    import ActionRequestDataService from 'shared/data-services/action-request-data.service';
    import type {UniversalFormModalModel} from 'src/modals/universal-form-modal/types';

    export let model: SubmittingFormActionModel;
    export let modalWindowActions: ModalWindowActions;

    const actionRequestDataService = getInjector().getByClass(ActionRequestDataService);
    const walkerService = getInjector().getByClass(WalkerService);

    function createUniversalFormModel(submittingFormModel: typeof model): UniversalFormModalModel {
        return {
            formObject: submittingFormModel.formObject,
            formSetting: submittingFormModel.formSetting,
            submitFunction
        }
    }

    const submitFunction = (transferableFormObject: Record<string, any>) => {
        if (model.submitAsPage) {
            return Promise.resolve(walkerService.newPage(model.submitPath, transferableFormObject));
        }
        return actionRequestDataService.request(model.submitPath, model.submitMethod, transferableFormObject);
    }
</script>


<KpUniversalFormModal {modalWindowActions} model="{createUniversalFormModel(model)}"/>
