<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import type {RegistrationOptions} from 'shared/login/registration-options';
    import {exists} from 'shared/utils/custom-utils';
    import {getSanitize} from 'core/svelte-context/context';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let registrationOptions: RegistrationOptions;
    export let modalWindowActions: ModalWindowActions;
    export let buttonIcon: UIcons;
    export let buttonLabel: string;
    export let description: string;
    export let option: string;
    export let dataQa: string;

    const registrationOption = registrationOptions.get(option);
    const htmlSanitize = getSanitize();

    const handleClick = () => {
        registrationOption.doAction();
        modalWindowActions.cancel();
    }
</script>

{#if exists(registrationOption) && registrationOption.isEnabled()}
    <li class="kp-registration-option">
        <KpButton on:click={handleClick} {dataQa}>
            <IconedContent orientation="horizontal"
                           align="center"
                           justify="center"
                           icon="{buttonIcon}">

                {buttonLabel}
            </IconedContent>
        </KpButton>

        <span class="option-description">{@html htmlSanitize(description)}</span>
    </li>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .kp-registration-option {
        display: flex;
        align-items: center;
        gap: @spacing-ml;

        @media (max-width: @screen-xs-max) {
            gap: @spacing-sm;
            flex-direction: column;
        }

        .option-description {
            flex: 1;
        }
    }

    :global {
        .kp-registration-option button {
            flex: 1;

            @media (max-width: @screen-xs-max) {
                width: 100%;
            }
        }
    }
</style>