<script lang="ts">
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import {DepartmentCreatedModalService} from 'src/modals/department-created-modal/department-created-modal.service';
    import CurrentAuthService from 'shared/services/current-auth.service';

    export let model: void;
    export let modalWindowActions: ModalWindowActions;

    ignoreUnusedProperties(model);

    const localize = getLocalization();
    const departmentCreatedService = getInjector().getByClass(DepartmentCreatedModalService);
    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const currentAuth$ = currentAuthService.currentAuth$();

    $: host = departmentCreatedService.getHost();

    function closeModal() {
        modalWindowActions.cancel(); // probably to close without another toast notification (in page controller)
    }

    async function activateDepartment() {
        await departmentCreatedService.activateDepartment();
        closeModal();
    }

    async function registerWithEditorAccount() {
        await departmentCreatedService.registerWithEditorAccount();
        closeModal();
    }

    async function loginAndAddEditorAccount() {
        await departmentCreatedService.loginAndAddEditorAccount();
        closeModal();
    }

    async function addEditorAccount() {
        await departmentCreatedService.addEditorAccount();
        closeModal();
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="department-created-modal">
    <svelte:fragment slot="body">
        <div class="text-center">
            <h1>
                {localize(/* @kp-localization departmentCreation.CongratulationsYourLibraryIsReady */ 'departmentCreation.CongratulationsYourLibraryIsReady')}
            </h1>
            <p>
                {localize(/* @kp-localization departmentCreation.YouCanFindItAt */ 'departmentCreation.YouCanFindItAt')}
                <em>{host}</em>
            </p>
        </div>
    </svelte:fragment>
    <svelte:fragment slot="footer">
        {#key $currentAuth$}
            <div class="department-created-modal-footer">
                {#if (currentAuthService.isNotLoggedIn())}
                    <p>
                        {localize(/* @kp-localization departmentCreation.YouNeedAnAccount */ 'departmentCreation.YouNeedAnAccount')}
                    </p>
                    <p>
                        <KpButton buttonStyle="primary" on:click={registerWithEditorAccount}>
                            {localize(/* @kp-localization departmentCreation.CreateAnAccount */ 'departmentCreation.CreateAnAccount')}
                        </KpButton>
                        <KpButton on:click={loginAndAddEditorAccount}>
                            {localize(/* @kp-localization departmentCreation.UseExistingAccount */ 'departmentCreation.UseExistingAccount')}
                        </KpButton>
                    </p>
                {:else}
                    {#if (currentAuthService.hasNoRole('ROLE_LIBRARIAN'))}
                        <p>
                            {localize(/* @kp-localization departmentCreation.YouNeedAdminAccountUpgrade */ 'departmentCreation.YouNeedAdminAccountUpgrade')}
                        </p>
                        <p>
                            <KpButton buttonStyle="primary" on:click={addEditorAccount}>
                                {localize(/* @kp-localization departmentCreation.UpgradeTheAccount */ 'departmentCreation.UpgradeTheAccount')}
                            </KpButton>
                        </p>
                    {:else}
                        <p>
                            {localize(/* @kp-localization departmentCreation.YouCanStartUsing */ 'departmentCreation.YouCanStartUsing')}
                        </p>
                        <p>
                            <KpButton buttonStyle="primary" on:click={activateDepartment}>
                                {localize(/* @kp-localization departmentCreation.LetsStart */ 'departmentCreation.LetsStart')}
                            </KpButton>
                        </p>
                    {/if}
                {/if}
            </div>
        {/key}
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    h1 {
        font-size: 1.5em;
    }
    em {
        display: block;
        font-style: normal;
        font-size: 2em;
        font-weight: 500;
        line-height: 1.1;
        margin-top: 20px;
        margin-bottom: 10px;
    }

    .department-created-modal-footer {
        width: 100%;
        p {
            display: flex;
            justify-content: center;
            gap: 10px;
        }
    }
</style>