<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {UserPreferencesModalModel} from './types';
    import type {UserPreference} from 'typings/portaro.be.types';
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import {KpUserPreferencesModalService} from './kp-user-preferences-modal.service';
    import {replaceAll, byProperty} from 'shared/utils/array-utils';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import UserPreferenceItem from './UserPreferenceItem.svelte';

    export let model: UserPreferencesModalModel;
    export let modalWindowActions: ModalWindowActions;

    const service = getInjector().getByToken<KpUserPreferencesModalService>(KpUserPreferencesModalService.serviceName);

    const localize = getLocalization();
    const htmlSanitize = getSanitize();

    const handleUserPreferenceUpdate = (event: CustomEvent<UserPreference>) => {
        const preference = event.detail;
        const userPreferences = replaceAll(model.userPreferences, byProperty('keyId', preference.keyId), preference);
        model = {...model, userPreferences}
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="user-preferences-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization commons.UzivatelskeNastaveni */ 'commons.UzivatelskeNastaveni')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if model.message}
            <p class="modal-message text-center">
                <strong>{@html htmlSanitize(model.message)}</strong>
            </p>
        {/if}

        <KpClassicTable additionalClasses="user-preferences-table"
                        responsive
                        colorAccented
                        columnHeadersCentered
                        horizontallyDivided
                        hoverRows>

            <tr slot="header">
                <th>{localize(/* @kp-localization commons.nazev */ 'commons.nazev')}</th>
                <th>{localize(/* @kp-localization commons.Value */ 'commons.Value')}</th>
                <th>{localize(/* @kp-localization commons.Uprava */ 'commons.Uprava')}</th>
            </tr>

            <svelte:fragment slot="body">
                {#each model.userPreferences as userPreference(userPreference.keyId)}
                    <UserPreferenceItem {userPreference}
                                        {service}
                                        on:update={handleUserPreferenceUpdate}/>
                {/each}
            </svelte:fragment>
        </KpClassicTable>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    .modal-message {
        margin: 0;
        padding: @spacing-ml @spacing-ml;
    }

    :global {
        .user-preferences-modal {
            .modal-body {
                padding: 0;
                gap: 0;
            }

            .user-preferences-table {
                tr:first-child > td {
                    border-top: none;
                }

                tr > td,
                tr > th {
                    padding: @table-cell-padding @spacing-ml !important;
                }
            }
        }
    }
</style>