<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLocalization} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import {ignoreUnusedProperties} from 'shared/utils/custom-utils';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import UserService from 'src/features/user/user.service';
    import CurrentAuthService from 'shared/services/current-auth.service';

    export let model: void;
    export let modalWindowActions: ModalWindowActions;

    ignoreUnusedProperties(model);

    const localize = getLocalization();
    const currentAuthService = getInjector().getByClass(CurrentAuthService);
    const userService = getInjector().getByClass(UserService);

    async function activateUser() {
        await userService.activateUser(currentAuthService.getCurrentAuthValue().activeUser);
        modalWindowActions.cancel();
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="user-activation-required-modal">
    <svelte:fragment slot="header">
        <KpModalTitle>
            {localize(/* @kp-localization user.activation.AccountActivation */ 'user.activation.AccountActivation')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <div class="text-center">
            <p>
                {localize(/* @kp-localization user.activation.YourAccountIsNotYetActivated */ 'user.activation.YourAccountIsNotYetActivated')}
            </p>
            <KpButton buttonStyle="primary" dataQa="ser-activation-required-button" on:click={() => activateUser()}>
                {localize(/* @kp-localization user.activation.ActivateAccount */ 'user.activation.ActivateAccount')}
            </KpButton>
        </div>
    </svelte:fragment>
</KpModalContent>

