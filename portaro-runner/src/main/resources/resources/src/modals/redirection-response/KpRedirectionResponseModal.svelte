<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import type {RedirectionResponse} from 'typings/portaro.be.types';
    import WalkerService from 'shared/services/walker.service';
    import {onMount} from 'svelte';
    import {responseTypes} from 'shared/constants/portaro.constants';
    import {PopupBlockerError} from 'shared/errors/popup-blocker.error';
    import {exists} from 'shared/utils/custom-utils';

    export let model: RedirectionResponse;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const sanitize = getSanitize();
    const walkerService = getInjector().getByClass(WalkerService);

    let error: Error = null;
    $: redirectionHasErrored = exists(error);
    $: popupBlocked = exists(error) && error instanceof PopupBlockerError;

    onMount(() => {
        try {
            redirect(); // do not 'await'
            modalWindowActions.submitPromise(Promise.resolve({
                responseType: responseTypes.FINISHED_RESPONSE,
                finished: true
            }));
        } catch (e) {
            error = e;
        }
    })

    async function redirect() {
        switch (model.strategy) {
            case 'AUTO': return walkerService.newPageDetectNewWindow(model.url, null);
            case 'CURRENT_WINDOW': return walkerService.newPage(model.url, null);
            case 'NEW_WINDOW': return walkerService.newPageInNewWindow(model.url, null);
        }
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="redirection-response-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        {#if redirectionHasErrored}
            <KpModalTitle>
                {localize(/* @kp-localization commons.chyba */ 'commons.chyba')}
            </KpModalTitle>
        {/if}
    </svelte:fragment>
    <svelte:fragment slot="body">
        {#if redirectionHasErrored}
            <div class="text-center text-danger">
                {#if popupBlocked}
                    {localize(/* @kp-localization error.PopupBlocked */ 'error.PopupBlocked')}
                {:else}
                    <div>{localize(/* @kp-localization error.UnknownError */ 'error.UnknownError')}</div>
                    <div>{JSON.stringify(error, null, 2)}</div>
                {/if}
            </div>
        {:else}
            <p>
                <strong>{@html sanitize(model.text)}</strong>
            </p>
        {/if}
    </svelte:fragment>
    <svelte:fragment slot="footer">
        {#if redirectionHasErrored}
            <KpModalFooterCloseButton action="cancel"/>
        {/if}
    </svelte:fragment>
</KpModalContent>
