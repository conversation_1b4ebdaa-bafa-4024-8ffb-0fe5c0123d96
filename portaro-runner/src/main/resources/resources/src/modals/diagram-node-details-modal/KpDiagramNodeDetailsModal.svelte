<script lang="ts">
    import type {DiagramNodeDetailsModalModel} from './types';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';

    export let model: DiagramNodeDetailsModalModel;
    export let modalWindowActions: ModalWindowActions;
</script>

<KpModalContent {modalWindowActions} additionalClasses="diagram-node-details-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>{model.heading}</KpModalTitle>
    </svelte:fragment>

    <svelte:component slot="body"
                      this="{model.contentComponent}"
                      item="{model.item}"/>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>