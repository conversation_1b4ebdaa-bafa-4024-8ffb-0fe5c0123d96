<script lang="ts">
    import type {SingleAcceptableValueEditorOptions} from 'shared/value-editors/internal/editors/single-acceptable/types';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {CatalogueSelectionModalModel, SelectableCatalogue} from './types';
    import type {SvelteComponentConstructor} from 'core/types';
    import {createPayloadActionResponse} from '../modal-utils';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import CustomAcceptableEditorOption from './CustomAcceptableEditorOption.svelte';

    export let model: CatalogueSelectionModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();

    function selectCatalogue(selectedCatalogue: SelectableCatalogue) {
        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(selectedCatalogue.url)));
    }

    function createValueEditorOptions(): SingleAcceptableValueEditorOptions<SelectableCatalogue> {
        const catalogues: SelectableCatalogue[] = Object.keys(model).map((key) => ({
            url: key,
            department: model[key]
        }));

        return {
            acceptableValues: catalogues,
            switchToInlineModeThreshold: 0,
            optionIdResolver: ({option}) => option.department.id,
            optionLabelComponent: CustomAcceptableEditorOption as SvelteComponentConstructor<{ option: SelectableCatalogue }>
        }
    }

    const handleDontRedirectClick = () => {
        modalWindowActions.cancel();
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="catalogue-selection-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization login.CatalogSelection */ 'login.CatalogSelection')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <p>
            {localize(/* @kp-localization login.selectDepartment.Message */ 'login.selectDepartment.Message')}
        </p>

        <KpValueEditor type="single-acceptable"
                       model="{null}"
                       options="{createValueEditorOptions()}"
                       on:model-change={(event) => selectCatalogue(event.detail)}/>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton on:click={handleDontRedirectClick}>
            {localize(/* @kp-localization login.selectDepartment.DontRedirect */ 'login.selectDepartment.DontRedirect')}
        </KpButton>
    </svelte:fragment>
</KpModalContent>