<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getLocalization, getSanitize} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import type {MultipleRenewalResponse} from './types';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import {isExceptionActionResponse} from 'shared/utils/types-utils';
    import KpHorizontalSeparator from 'shared/ui-widgets/separator/KpHorizontalSeparator.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';

    export let model: MultipleRenewalResponse;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const sanitize = getSanitize();
</script>


<KpModalContent {modalWindowActions} additionalClasses="finished-renewal-composite-response-modal">
    <svelte:fragment slot="header">
        <KpModalTitle>
            {localize(/* @kp-localization loan.RenewAllLoans */ 'loan.RenewAllLoans')}
        </KpModalTitle>
    </svelte:fragment>>

    <svelte:fragment slot="body">
        {#each model.renewalResponses as response, index}
            <div class="text-center alert"
                 class:alert-success={!isExceptionActionResponse(response)}
                 class:alert-danger={isExceptionActionResponse(response)}>
                <p>
                    <span>{response.actionText}:</span>
                    <strong>{@html sanitize(response.text)}</strong>
                </p>
            </div>
            {#if index < (model.renewalResponses.length - 1)}
                <KpHorizontalSeparator marginVertical="0px"/>
            {/if}
        {/each}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton action="acknowledge" dataQa="finished-response-close-button"/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    .alert {
        margin: 0 10% 0 10%;
    }
</style>

