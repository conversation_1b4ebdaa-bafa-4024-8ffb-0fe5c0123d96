<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {SpecialSymbolsKeyboardModalModel} from './types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpSpecialSymbolsKeyboard from './kp-special-symbols-keyboard/KpSpecialSymbolsKeyboard.svelte';

    export let model: SpecialSymbolsKeyboardModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();

    const handleSymbolChosen = (event: CustomEvent<string>) => {
        model.inputRef.value = `${model.inputRef.value}${event.detail}`;

        const inputEvent = new InputEvent('input', {
            bubbles: true,
            cancelable: true
        });

        model.inputRef.dispatchEvent(inputEvent);
        modalWindowActions.acknowledge();
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="special-symbols-keyboard-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <!-- CUSTOM MODAL HEADING -->
        <div class="modal-title">
            <h1>
                {localize(/* @kp-localization commons.InsertingSpecialCharactersHeading */ 'commons.InsertingSpecialCharactersHeading')}
            </h1>

            <small class="text-muted">
                {localize(/* @kp-localization commons.InsertingSpecialCharactersDescription */ 'commons.InsertingSpecialCharactersDescription')}
            </small>
        </div>
    </svelte:fragment>

    <KpSpecialSymbolsKeyboard slot="body" on:close={() => modalWindowActions.cancel()} on:symbol={handleSymbolChosen}/>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    :global {
        .special-symbols-keyboard-modal {
            .modal-body {
                padding: 0;
                gap: 0;
            }
        }
    }

    .modal-title {
        display: flex;
        flex-direction: column;
        gap: @spacing-s;

        h1 {
            margin: 0;
            font-size: 1.3em;
        }
    }
</style>