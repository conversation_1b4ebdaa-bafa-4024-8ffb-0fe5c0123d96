<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import type {LoanReadyNoticeToSendQuestion} from './types';
    import type {Identified} from 'typings/portaro.be.types';
    import LoanDataService from 'src/features/loan/loan.data-service';

    export let model: LoanReadyNoticeToSendQuestion;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const sanitize = getSanitize();
    const loanDataService = getInjector().getByClass(LoanDataService);

    function processLoan(loan: Identified<string>, exemplar: Identified<number>): void {
        modalWindowActions.submitPromise(loanDataService.processLoan(loan.id, exemplar.id));
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="loan-ready-notice-question-to-send-modal">
    <svelte:fragment slot="body">
        <div class="text-center">
            <p>
                <strong>{@html sanitize(model.text)}</strong>
            </p>
            <p>
                {localize(/* @kp-localization loan.loanReadyNotice.UserWishesToBeNotifiedBy */ 'loan.loanReadyNotice.UserWishesToBeNotifiedBy')}
                <strong>{model.userReservationsPrintType.text}</strong>
            </p>
            <p>
                <strong>{localize(/* @kp-localization loan.loanReadyNotice.SendNotice */ 'loan.loanReadyNotice.SendNotice')}</strong>
            </p>
        </div>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary"
                  dataQa="loan-ready-send-notice-yes"
                  on:click={() => processLoan(model.loan, model.exemplar)}>
            {localize(/* @kp-localization commons.ANO */ 'commons.ANO')}
        </KpButton>

        <KpButton buttonStyle="default"
                  dataQa="loan-ready-send-notice-no"
                  on:click={() => modalWindowActions.cancel()}>
            {localize(/* @kp-localization commons.NE */ 'commons.NE')}
        </KpButton>
    </svelte:fragment>
</KpModalContent>

