<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getSanitize} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import type {UniversalFormModalModel} from './types';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import KpValueEditorForceSettings from 'shared/value-editors/kp-value-editor-force-settings/KpValueEditorForceSettings.svelte';
    import {transferify} from 'shared/utils/data-service-utils';
    import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import type {ForceSetting} from 'shared/value-editors/kp-value-editor-force-settings/types';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import {exists} from 'shared/utils/custom-utils';
    import type {Field} from 'node_modules/svelte-forms/types';
    import type {Observable} from 'rxjs';

    export let model: UniversalFormModalModel;
    export let modalWindowActions: ModalWindowActions;

    const sanitize = getSanitize();

    let formRef: {getFormController: () => FormGroup<Record<string, any>>};
    $: if (exists(formRef)) {
        formState$ = formRef.getFormController().getFieldState$();
        isAsyncValidationPending$ = formRef.getFormController().isAsyncValidationPending();
    }
    let formState$: Observable<Field<any>>;
    $: isFormValid = $formState$?.valid ?? false;
    let isAsyncValidationPending$: Observable<boolean>;
    let formSubmitted = false;

    const formSetting = model.formSetting;
    let formObject = structuredClone(model.formObject);

    function submit() {
        if (!formSubmitted) {
            formSubmitted = true;
        }

        if (isFormValid && !$isAsyncValidationPending$) {
            modalWindowActions.submitPromise(model.submitFunction(transferify(formObject)));
        }
    }

    function createUniversalFormForceSettings(formSettings: KpUniversalFormSettings<Record<string, unknown>>): ForceSetting[] {
        if (hasAllBooleanFields(formSettings)) {
            return [{type: 'object', options: {labelsWidth: 6}}]
        }
        return [];
    }

    function hasAllBooleanFields(formSettings: KpUniversalFormSettings<Record<string, unknown>>): boolean {
        return formSettings.fields.every((field) => field.editor.type === 'boolean');
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="universal-form-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {@html sanitize(formSetting.text)}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <KpValueEditorForceSettings forceSettings="{createUniversalFormForceSettings(formSetting)}">
            <KpUniversalForm formId="{formSetting.id}"
                             formSettings={formSetting}
                             bind:model={formObject}
                             forceShowErrors={formSubmitted}
                             on:submit={submit}
                             bind:this={formRef}/>
        </KpValueEditorForceSettings>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary" dataQa="universal-form-ok-button" on:click={submit}>
            OK
        </KpButton>
        <KpModalFooterCloseButton action="cancel" dataQa="universal-form-cancel-button"/>
    </svelte:fragment>
</KpModalContent>

