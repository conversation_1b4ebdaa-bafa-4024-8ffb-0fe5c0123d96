<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {ConfirmationDialogModalModel} from './types';
    import {getLocalization, getSanitize} from 'core/svelte-context/context';
    import {createAcknowledgeActionResponse} from '../modal-utils';
    import {tick} from 'svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';

    export let model: ConfirmationDialogModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const htmlSanitize = getSanitize();

    let buttonFocused = false;
    tick().then(() => buttonFocused = true)

    const handleSubmit = () => {
        modalWindowActions.submitPromise(Promise.resolve(createAcknowledgeActionResponse()));
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="confirmation-dialog-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <p>
            <strong>{@html htmlSanitize(model.text)}</strong>
        </p>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="accent-blue-new"
                  dataQa="confirmation-dialog-submit"
                  focused="{buttonFocused}"
                  on:click={handleSubmit}>
            {localize(/* @kp-localization commons.ANO */ 'commons.ANO')}
        </KpButton>

        <KpModalFooterCloseButton dataQa="confirmation-dialog-cancel"/>
    </svelte:fragment>
</KpModalContent>