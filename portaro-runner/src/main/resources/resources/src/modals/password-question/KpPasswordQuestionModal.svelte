<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {PasswordQuestionModalModel} from './types';
    import {getSanitize} from 'core/svelte-context/context';
    import {createPayloadActionResponse} from '../modal-utils';
    import {focusOnMount} from 'shared/value-editors/internal/shared/use.focus-on-mount';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import {tick} from 'svelte';

    export let model: PasswordQuestionModalModel;
    export let modalWindowActions: ModalWindowActions;

    const htmlSanitize = getSanitize();

    let password = '';

    let inputFocused = false;
    tick().then(() => inputFocused = true)

    const handleSubmit = () => {
        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(password)));
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="password-question-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <p>
            <strong>{@html htmlSanitize(model.text)}</strong>
        </p>

        <p>
            <input type="password"
                   class="form-control"
                   required
                   bind:value={password}
                   use:focusOnMount={inputFocused}>
        </p>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="accent-blue-new" on:click={handleSubmit}>OK</KpButton>
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>