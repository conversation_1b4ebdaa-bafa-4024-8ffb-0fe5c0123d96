<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import type {ExceptionActionResponse} from 'typings/portaro.be.types';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import {ExceptionSeverity} from 'shared/constants/portaro.constants';

    export let model: ExceptionActionResponse;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
</script>


<KpModalContent {modalWindowActions} additionalClasses="finished-response-modal">
    <svelte:fragment slot="header">
        <KpModalTitle>
            {localize(/* @kp-localization commons.chyba */ 'commons.chyba')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <div class="text-center">
            <p>
                <!-- we do not use @html (as in finished-response modal), because we can show error from e.g. external web service http call, which can contain malicious error message -->
                <strong data-qa="exception-response-text">{model.text}</strong>
            </p>

            {#if model.severity === ExceptionSeverity.SEVERITY_ERROR}
                <div class="report-error-email">
                    <span>{localize(/* @kp-localization commons.ReportErrorViaEmailAt */ 'commons.ReportErrorViaEmailAt')}:</span>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
            {/if}
        </div>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton action="acknowledge" dataQa="exception-response-close-button"/>
    </svelte:fragment>
</KpModalContent>

