import type {ModalWindowActions} from 'shared/modal-dialogs/types';
import {getContext, setContext} from 'svelte';

const MODAL_CONTEXT_KEY = 'modal-context';

export interface ModalContext {
    modalWindowActions: ModalWindowActions;
}

export function setModalContext(modalWindowActions: ModalWindowActions) {
    setContext(MODAL_CONTEXT_KEY, {modalWindowActions});
}

export function getModalContext() {
    return getContext<ModalContext>(MODAL_CONTEXT_KEY);
}