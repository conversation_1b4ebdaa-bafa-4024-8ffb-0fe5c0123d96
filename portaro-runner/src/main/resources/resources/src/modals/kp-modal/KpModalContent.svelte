<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {setModalContext} from './modal-context';

    export let additionalClasses = '';
    export let id: string = null;
    export let modalWindowActions: ModalWindowActions;

    setModalContext(modalWindowActions);
</script>

<div {id} class="kp-modal-content {additionalClasses}">
    <div class="kp-modal-header">
        <slot name="header"></slot>
    </div>

    <div class="kp-modal-body">
        <slot name="body"></slot>
    </div>

    <div class="kp-modal-footer">
        <slot name="footer"></slot>
    </div>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    .kp-modal-header {
        padding: @modal-title-padding;
        border-bottom: 1px solid @modal-header-border-color;
        min-height: (@modal-title-padding + @modal-title-line-height);
        display: grid;
        grid-template-columns: 1fr auto;
        grid-template-rows: auto;
        grid-template-areas: "modal-title modal-close-button";
        grid-auto-flow: row;
        gap: 20px;
    }

    .kp-modal-body {
        padding: @modal-inner-padding;
        display: flex;
        flex-direction: column;
        gap: 20px;
        min-height: 2 * @modal-inner-padding + 20px;
    }

    .kp-modal-footer {
        padding: @modal-inner-padding;
        border-top: 1px solid @modal-footer-border-color;

        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
</style>
