<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getSanitize} from 'core/svelte-context/context';
    import WalkerService from 'shared/services/walker.service';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import type {GoToResponse} from './types';

    export let model: GoToResponse;
    export let modalWindowActions: ModalWindowActions;

    const sanitize = getSanitize();
    const walkerService = getInjector().getByClass(WalkerService);

    function goTo(url: string) {
        return walkerService.newPage(url);
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="go-to-response-modal">
    <svelte:fragment slot="body">
        <div class="text-center">
            <p>
                <strong>{@html sanitize(model.text)}</strong>
            </p>
        </div>
    </svelte:fragment>
    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary" dataQa="go-to-response-set-gdpr-button" on:click={() => goTo(model.url)}>
            {model.buttonText}
        </KpButton>
    </svelte:fragment>
</KpModalContent>

