<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {SettingsModalModel} from './types';
    import type {ViewableSettingChain} from 'typings/portaro.be.types';
    import {getInjector, getSanitize} from 'core/svelte-context/context';
    import {createSettingsContext} from 'src/features/settings/context';
    import {KpSettingsService} from 'src/features/settings/kp-settings.service';
    import {onDestroy, onMount} from 'svelte';
    import {replaceAll, byId} from 'shared/utils/array-utils';
    import {SETTINGS_CHAIN_EDITED_EVENT} from 'src/features/settings/constants';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpSettingChain from '../../features/settings/KpSettingChain.svelte';

    export let model: SettingsModalModel;
    export let modalWindowActions: ModalWindowActions;

    const htmlSanitize = getSanitize();
    const context = createSettingsContext(getInjector().getByToken<KpSettingsService>(KpSettingsService.serviceName));

    let settings = model.settings;

    onMount(() => {
        context.eventBus.addEventListener(SETTINGS_CHAIN_EDITED_EVENT, handleSettingsChainEdited);
    });

    onDestroy(() => {
        context.eventBus.removeEventListener(SETTINGS_CHAIN_EDITED_EVENT, handleSettingsChainEdited);
    });

    const handleSettingsChainEdited = (event: CustomEvent<ViewableSettingChain>) => {
        settings = replaceAll(settings, byId(event.detail.id), event.detail);
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="settings-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>Nastavení (Ini)</KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if model.message}
            <p class="modal-body-block text-center">
                <strong>{@html htmlSanitize(model.message)}</strong>
            </p>
        {/if}

        <KpClassicTable additionalClasses="settings-modal-table"
                        horizontallyDivided
                        responsive
                        hoverRows>

            <svelte:fragment slot="body">
                {#each settings as setting}
                    <KpSettingChain {setting}
                                    displaySectionLabels="{true}"/>
                {/each}
            </svelte:fragment>
        </KpClassicTable>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "bootstrap-less/bootstrap/variables";

    :global {
        .settings-modal {
            .modal-body-block {
                margin: 0;
                padding: @spacing-ml @spacing-ml;
            }

            .modal-body {
                padding: 0;
                gap: 0;
            }

            .settings-modal-table {
                tr:first-child > td {
                    border-top: none;
                }

                tr > td,
                tr > th {
                    padding: @table-cell-padding @spacing-ml !important;
                }
            }
        }
    }
</style>