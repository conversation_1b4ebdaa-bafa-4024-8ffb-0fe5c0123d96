<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getSanitize} from 'core/svelte-context/context';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import type {FinishedActionResponse} from 'typings/portaro.be.types';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';

    export let model: FinishedActionResponse;
    export let modalWindowActions: ModalWindowActions;

    const sanitize = getSanitize();
</script>


<KpModalContent {modalWindowActions} additionalClasses="finished-response-modal">
    <svelte:fragment slot="body">
        <div class="text-center">
            <p>
                <strong data-qa="finished-response-text">{@html sanitize(model.text)}</strong>
            </p>
        </div>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton action="acknowledge" dataQa="finished-response-close-button"/>
    </svelte:fragment>
</KpModalContent>

