<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {RecordEditation} from 'typings/portaro.be.types';
    import type {RecordEditationModalModel} from './types';
    import type {RecordEditationManager} from '../../features/record-editation/kp-record-editation/record-editation.manager';
    import type {Subscription} from 'rxjs';
    import {onDestroy, onMount} from 'svelte';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {createPayloadActionResponse} from '../modal-utils';
    import {getInjector} from 'core/svelte-context/context';
    import {KpRecordEditationModalService} from './kp-record-editation-modal.service';
    import {exists} from 'shared/utils/custom-utils';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpRecordEditation from '../../features/record-editation/kp-record-editation/KpRecordEditation.svelte';
    import RecordEditationHeading from '../../features/record-editation/kp-record-editation/components/RecordEditationHeading.svelte';

    export let modalWindowActions: ModalWindowActions;
    export let model: RecordEditationModalModel;

    const service = getInjector().getByToken<KpRecordEditationModalService>(KpRecordEditationModalService.serviceName);

    let editationManager: RecordEditationManager;
    let editationState: RecordEditation;
    let editationStateSubscription: Subscription;

    onMount(async () => {
        const newEditation = await service.createRecordEditation(model.editationParams);

        if (!exists(newEditation)) {
            modalWindowActions.cancel();
            return;
        }

        editationManager = service.recordEditationManagerFactory.createManager(newEditation, model.focusedFieldTypeId, {
            publishAndClose: publishDraftAndClose,
            saveAndClose,
            deleteDraftAndClose,
            discardAndClose,
            modalClose: close
        });

        editationStateSubscription = editationManager.editationState$.subscribe((currentState) => editationState = currentState);
    });

    onDestroy(() => {
        unsubscribeAllSubscriptions(editationStateSubscription);
    });

    function successClose(): void {
        const editationObject = editationManager.editationState$.getValue();
        const actionResponse = createPayloadActionResponse({
            id: editationObject.recordId,
            text: editationObject.text
        });

        modalWindowActions.submitPromise(Promise.resolve(actionResponse));
    }

    async function publishDraftAndClose(): Promise<void> {
        await editationManager.publish();
        successClose();
    }

    async function deleteDraftAndClose(): Promise<void> {
        await editationManager.remove();
        modalWindowActions.cancel();
    }

    async function saveAndClose(): Promise<void> {
        await editationManager.save();
        successClose();
    }

    function discardAndClose(): Promise<void> {
        successClose();
        return Promise.resolve();
    }

    async function close(): Promise<void> {
        if (editationState.draft) {
            return deleteDraftAndClose();
        }
        return discardAndClose();
    }
</script>

{#if exists(editationManager)}
    <KpModalContent {modalWindowActions} additionalClasses="record-editation-modal">
        <svelte:fragment slot="header">
            <KpModalHeaderCloseButton defaultClose="{false}"
                                      on:close={() => editationManager.callbacks.modalClose()}/>

            <KpModalTitle>
                <RecordEditationHeading {editationState}/>
            </KpModalTitle>
        </svelte:fragment>

        <svelte:fragment slot="body">
            <KpRecordEditation viewType="modal" {editationManager}/>
        </svelte:fragment>

        <svelte:fragment slot="footer">
            <KpModalFooterCloseButton/>
        </svelte:fragment>
    </KpModalContent>
{/if}

<style lang="less">
    :global {
        .record-editation-modal .modal-body {
            padding: 0;
        }
    }
</style>