<script lang="ts">
    import KpModalTitle from '../kp-modal/KpModalTitle.svelte';
    import KpModalContent from '../kp-modal/KpModalContent.svelte';
    import KpModalFooterCloseButton from '../kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from '../kp-modal/KpModalHeaderCloseButton.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {LoanRequestFormActionResponse, LoanRequestOption} from 'typings/portaro.be.types';
    import {LoanRequestFormService} from './loan-request-form.service';
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import {exists} from 'shared/utils/custom-utils';
    import {stringifyArgument} from 'shared/utils/error-utils';
    import {setLoanRequestFormContext} from './context';
    import {CAPABILITY_TO_COMPONENT_MAP} from './constants';

    export let model: LoanRequestFormActionResponse;
    export let modalWindowActions: ModalWindowActions;

    const presenter = getInjector().getByClass(LoanRequestFormService);
    const localize = getLocalization();
    const sanitize = getSanitize();

    let showLoading = false;
    let loanRequestForm = model; // to prevent overwriting current form from prop if we update form from within the component

    let loanRequestOptions: LoanRequestOption[];
    $: loanRequestOptions = loanRequestForm.loanRequestOptions;


    const loginAndUpdateForm = async () => {
        await presenter.login();
        try {
            showLoading = true;
            loanRequestForm = await presenter.fetchUpdatedFormModel(loanRequestForm.originalRequestItems);
        } finally {
            showLoading = false;
        }
    }

    const sendRequest = async (pathWithoutApiPrefix: string, loanRequestOption: LoanRequestOption) => {
        if (presenter.requiresLogin(loanRequestOption)) {
            throw new Error(`User have to be logged in for this loan request: url=${pathWithoutApiPrefix} ${stringifyArgument(loanRequestOption)}`);
        }

        if (loanRequestOptions.length === 1) {
            modalWindowActions.resolve(presenter.requestLoan(pathWithoutApiPrefix, loanRequestOption));
        } else {
            loanRequestForm = await presenter.requestLoanAndUpdateCurrentModel(pathWithoutApiPrefix, loanRequestOption, loanRequestForm);
        }
    }

    setLoanRequestFormContext(loginAndUpdateForm, sendRequest);
</script>

<KpModalContent {modalWindowActions} additionalClasses="loan-request-dialog">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>{localize(/* @kp-localization loan.Vypujcit */ 'loan.Vypujcit')}</KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if showLoading}
            <KpLoadingBlock size="lg"/>
        {:else}
            {#each loanRequestOptions as option}
                <div class="modal-body-block">
                    <h2 class="request-items-heading">
                        {#each option.requestItems as item, index}
                            <strong>
                                {item.document.name}
                                {#if exists(item.exemplar)}
                                    <span>
                                        {item.exemplar.signature} {item.exemplar.bundledVolumeNumber} {item.exemplar.bundledVolumeYear}
                                    </span>
                                {/if}
                                {#if index !== option.requestItems.length - 1}
                                    <span>, </span>
                                {/if}
                            </strong>
                        {/each}
                    </h2>

                    <div class="loan-request-option-container">
                        {#if exists(option.responses)}
                            <div class="loan-request-option-responses">
                                <ul>
                                    {#each option.responses as response}
                                        <li class="alert alert-success">{@html sanitize(response.text)}</li>
                                    {/each}
                                </ul>
                            </div>
                        {:else}
                            <div class="loan-request-option {CAPABILITY_TO_COMPONENT_MAP[option.capability.name].cssClass}">
                                <svelte:component this="{CAPABILITY_TO_COMPONENT_MAP[option.capability.name].component}" {option}/>
                            </div>
                        {/if}
                    </div>
                </div>
            {:else}
                <div class="text-center">
                    <strong>{localize(/* @kp-localization loan.NelzeVypujcit */ 'loan.NelzeVypujcit')}</strong>
                </div>
            {/each}
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton dataQa="order-request-close-button"/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    .request-items-heading {
        font-size: 1.1em;
        padding: 0;
        margin: 0;
        text-align: center;
    }

    .loan-request-option-responses {
        margin: 20px 0;
        font-size: 1.2em;
    }

    .loan-request-option-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 5px;
    }

    :global {
        .loan-request-option {
            width: 75%;

            .panel:hover {
                box-shadow: 0 0 6px #d3d3d3;
            }

            ul {
                margin-left: 18px;
                li {
                    list-style-type: disc;
                }
            }

            ol {
                margin-left: 20px;
            }
        }
    }
</style>