import type LoanRequestService from '../../features/loan/loan-request.service';
import type {
    Auth,
    Department,
    LoanRequestFormActionResponse,
    LoanRequestItem,
    LoanRequestOption, SlotVisitOrderability
} from 'typings/portaro.be.types';
import type LoginService from 'shared/login/login.service';
import type LoanRequestDataService from '../../features/loan/loan-request.data-service';
import type FinishedResponseInteractionService from 'shared/services/finished-response-interaction.service';
import type {LogService} from 'core/logging/log.service';


export class LoanRequestFormService {
    static serviceName = 'loanRequestFormService';

    /*@ngInject*/
    constructor(private loanRequestService: LoanRequestService,
                private currentAuth: Auth,
                private currentDepartment: Department,
                private loginService: LoginService,
                private loanRequestDataService: LoanRequestDataService,
                private finishedResponseInteractionService: FinishedResponseInteractionService,
                private logService: LogService
    ) {
    }

    public requiresLogin(option: LoanRequestOption): boolean {
        return option.capability.loginRequired && !this.currentAuth.evided;
    }

    public async login(): Promise<void> {
        await this.loginService.login();
    }

    public async fetchUpdatedFormModel(originalRequestItems: LoanRequestItem[]) {
        try {
            return await this.loanRequestDataService.requestLoanForm(originalRequestItems, true);
        } catch (error) {
            this.finishedResponseInteractionService.showFailedResponseInToast(error);
            throw error;
        }
    }

    public requestLoan(pathWithoutApiPrefix: string, loanRequestOption: LoanRequestOption) {
        this.logService.info('requestLoan', pathWithoutApiPrefix, loanRequestOption);
        return this.loanRequestService.requestLoan(pathWithoutApiPrefix, loanRequestOption.formObject);
    }

    public async requestLoanAndUpdateCurrentModel(pathWithoutApiPrefix: string, loanRequestOption: LoanRequestOption, currentModel: LoanRequestFormActionResponse): Promise<LoanRequestFormActionResponse> {
        try {
            const response = await this.requestLoan(pathWithoutApiPrefix, loanRequestOption);
            loanRequestOption.responses = [response];
            return currentModel;
        } catch (errorResult) {
            await this.finishedResponseInteractionService.showFailedInModalWindow(errorResult);
            return currentModel;
        }
    }

    public setDesiredBuildingDefault(option: LoanRequestOption<SlotVisitOrderability>) {
        if (!option.formObject.desiredBuilding) { // if we do not have any slots today, we will not have desiredBuilding in formObject -> set currentDepartment
            option.formObject.desiredBuilding = this.currentDepartment;
        }
        return option;
    }


    public async fetchSlotOrderingForm(option: LoanRequestOption<SlotVisitOrderability>, slotScopeDateRange: {fromDate: string}) {
        const params = {
            document: option.formObject.items[0].document,
            requester: option.formObject.requester,
            requestDepartment: option.formObject.desiredBuilding,
            scopeDateRange: {
                fromDate: slotScopeDateRange.fromDate,
                toDate: new Date(new Date(slotScopeDateRange.fromDate).setHours(24, 0, 0, 0)).toISOString() // = slotScopeDateRange plus 1 day
            }
        };
        option.form = await this.loanRequestDataService.getDocumentCapabilityForm(option.capability.name, params);
        return option;
    }
}