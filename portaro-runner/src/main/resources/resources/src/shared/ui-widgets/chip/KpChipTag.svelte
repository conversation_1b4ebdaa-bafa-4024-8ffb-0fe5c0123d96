<script lang="ts">
    import './kp-chip-tag.styles.less';
    import type {ButtonStyle} from 'shared/ui-widgets/types';
    import type {ChipSize} from 'shared/ui-widgets/chip/types';
    import {exists} from 'shared/utils/custom-utils';

    export let chipSize: ChipSize = 'sm';
    export let chipStyle: ButtonStyle = 'default';
    export let chipTitle: string | null = null;
    export let additionalClasses = '';
    export let width = 'min-content';
    export let dataQa: string | null = null;
    export let hideEmpty = true;
    export let backgroundColor: string | null = null;
    export let textColor: string | null = null;

    let chipRef: HTMLSpanElement = null;

    export function getChipRef(): HTMLSpanElement {
        return chipRef;
    }

    const chipSizes = {
        xxs: {
            height: 16,
            fontSize: 10,
            horizontalPadding: 8
        },
        xs: {
            height: 20,
            fontSize: 12,
            horizontalPadding: 10
        },
        sm: {
            height: 24,
            fontSize: 13,
            horizontalPadding: 12
        },
        md: {
            height: 32,
            fontSize: 15,
            horizontalPadding: 14
        },
        lg: {
            height: 40,
            fontSize: 17,
            horizontalPadding: 16
        }
    };

    const size = chipSizes[chipSize];
</script>

<span class="kp-chip-tag kp-chip-tag-{chipStyle} {additionalClasses}"
      style="{exists(backgroundColor) ? `background-color: ${backgroundColor};` : ''} {exists(textColor) ? `color: ${textColor};` : ''}"
      class:empty-hidden={hideEmpty}
      bind:this={chipRef}
      title={chipTitle}
      data-qa="{dataQa}"
      style:padding="0 {size.horizontalPadding}px"
      style:border-radius="{size.height / 2}px"
      style:font-size="{size.fontSize}px"
      style:height="{size.height}px"
      style:width="{width}">

    <slot/>
</span>

<style lang="less">
    .kp-chip-tag {
        flex-shrink: 0;
        font-weight: 500;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;

        &.empty-hidden:empty {
            display: none;
        }
    }
</style>