<script lang="ts">
    import type {GridField} from 'src/features/record-grid/lib/types';
    import type {ChipSize} from 'shared/ui-widgets/chip/types';
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {exists, isDefined} from 'shared/utils/custom-utils';
    import {getTextFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import SutorFondChip from 'src/features/sutor/pages/record-detail/components/SutorFondChip.svelte';

    export let value: string | number | null = null;
    export let field: GridField | null = null;
    export let href: string | null = null;
    export let lengthLimit = 35;
    export let fondId: number | null = null;
    export let chipSize: ChipSize = 'xs';

    const link = href ?? (exists(field) && hasRecordReference(field) ? `/#!/records/${field.recordReference.id}` : null);
    const stringValue = isDefined($$props.field) ? getTextFromGridFieldValue(field) : (typeof value === 'number' ? value.toString(10) : value);

    function getDisplayedText(): string {
        if (!exists(stringValue)) {
            return '';
        }

        return stringValue.substring(0, lengthLimit);
    }

    $: wasShortened = (stringValue ?? '').length > lengthLimit;
</script>

<span class="barebones-table-value"
      use:tooltip={{enabled: wasShortened && !$$slots.default, content: stringValue, role: 'tooltip'}}>

    {#if $$slots.default}
        <div class="row-container">
            <slot/>
        </div>
    {:else}
        {#if exists(field?.recordReference?.fond) || exists(fondId)}
            {@const displayedText = getDisplayedText()}

            {#if displayedText !== '-'}
                <SutorFondChip fondId="{exists(field?.recordReference?.fond) ? field.recordReference.fond.id : fondId}" fondName="{exists(field?.recordReference?.fond) ? field.recordReference.fond.text : null}" {chipSize}>
                    {getDisplayedText()}

                    {#if wasShortened}
                        <span class="ellipsis-icon-container" aria-hidden="true">…</span>
                    {/if}
                </SutorFondChip>
            {:else}
                {displayedText}
            {/if}
        {:else}
            {getDisplayedText()}

            {#if wasShortened}
                <span class="ellipsis-icon-container" aria-hidden="true">…</span>
            {/if}
        {/if}
    {/if}

    {#if exists(link)}
        <div class="link-button-container">
            <KpIconButton buttonSize="xs" href="{link}" icon="link" accented/>
        </div>
    {/if}
</span>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .barebones-table-value {
        position: relative;
        display: inline-flex;
        align-items: center;

        &:hover {
            .link-button-container {
                opacity: 1;
                visibility: visible;
                transform: translate(0, -50%);
            }
        }

        .link-button-container {
            position: absolute;
            right: calc(@spacing-s * -1);
            top: 50%;
            opacity: 0;
            transform: translate(@spacing-xs, -50%);
            visibility: hidden;
            transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out, transform 0.2s ease-in-out;
        }

        .ellipsis-icon-container {
            color: var(--accent-blue-new);
        }

        .row-container {
            display: flex;
            align-items: center;
            width: min-content;
            white-space: nowrap;
            gap: @spacing-s;
        }
    }
</style>