<script lang="ts">
    import {format} from '../../utils/string-utils';

    export let count: number;
    export let zeroCountText: string = null;
    export let oneCountText: string = null;
    export let twoThreeFourCountText: string = null;
    export let moreText: string;

    function chooseText(cnt: number) {
        if (cnt === undefined || cnt < 0) {
            return '';
        } else if (cnt === 0) {
            return zeroCountText;
        } else if (oneCountText && cnt === 1) {
            return oneCountText;
        } else if (twoThreeFourCountText && cnt <= 4) {
            return twoThreeFourCountText;
        } else {
            return moreText;
        }
    }
</script>

{format(count?.toString(10))(chooseText(count))}