<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getLocalization} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import type {LoginView} from 'shared/login/kp-login/types';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpLogin from 'shared/login/kp-login/KpLogin.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import type {Auth} from 'typings/portaro.be.types';
    import {createPayloadActionResponse} from 'src/modals/modal-utils';

    export let model: LoginView;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();

    function closeFormOnSuccess(currentAuth: Auth) {
        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(currentAuth)));
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="login-form-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>
        <KpModalTitle>
            {localize(/* @kp-localization login.menuLoginButton */ 'login.menuLoginButton')}
        </KpModalTitle>
    </svelte:fragment>
    <svelte:fragment slot="body">
        <KpLogin loginView={model} onLoginCancel="{modalWindowActions.cancel}" onLoginSuccess="{closeFormOnSuccess}"/>
    </svelte:fragment>
    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton action="cancel"/>
    </svelte:fragment>
</KpModalContent>

