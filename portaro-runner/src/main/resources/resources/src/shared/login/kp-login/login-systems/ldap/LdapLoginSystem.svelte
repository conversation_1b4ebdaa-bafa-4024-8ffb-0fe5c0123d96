<script lang="ts">
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import type {LdapLoginCredentials} from './types';
    import {LdapLoginSystemDataService} from 'shared/login/kp-login/login-systems/ldap/ldap.login-system.data-service';
    import {getLoginFormContext, getLoginSystemsContext} from 'shared/login/kp-login/context';
    import {resolveErrorMessage} from 'shared/utils/error-utils';

    const localize = getLocalization();
    const sanitize = getSanitize();
    const {onLoginSuccess} = getLoginSystemsContext();
    const {startProcessing, stopProcessing, clearError, setError} = getLoginFormContext();
    const dataService = getInjector().getByClass(LdapLoginSystemDataService)

    const credentials: LdapLoginCredentials = {
        username: '',
        password: ''
    };

    async function submitLoginForm(): Promise<void> {
        clearError();
        startProcessing();

        try {
            const currentAuthentication = await dataService.sendRequest(credentials);
            onLoginSuccess(currentAuthentication);
        } catch (e) {
            setError(resolveErrorMessage(e));
            /* ignore login error, error message was already displayed by login form */
        } finally {
            stopProcessing();
        }
    }
</script>


<div class="login-system login-system-ldap login-system-panel-top-border">

    <h2>{localize(/* @kp-localization login.prihlaseniLdapem */ 'login.prihlaseniLdapem')}</h2>

    <form id="ldapLoginForm" on:submit|preventDefault={submitLoginForm}>

        <div class="form-group">
            <label for="ldap-login-username-input">
                {@html sanitize(localize(/* @kp-localization login.ldap.usernameLabel */ 'login.ldap.usernameLabel'))}
            </label>
            <input id="ldap-login-username-input"
                   class="form-control input-sm"
                   type="text"
                   name="username"
                   autocomplete="username"
                   autocapitalize="off"
                   aria-required="true"
                   data-qa="ldap-login-username-input"
                   bind:value={credentials.username}>
        </div>

        <div class="form-group">
            <label for="ldap-login-password-input">
                {@html sanitize(localize(/* @kp-localization login.ldap.passwordLabel */ 'login.ldap.passwordLabel'))}
            </label>
            <input id="ldap-login-password-input"
                   class="form-control input-sm"
                   type="password"
                   name="password"
                   autocomplete="current-password"
                   aria-required="true"
                   data-qa="ldap-login-password-input"
                   bind:value={credentials.password}>
        </div>


        <button type="submit"
                id="ldap-login-submit-button"
                class="btn btn-primary btn-block"
                data-qa="ldap-login-submit-button">
            {localize(/* @kp-localization login.ldap.loginButton */ 'login.ldap.loginButton')}
        </button>
    </form>
</div>
