<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import type {AuthPairingResponse} from './types';
    import {LoginDataService} from 'shared/login/kp-login/kp-login.data-service';
    import type {LoginView} from '../kp-login/types';
    import {onMount} from 'svelte';
    import {AuthPairingService} from 'shared/login/account-pairing-modal/auth-pairing.service';
    import KpGenericPanel from 'shared/ui-widgets/panel/KpGenericPanel.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import {exists} from 'shared/utils/custom-utils';

    export let model: AuthPairingResponse;
    export let modalWindowActions: ModalWindowActions;

    const loginDataService = getInjector().getByClass(LoginDataService);
    const authPairingService = getInjector().getByClass(AuthPairingService);

    let loginView: LoginView;
    let showExternalFullRegistration: boolean;
    onMount(async () => {
        loginView = await loginDataService.getLoginView()
        showExternalFullRegistration = loginView.showExternalFullRegistration && model.showFullRegistration;
    })
</script>

<KpModalContent {modalWindowActions} additionalClasses="account-pairing-response-modal">
    <svelte:fragment slot="header">
        <!-- CUSTOM MODAL HEADING -->
        <h1>{model.pairingTitle}</h1>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(loginView)}
            <p class="auth-pairing-paragraph">
                {model.pairingFirstParagraph}
                {#if showExternalFullRegistration}
                    {model.pairingSecondParagraph}
                {/if}
            </p>
            <div class="options-container">
                <KpGenericPanel additionalClasses="auth-pairing-option">
                    <h2 slot="heading" class="unset-style">
                        {model.linkingWithLibraryAccountTitle}
                    </h2>

                    <div class="option-panel-content">
                        <div>
                            <p>{model.linkingWithLibraryAccountFirstParagraph}</p>
                            <p>{model.linkingWithLibraryAccountSecondParagraph}</p>
                        </div>
                        <div class="text-center">
                            <KpButton buttonStyle="primary"
                                      on:click={() => authPairingService.mergeToAuthenticatedInternalUser(model.provider.id)}>
                                {model.linkingWithLibraryAccountButtonText}
                            </KpButton>
                        </div>
                    </div>

                </KpGenericPanel>

                {#if showExternalFullRegistration}
                    <KpGenericPanel additionalClasses="auth-pairing-option">
                        <h2 slot="heading" class="unset-style">
                            {model.newAccountTitle}
                        </h2>

                        <div class="option-panel-content">
                            <div>
                                <p>{model.newAccountFirstParagraph}</p>
                                <p>{model.newAccountSecondParagraph}</p>
                                <p>{model.newAccountThirdParagraph}</p>
                            </div>
                            <div class="text-center">
                                <KpButton buttonStyle="primary"
                                          on:click={() => authPairingService.saveNewReader()}>
                                    {model.newAccountButtonText}
                                </KpButton>
                            </div>
                        </div>
                    </KpGenericPanel>
                {/if}


                <KpGenericPanel additionalClasses="auth-pairing-option">
                    <h2 slot="heading" class="unset-style">
                        {model.cancelActionTitle}
                    </h2>

                    <div class="option-panel-content">
                        <div>
                            <p>{model.cancelActionParagraph}</p>
                        </div>
                        <div class="text-center">
                            <KpButton buttonStyle="primary"
                                      on:click={() => authPairingService.logout()}>
                                {model.cancelActionButtonText}
                            </KpButton>
                        </div>
                    </div>
                </KpGenericPanel>
            </div>
        {/if}
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";

    @default-panel-width-for-3-panel-layout: 276px; // default large screen size size
    @top-and-bottom-panel-heading-padding: 20px; // padding top + padding bottom
    @panel-heading-offset: @top-and-bottom-panel-heading-padding + @line-height-computed; // cca 41px

    .auth-pairing-paragraph {
        margin: 0;
    }

    h2 {
        font-weight: bold;
    }

    .options-container {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        column-gap: 20px;

       :global {
           & > .auth-pairing-option {
               flex: 1;
               min-width: @default-panel-width-for-3-panel-layout;

               .panel-body {
                   height: calc(100% - @panel-heading-offset);
               }
           }
       }
    }

    .option-panel-content {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
</style>