<script lang="ts">
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import {InternalLoginSystemDataService} from './internal.login-system.data-service';
    import type {InternalLoginCredentials} from 'shared/login/kp-login/login-systems/internal/types';
    import UserService from 'src/features/user/user.service';
    import RegistrationService from 'shared/login/registration.service';
    import {onMount} from 'svelte';
    import type {RegistrationOptions} from 'shared/login/registration-options';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import {getLoginFormContext, getLoginSystemsContext} from 'shared/login/kp-login/context';
    import {exists} from 'shared/utils/custom-utils';
    import {resolveErrorMessage} from 'shared/utils/error-utils';

    const localize = getLocalization();
    const sanitize = getSanitize();
    const {loginView, authSystemView, onLoginSuccess, onLoginCancel} = getLoginSystemsContext();
    const {startProcessing, stopProcessing, clearError, setError} = getLoginFormContext();
    const dataService = getInjector().getByClass(InternalLoginSystemDataService)
    const userService = getInjector().getByClass(UserService);
    const registrationService = getInjector().getByClass(RegistrationService)

    let registrationOptions: RegistrationOptions;
    const credentials: InternalLoginCredentials = {
        username: '',
        password: ''
    };

    onMount(async () => {
        registrationOptions = await registrationService.getRegistrationOptions();
    })

    async function submitLoginForm(): Promise<void> {
        clearError();
        startProcessing();

        try {
            const currentAuthentication = await dataService.sendRequest(credentials);
            onLoginSuccess(currentAuthentication);
        } catch (e) {
            setError(resolveErrorMessage(e));
            /* ignore login error, error message was already displayed by login form */
        } finally {
            stopProcessing();
        }
    }


    function showRegistration() {
        onLoginCancel();
        return registrationService.showRegistrationOptions();
    }

    function showForgottenCredentials() {
        onLoginCancel();
        return userService.changePasswordMail();
    }
</script>


<div class="login-system login-system-internal login-system-panel-top-border">

    <h2>{localize(/* @kp-localization login.prihlaseniVnitrnimSystemem */ 'login.prihlaseniVnitrnimSystemem')}</h2>

    <form id="loginForm" on:submit|preventDefault={submitLoginForm}>

        <div class="form-group">
            <label for="internal-login-username-input">
                {@html sanitize(localize(/* @kp-localization login.usernameLabel */ 'login.usernameLabel'))}
            </label>
            <input id="internal-login-username-input"
                   class="form-control input-sm"
                   type="text"
                   name="username"
                   autocomplete="username"
                   required
                   autocapitalize="off"
                   aria-required="true"
                   data-qa="internal-login-username-input"
                   bind:value={credentials.username}>
        </div>

        {#if authSystemView.secondFieldEnabled}
            <div class="form-group">
                <label for="internal-login-password-input">
                    {@html sanitize(localize(/* @kp-localization login.passwordLabel */ 'login.passwordLabel'))}
                </label>
                <input id="internal-login-password-input"
                       class="form-control input-sm"
                       type="password"
                       name="password"
                       autocomplete="current-password"
                       aria-required="true"
                       data-qa="internal-login-password-input"
                       bind:value={credentials.password}>
            </div>
        {/if}


        <button type="submit"
                id="internal-login-submit-button"
                class="btn btn-primary btn-block"
                data-qa="internal-login-submit-button">
            {localize(/* @kp-localization login.loginButton */ 'login.loginButton')}
        </button>

        <div class="registration-section">
            <div class="registration-options">
                {#if exists(registrationOptions) &&  registrationOptions.isAny()}
                    <KpButton id="internal-login-new-registration-button"
                              dataQa="internal-login-new-registration-button"
                              buttonStyle="link"
                              on:click={showRegistration}>
                        {#if registrationOptions.isOnlyOne() && registrationOptions.get('credentialsRegistration').isEnabled()}
                            {localize(/* @kp-localization login.NastavitPrihlaseni */ 'login.NastavitPrihlaseni')}
                        {:else}
                            {localize(/* @kp-localization login.NotRegistered */ 'login.NotRegistered')}
                        {/if}
                    </KpButton>
                {/if}

            </div>
            <div class="forgotten-credentials">
                {#if loginView.showForgottenCredentials}
                    <KpButton buttonStyle="link" on:click={showForgottenCredentials}>
                        {localize(/* @kp-localization login.resetPassword.ForgottenPassword */ 'login.resetPassword.ForgottenPassword')}
                    </KpButton>
                {/if}
            </div>
        </div>

    </form>

</div>



<style lang="less">
    .registration-section {
        margin-top: 1em;

        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;

        .registration-options {
            display: flex;
            justify-content: flex-start;
        }

        .forgotten-credentials {
            display: flex;
            justify-content: flex-end;
        }

        :global {
            button {
                padding: 0;
                border: 0;
            }
        }
    }
</style>