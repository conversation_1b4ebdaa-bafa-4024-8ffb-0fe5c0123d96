<script lang="ts">
    import type {FlexDirection, FlexWrap, JustifyContent, AlignItems, FlexGrow, FlexShrink, FlexBasis, HtmlTag, Spacing} from './types';
    import type {CssSize} from 'shared/ui-widgets/types';
    import {getSpacingCssValue} from './types';
    import {exists} from 'shared/utils/custom-utils';

    export let as: HtmlTag = 'div';
    export let direction: FlexDirection = 'row';
    export let wrap: FlexWrap = 'nowrap';
    export let justifyContent: JustifyContent = 'flex-start';
    export let alignItems: AlignItems = 'stretch';
    export let flexGrow: FlexGrow = 0;
    export let flexShrink: FlexShrink = 1;
    export let flexBasis: FlexBasis = 'auto';
    export let gap: Spacing = '0';
    export let rowGap: Spacing | null = null;
    export let columnGap: Spacing | null = null;
    export let width: CssSize = 'auto';
    export let height: CssSize = 'auto';
    export let inline = false;
    export let fillAvailableSpace = false;

    export let element: HTMLElementTagNameMap[typeof as] = null; // For outside binding
</script>

<svelte:element this="{as}"
                class="kp-flex {$$restProps.class ?? ''}"
                {...$$restProps}
                style:width="{width}"
                style:height="{height}"
                style:display="{inline ? 'inline-flex' : 'flex'}"
                style:flex-direction="{direction}"
                style:flex-wrap="{wrap}"
                style:justify-content="{justifyContent}"
                style:align-items="{alignItems}"
                style:flex-grow="{fillAvailableSpace ? 1 : flexGrow}"
                style:flex-shrink="{fillAvailableSpace ? 1 : flexShrink}"
                style:flex-basis="{fillAvailableSpace ? 0 : flexBasis}"
                style:row-gap="{exists(rowGap) ? getSpacingCssValue(rowGap) : getSpacingCssValue(gap)}"
                style:column-gap="{exists(columnGap) ? getSpacingCssValue(columnGap) : getSpacingCssValue(gap)}"
                style="{$$restProps.style}"
                bind:this={element}>

    <slot/>
</svelte:element>