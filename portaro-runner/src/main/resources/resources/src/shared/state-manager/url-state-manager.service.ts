import type {ILocationService, IRootScopeService} from 'angular';
import type {LogService} from 'core/logging/log.service';
import type {Observable} from 'rxjs';
import {BehaviorSubject} from 'rxjs';
import type {StateManager} from './state-manager';
import {getUrlParams} from 'shared/utils/url-utils';
import {distinctUntilChanged} from 'rxjs/operators';
import {isEqual} from 'lodash-es';
import {shareWithRefCountAndReplayLast} from 'shared/utils/observables-utils';

export class UrlStateManagerService<STATE extends Record<string, any>> implements StateManager<STATE> {
    public static serviceName = 'urlStateManagerService';

    private readonly stateSource$: BehaviorSubject<STATE>;
    private readonly state$: Observable<STATE>;

    /*@ngInject*/
    constructor(private $location: ILocationService,
                private $rootScope: IRootScopeService,
                private logService: LogService) {
        this.stateSource$ = new BehaviorSubject(this.currentParams());
        this.state$ = this.stateSource$.pipe(distinctUntilChanged(isEqual), shareWithRefCountAndReplayLast());
        this.registerLocationChangeListeners();
    }

    public getState$(): Observable<STATE> {
        return this.state$;
    }

    public requestChangeState(newParams: STATE) {
        // nastavime jen zmeny oproti "?" parametrum, ktere zmenit nejdou
        const currentUrlParams = getUrlParams();
        const afterHashParams: Record<string, any> = {};
        for (const propName in newParams) {
            if (Object.hasOwn(newParams, propName)) { // sanity check
                if (!Object.hasOwn(currentUrlParams, propName) || newParams[propName] !== currentUrlParams[propName]) {
                    afterHashParams[propName] = newParams[propName];
                }
            }
        }
        this.$location.search(afterHashParams);
        this.$rootScope.$applyAsync(); // to notify angular if change request was called from outside angular context (e.g. from svelte)
    }

    // params can be whatever dictionary(Record<string, any>) object, but name of the changed parameter has to exist on that params object and it has to be string (not symbol or number)
    public requestChangeStateParameter<K extends keyof STATE>(parameterName: Exclude<K, number | symbol>, newValue: STATE[Exclude<K, number | symbol>], replace: boolean = false) {
        // nastavime jen zmeny oproti "?" parametrum, ktere zmenit nejdou
        const urlParams = getUrlParams();
        if (!Object.hasOwn(urlParams, parameterName) || urlParams[parameterName] !== newValue) {
            this.$location.search(parameterName, newValue);
            if (replace) {
                this.$location.replace();
            }

            this.$rootScope.$applyAsync(); // to notify angular if change request was called from outside angular context (e.g. from svelte)
        }
    }

    private currentParams(): STATE {
        return {...getUrlParams(), ...this.$location.search()};
    }

    private registerLocationChangeListeners(): void {
        this.registerLocationChangeSuccessEventListener();
    }

    private registerLocationChangeSuccessEventListener(): void {
        // on location change listener
        this.$rootScope.$on('$locationChangeSuccess', (_, newUrl, oldUrl) => {
            const state = this.currentParams();
            this.logService.info('UrlStateManagerService', 'Location changed', `${oldUrl ?? ''} -> ${newUrl}`, state);
            this.stateSource$.next(state);
        });
    }
}