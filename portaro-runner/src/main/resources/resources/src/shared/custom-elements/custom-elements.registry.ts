import type {SvelteComponentConstructor} from 'core/types';
import CustomElementExample from 'shared/custom-elements/CustomElementExample.svelte';
import KpDefinedActionButton from 'shared/components/kp-defined-action-button/KpDefinedActionButton.svelte';
import KpActionRequestButton from 'shared/components/kp-action-request-button/KpActionRequestButton.svelte';
import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
import KpOpeningCalendar from 'shared/components/kp-opening-calendar/KpOpeningCalendar.svelte';
import KpLocaleSelect from 'shared/components/kp-locale/KpLocaleSelect.svelte';
import KpLoginLink from 'shared/components/kp-login-link/KpLoginLink.svelte';
import KpLandingPageStats from 'shared/components/kp-landing-page-stats/KpLandingPageStats.svelte';
import KpDocumentsCount from 'shared/components/kp-documents-count/KpDocumentsCount.svelte';
import KpGlobalSearchInput from 'shared/global-search/KpGlobalSearchInput.svelte';
import KpSearchedRecordsTiles from '../../features/record/kp-searched-records-tiles/KpSearchedRecordsTiles.svelte';
import SutorRecordInfoFondChip from 'src/features/sutor/pages/record-detail/components/SutorRecordInfoFondChip.svelte';
import KpScrollUpButton from 'shared/components/kp-scroll-up-button/KpScrollUpButton.svelte';
import SutorHomePage from 'src/features/sutor/pages/homepage/SutorHomePage.svelte';
import KpClickCopyButton from 'shared/components/kp-click-copy-button/KpClickCopyButton.svelte';

export interface CustomElementsRegistryEntry {
    name: string;
    componentConstructor: SvelteComponentConstructor;
    alternativeNames?: string[];
    propsNames?: string[];
}

export const CUSTOM_ELEMENTS_REGISTRY: CustomElementsRegistryEntry[] = [
    {
        name: 'custom-element-wrapper',
        componentConstructor: CustomElementExample,
        propsNames: ['cislo', 'text', 'bool', 'objekt']
    },
    {
        name: 'kp-defined-action-button',
        componentConstructor: KpDefinedActionButton,
        propsNames: ['action', 'buttonStyle', 'buttonSize', 'isBlock', 'isDisabled', 'title', 'additionalClasses', 'id']
    },
    {
        name: 'kp-action-request-button',
        alternativeNames: ['kp-action-request-button-v2'],
        componentConstructor: KpActionRequestButton,
        propsNames: ['path', 'requestMethod', 'requestBody', 'buttonStyle', 'buttonSize', 'isBlock', 'isDisabled', 'title', 'additionalClasses', 'id']
    },
    {
        name: 'kp-button-style-anchor',
        componentConstructor: KpButtonStyleAnchor,
        propsNames: ['href', 'buttonStyle', 'buttonSize', 'isBlock', 'isDisabled', 'title', 'additionalClasses', 'id', 'target']
    },
    {
        name: 'kp-opening-calendar',
        componentConstructor: KpOpeningCalendar,
        propsNames: ['calendarData', 'calendarDataSource', 'showWeekend', 'departmentId']
    },
    {
        name: 'kp-locale-select',
        componentConstructor: KpLocaleSelect,
        propsNames: ['locale']
    },
    {
        name: 'kp-login-link',
        componentConstructor: KpLoginLink,
        propsNames: ['additionalClasses']
    },
    {
        name: 'kp-landing-page-stats',
        componentConstructor: KpLandingPageStats
    },
    {
        name: 'kp-documents-count',
        componentConstructor: KpDocumentsCount
    },
    {
        name: 'kp-global-search-input',
        componentConstructor: KpGlobalSearchInput,
        propsNames: ['inputId', 'additionalInputClasses', 'autofocusOrCompactOpened', 'additionalButtonClasses', 'compact', 'placeholder']
    },
    {
        name: 'kp-searched-records-tiles',
        componentConstructor: KpSearchedRecordsTiles,
        propsNames: ['type', 'cache', 'maxCount', 'department', 'searchQuery', 'orderBy']
    },
    {
        name: 'kp-scroll-up-button',
        componentConstructor: KpScrollUpButton,
        propsNames: ['topThreshold']
    },
    {
        name: 'kp-click-copy-button',
        componentConstructor: KpClickCopyButton
    },

    // SUTOR
    {
        name: 'sutor-record-info-fond-chip-tag',
        componentConstructor: SutorRecordInfoFondChip,
        propsNames: ['chipSize', 'fondId', 'fondName', 'value']
    },
    {
        name: 'sutor-landing-page',
        componentConstructor: SutorHomePage
    }
];