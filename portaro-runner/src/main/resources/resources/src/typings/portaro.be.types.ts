import type {Conjunction, Restriction} from '../features/search/search-criteria/criteria.types';
import type {AnyObject, EnumValues, NonNullableProperties, TypeOrIdentifiedStub} from 'typings/portaro.fe.types';
import type {ValueEditorBindings} from 'shared/value-editors/kp-value-editor/types';
import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
import type {ContraDebtablePaymentItemRequest} from '../features/payment/types';
import type {TableRow} from '../features/record/kp-record-detail-table/types';
import type {FileViewForm, Kind, SearchType, State, Subkind, UserType} from 'shared/constants/portaro.constants';
import type {SeekingIntentId, SeekingProvisionIntentId} from '../features/ill/seeking-constants';
import type {UserId} from 'src/features/user/types';

// Branded types - this UUID string is different from all other string types (https://egghead.io/blog/using-branded-types-in-typescript)
export type UUID<T extends string = string> = T & {__brand: 'UUID'}; // generics to support literal type UUIDs (UUID<'005e8650-3e13-4c02-ba6c-a46d680d4bf1'>)

export interface Identified<ID> {
    id: ID;
}

export interface Named {
    name: string;
}

export interface Labeled {
    text: string;
}

export type LabeledIdentified<ID> = Identified<ID> & Labeled;

export type NamedIdentified<ID> = Identified<ID> & Named;

export type NamedLabeledIdentified<ID> = Identified<ID> & Labeled & Named;

export type NamedLabeled = Labeled & Named;

export interface LabeledValue<V> extends Labeled, Valuable<V> {
}

export interface LabeledReference<ID> extends LabeledIdentified<ID> {
    kind: Kind;
}

export interface FondedRecordLabeledReference extends LabeledReference<UUID> {
    kind: Kind.KIND_RECORD;
    fond: SimpleFond;
}

export type FieldValue = string | number | Identified<string> | Rec | null;

// @ts-ignore overwrite string literals type with string
export interface CustomValueEditorBindings extends ValueEditorBindings {
    type: string;
}

export interface FieldType extends LabeledIdentified<FieldTypeId> {
    code: string;
    repeatable: boolean;
}

export interface Document extends Rec {
    subtitle: Labeled[];
    responsibilityStatement?: Labeled;
    primaryAuthors: <AUTHORS>
    isbns?: LabeledValue<string>[];
    issns?: LabeledValue<string>[];
    publishers: Labeled[];
    publicationStartYear: LabeledValue<number>;
    tocUrl: string;
    tocContent: string;
}

// SimpleFondResponse.java
// eslint-disable-next-line
export interface SimpleFond extends LabeledIdentified<number> {
}

// RichFondResponse.java
export interface Fond extends SimpleFond, NamedLabeledIdentified<number> {
    order: number;
    parentId?: number;
    entryFieldTypeConfigId: FieldTypeId;
    editable: boolean;
    enabled: boolean;
    ofAuthority: boolean;
    forSourceDocument: boolean;
    z39Typ?: number;
    periodical: boolean;
    documentCategories: string;
    documentTypes: string;
    withExemplars: boolean;
    recordType?: string;
    bibliographicLevel?: string;
    threadable: boolean;
}

export interface PermissionResult {
    allowed: boolean;
    reason?: Labeled;
}

export interface ImageDimensions {
    width: number;
    height: number;
}

export interface IdentifiedFile extends Resource, LabeledIdentified<number> {
    dimensions?: ImageDimensions;
}

export interface ViewableFile extends IdentifiedFile {
    showPermission: PermissionResult;
    printPermission: PermissionResult;
    exportPermission: PermissionResult;
}

export interface Resource {
    hasTextualForm: boolean;
    name: string;
    directory?: Directory;
    viewForms: FileViewForm[];
    category: FileCategory;
    accessType: FileAccessType;
    filename: string;
    size: number;
    source: string;
    creatorId: number;
    url: string;
    creationDate: string;
}

export interface Directory extends NamedLabeledIdentified<number> {
    parentDirectoryId: number;
    order: number;
    accessType: FileAccessType;
}

export interface DirectoryNode extends Directory {
    children: DirectoryNode[];
}

export enum FileCategoryId {
    COVER = 1
}

export interface FileCategory {
    id: number;
    name: string;
    acceptableFileTypes: string[];
    text: string;
}

export interface FileAccessType {
    text: string;
    id: number;
}

export interface Valuable<V> {
    value: V;
}

export interface Loan extends Identified<string> {
    lendDate: string;
    chunkable: boolean;
    department: Department;
    designation: string;
    document: Document;
    dueDate?: string;
    endDate: string;
    estimatedAvailableDate?: string;
    exemplar?: Exemplar;
    endingLoan: boolean;
    expiredLoan: boolean;
    inQueue: boolean;
    lastRenewalDate?: string;
    loanCategory: LoanCategory;
    loanPeriodDays?: number;
    loanRealizationId: number;
    loanRequestId: number;
    loanType: LoanType;
    location: Location;
    orderDate: string;
    overdueNoticesCount: number;
    queueNumber: number;
    queueOrder: number;
    requestCancelDate: string;
    requestDate: string;
    reservationDate: string;
    reservationDeadlineDate?: string;
    state: EnumToLabelledIdentified<LoanState> & {finished: boolean};
    stateText: Text;
    text: Text;
    user: BasicUser;
    desiredDepartments: Department[];
    timeslotOccupations: TimeslotOccupation[];
    quantity: number;
    renewability: Renewability;
}

export interface LoanReminder extends Identified<number> {
    loan: Loan;
    type: LabeledIdentified<number>;
    generationDate: string;
    sentDate?: string;
}

export interface TimeslotOccupation extends Identified<UUID> {
    dateRange: DateRange;
}

export interface Renewability {
    renewable: boolean;
    enabled: boolean;
    numberOfDays: number;
    text: string;
}

export type EnumToLabelledIdentified<T> = {id: keyof T, text: string};

export enum LoanState {
    // Pozadavek na rezervaci (neboli nepotvrzena rezervace)
    ACTIVE_NOT_APPROVED_REQUEST = 'ACTIVE_NOT_APPROVED_REQUEST',

    // Zruseno ve fazi pozadavku
    CANCELLED_NOT_APPROVED_REQUEST = 'CANCELLED_NOT_APPROVED_REQUEST',

    // Rezervace v poradi
    ON_WAITING_LIST = 'ON_WAITING_LIST',

    // Neodeslaná rezervace
    UNSENT_RESERVATION = 'UNSENT_RESERVATION',

    // Odeslana rezervace
    SENT_RESERVATION = 'SENT_RESERVATION',

    // Nevyrizena objednavka
    UNPROCESSED_ORDER = 'UNPROCESSED_ORDER',

    // Vyrizena objednavka
    PROCESSED_ORDER = 'PROCESSED_ORDER',

    // Vypujcka
    LENT = 'LENT',

    // Vracena vypujcka
    RETURNED = 'RETURNED',

    // Zrusena rezervace
    CANCELLED_RESERVATION = 'CANCELLED_RESERVATION',

    // Neznamy stav
    UNKNOWN = 'UNKNOWN'
}

export type LoanType = LabeledIdentified<number>

export interface Message extends Identified<UUID> {
    topic: EnumToLabelledIdentified<MessageTopic>;
    severity: EnumToLabelledIdentified<MessageSeverity>;
    senderUser: BasicUser;
    targetUser: BasicUser;
    creationEvent: Event;
    confirmationNecessary: boolean;
    content: string;
    contentType?: string;
    directoryId?: number;
}

export enum MessageTopic {
    COMMON = 0,
    LOAN_COMMON = 1000,
    REMINDER = 1100,
    ORDER = 1200,
    PRE_REMINDER = 1300,
    USER_COMMON = 2000,
    USER_REGISTRATION = 2100,
    REGISTRATION_CREATION = 2110,
    MESSAGE_FROM_READER = 3000,
    LIBRARY_NOTIFICATION = 5000,
    ADVERTISEMENT_COMMON = 6000,
    ERROR_COMMON = 8000
}

export enum MessageSeverity {
    INFO = 'info',
    WARNING = 'warning',
    URGENT = 'urgent'
}

export enum MessageMedium {
    UNDEFINED = 'undefined',
    INTERNAL = 'internal',
    POST = 'post',
    SMS = 'sms',
    EMAIL = 'email'
}

export enum MessageStatus {
    SENT = 'sent',
    UNSENT = 'unsent'
}

export interface MessageSending extends Identified<UUID> {
    message: Message;
    messageMedium: EnumToLabelledIdentified<MessageMedium>;
    sendingEvent?: Event;
    receiptionEvent?: Event;
    confirmationEvent?: Event;
}

export interface MessageSendingEmail extends MessageSending {
    recipientEmailAddress: string;
    senderEmailAddress: string;
    subject: string;
    body: string;
    attachmentDirectoryId?: number;
    errorMessage?: string;
}

export interface MessageSendingPost extends MessageSending {
    address: string;
    fulltextId?: number;
    printingEvent?: Event;
}

export interface MessageSendingSms extends MessageSending {
    recipientPhoneNumber: string;
    content: string;
    errorMessage?: string;
}

export interface MessageSendingInternal extends MessageSending {
    body: string;
}

export interface Event extends Identified<UUID> {
    id: UUID;
    code: string;
    createDate: string;
    sessionId?: string;
    initiatorUser?: BasicUser;
    department?: Department;
    data?: string;
}

export interface BasicUser extends LabeledIdentified<number> {
    evided: boolean;
}

export interface AuthableUser extends BasicUser {
    role: string[];
}

export interface User extends AuthableUser {
    rid: UUID;
    userRoles: UserRole[];
    username: string;
    addresses: UserAddress[];
    contacts: Contact[];
    representedUsers: BasicUser[];
    authority: NamedLabeledIdentified<number>;
    /** @deprecated use {@link readerAccounts} */
    readerRole?: ReaderRole;
    readerAccounts: ReaderRole[];
    editorAccounts: EditorAccount[];
    supplierAccounts: SupplierRole[];
    externalUserRole: any;
    email: string;
    readableDepartments?: Department[];
    editableDepartments: Department[];
    deletionEventId?: string;
    deleted: boolean;
    sigla?: string;
    schoolClass: string;
    recordId?: UUID;
    userServiceProperties: UserServiceProperty[];
}

export interface Person extends User {
    firstName: string;
    lastName: string;
    degree: string;
    suffixDegree: string;
    openidId: string;
    guId: string;
    netId: string;
    birthDate: string;
    job: string;
    jobAddress: string;
    educationLevel: string;
    schoolClass: string;
    identityCardNumber: string;
    mojeIdLastUpdateDate: string;
    mojeIdValid: number;
    bakalari: string;
    solId: string;
    idCards: IdCard[];
}

export interface IdCard {
    id: number;
    userId: UserId;
    type: LabeledIdentified<number>;
    value: string;
    source: LabeledIdentified<string>;
    validityEndDate?: string;
    issuingCountry?: string;
    issuer?: string;
    issueDate?: string;
}

export interface Auth {
    role: string[];
    activeUser: AuthableUser;
    evided: boolean;
    editableDepartments: Department[];
}

export interface UserServiceProperty extends Identified<UUID> {
    userId: UserId;
    service: string;
    name: string;
    value: string;
    validityEndDate?: string;
}

export interface UserServicePropertySearch {
    service: string;
    name?: string;
    value?: string;
}

export type UserRole = ReaderRole | EditorAccount | SupplierRole | AdministratorRole;

export interface ReaderRole extends Identified<number> {
    deleted: boolean;
    blocked: boolean;
    cardNumber?: string;
    barCode?: string;
    registrationDate: string;
    registrationExpirationDate?: string;
    registrationExpired: boolean;
    readerCategory: ReaderCategory;
    department: Department;
    reservationsPrintType: PrintType;
    overdueNoticesPrintType: PrintType;
    note?: string;
    librarianMessage?: string;
    rfid?: string;
}

export interface EditorAccount extends Identified<number> {
    withServicePrivileges: boolean;
    validationCode?: string;
    group?: LibrarianGroup;
    editLevel: EditLevel;
    active: boolean;
}

export type SupplierRole = Identified<number>;

export type AdministratorRole = Identified<number>;

export enum DepartmentEditationMode {
    OVERWRITE_ALL = 'OVERWRITE_ALL',
    MERGE_DUPLICATES_OR_APPEND = 'MERGE_DUPLICATES_OR_APPEND'
}

export interface UserEditationRequest extends Identified<number> {
    kind: UserKind;
    active: boolean;
    authority?: NamedLabeledIdentified<number>;
    readerAccounts?: ReaderAccountEditationRequest[];
    editorAccounts?: EditorAccountEditationRequest[];
    supplierAccounts?: SupplierAccountEditationRequest[];
    emails?: UserEmailEditationRequest[];
    phoneNumbers?: PhoneNumberEditationRequest[];
    addresses?: UserAddressEditationRequest[];
    username?: string;
    newPassword?: string;
    readableDepartments?: Department[];
    readableDepartmentsEditationMode?: DepartmentEditationMode;
    editableDepartments?: Department[];
    editableDepartmentsEditationMode?: DepartmentEditationMode;
    editableFonds?: Fond[];
}

export interface UploadedUserFilesRequest {
    files: File[],
    user: User,
    category?: FileCategory;
    accessType?: FileAccessType;
}

export interface ReaderAccountEditationRequest {
    deleted?: boolean;
    blocked?: boolean;
    registrationDate?: string;
    registrationExpirationDate?: string;
    readerCategory?: ReaderCategory;
    originDepartment?: Department;
    reservationsPrintType?: PrintType;
    overdueNoticesPrintType?: PrintType;
    cardNumber?: string;
    barCode?: string;
    note?: string;
    librarianMessage?: string;
    rfid?: string;
}

export interface EditorAccountEditationRequest {
    withServicePrivileges?: boolean;
    validationCode?: string;
    group?: LibrarianGroup;
    editLevel?: EditLevel;
    active?: boolean;
}

export type LibrarianGroup = NamedLabeledIdentified<number>

export interface EditLevel extends Valuable<number>, Labeled {
}

export type SupplierAccountEditationRequest = AnyObject;

export interface UserEmailEditationRequest {
    value?: string;
    source: LabeledIdentified<string>;
}

export interface PhoneNumberEditationRequest {
    value: string;
    smsCapable: boolean;
    source: LabeledIdentified<string>;
}

export interface UserAddressEditationRequest {
    permanent?: boolean;
    mailing?: boolean;
    source?: string;
    street?: string;
    city?: string;
    state?: string;
    postalCode?: string;
}

export enum PrintType {
    POST = 0,
    EMAIL = 1,
    SMS = 2,
    EMAIL_AND_SMS = 3
}

export interface ReaderCategory extends NamedLabeledIdentified<string> {
    registrationFee: number;
    registrationDurationMonths: number;
    lendingAllowed: boolean;
    remindingActive: boolean;
    loanCountLimit?: number;
    registrationToDateInFormatDDMM?: string;
    internetSessionLimitMinutes?: number;
    groupable: boolean;
    reservationsCountLimit: number;
    ordersCountLimit: number;
}

export enum UserKind {
    USER = 'USER',
    PERSON = 'PERSON',
    LIBRARY = 'LIBRARY',
    INSTITUTION = 'INSTITUTION',
    FAMILY = 'FAMILY',
    SOFTWARE = 'SOFTWARE'
}

export interface UserAddress extends Identified<number> {
    permanent: boolean;
    mailing: boolean;
    address: Address;
    source?: LabeledIdentified<string>;
}

export interface Address extends LabeledIdentified<number> {
    street: string;
    city: string;
    postalCode: string;
    country: LabeledIdentified<string>;
}

export interface Contact extends Identified<number> {
    userId: UserId;
    type: {
        id: ContactType,
        text: string;
    };
    source: LabeledIdentified<string>;
    value: string;
}

export enum ContactType {
    SMS_PHONE = 0,
    PHONE = 1,
    EMAIL = 2,
    ID_CARD = 10,
    P_CARD = 11,
    DL_CARD = 12,
    IR_CARD = 13,
    VS_CARD = 14,
    PS_CARD = 15,
    IX_CARD = 16,
    IE_CARD = 17,
    OP_CARD = 18,
    CA_CARD = 19,
    UNKNOWN_CARD = 20,
    ISIC_CARD = 21,
    PAYMENT_ACCOUNT = 30,
    BIRTH_NUMBER = 40,
}

export type DepartmentDescriptor = NamedLabeledIdentified<number>

export interface Department extends DepartmentDescriptor {
    order: number;
    root: boolean;
    exemplarable: boolean;
    parentId: number;
    sigla: string;
    online: boolean;
    central: boolean;
}

export interface Location extends NamedLabeledIdentified<number>, Ordered {
    code: string;
    departments: Department[];
    editable: boolean;
    deletable: boolean;
}

export interface ListResult<ITEM> {
    totalElements: number;
    content: ITEM[];
}

export interface ActionResponse {
    responseType: string;
    finished: boolean;
}

export interface ActionResponseWithText extends ActionResponse {
    text: string;
}

export interface ActionResponseWithActionLabel extends ActionResponse {
    actionText: string;
}

// eslint-disable-next-line
export interface FinishedActionResponse extends ActionResponseWithText {
}

export interface FinishedSaveResponse<T> extends ActionResponseWithText {
    savedObject: T;
}

export interface ExceptionActionResponse extends ActionResponseWithText, ActionResponseWithActionLabel {
    simpleName: string;
    message: string;
    severity: number;
}

export interface FormPackage<E> extends ActionResponseWithText {
    formSetting: KpUniversalFormSettings<E>;
    formObject: E;
}

export interface FieldEnablingExceptionContinuation extends ExceptionActionResponse {
    continuation: {
        fieldToEnable: string;
        confirmationText: string;
    };
}

export interface SettingTypeIdContainingException extends ExceptionActionResponse {
    text: string;
    settingTypeId: string;
}

export interface UserBalanceIsNotSufficientException extends ExceptionActionResponse {
    payer: BasicUser;
    provider: string;
    amounts: ContraDebtablePaymentItemRequest[];
}

export interface DiscountRequestShowException extends ExceptionActionResponse {
    user: User;
}

export interface ActionRequest {
    path: string;
    method: string;
    body: string;
}

export interface UserPreferencesException extends ExceptionActionResponse {
    text: string;
    userPreferenceKeys: number[];
}

export interface RedirectionResponse extends ActionResponseWithText {
    url: string;
    strategy: 'AUTO' | 'CURRENT_WINDOW' | 'NEW_WINDOW';
}

export interface LoanRequestFormActionResponse extends ActionResponse {
    responseType: 'LoanRequestForm';
    finished: false;
    loanRequestOptions: LoanRequestOption[];
    originalRequestItems: LoanRequestItem[];
}

export interface LoanRequestOption<CAPABILITY extends Capability = Capability> {
    capability: CAPABILITY;
    requestItems: LoanRequestItem[];
    formObject: CapabilityNameToLoanRequestMap[CAPABILITY['name']];
    form: KpUniversalFormSettings<Record<string, any>>;
    responses?: ActionResponseWithText[]; // this is added dynamically on frontend
}

export interface FinishedReturningActionResponse extends FinishedActionResponse {
    responseType: 'FinishedReturningResponse';
}

export interface FinishedLendingActionResponse extends FinishedActionResponse {
    responseType: 'FinishedLendingResponse';
}

export interface Form {
    id: any;
    text: string;
    fields: EditedProperty[];
    header: string;
    footer: string;
}

export interface EditedProperty {
    label: string;
    fieldName: string;
    editor: ValueEditorBindings;
}

export interface DocumentAvailability extends Availability {
    freeItemsDepartments: DepartmentDescriptor[];
    reservableItemsDepartments: DepartmentDescriptor[];
    returnedToday: boolean;
    freeItemsCount: number;
    freeTakeawayItemsCount: number;
    obtainableItemsCount: number;
    reservationsInQueueCount: number;
    reservationsCount: number;
    freeVisitableItemsCount: number;
    lastUserLoanDate: string;
}

export interface Availability extends Identified<string>, PlainAvailability {
    obtainable: boolean;
    capabilities: Capability[];
}

export interface PlainAvailability {
    known: boolean;
    reasonable: boolean;
    reservable: boolean;
    orderable: boolean;
    pickupable: boolean;
    free: boolean;
}

export interface Capability {
    name: CapabilityName;
    loginRequired: boolean;
}

export type CapabilityName =
    'slotVisitOrderability' |
    'mailRequestability' |
    'standardVisitReservability' |
    'visitPickupability' |
    'standardVisitOrderability' |
    'pickupability' |
    'flexibooksLoanability' |
    'downloadability' |
    'palmknihyLoanability' |
    'palmknihyAudioLoanability' |
    'palmknihyPdfLoanability' |
    'levnaknihovnaLoanability' |
    'seekingProvidability' |
    'standardReservability' |
    'standardOrderability';

export interface CapabilityOnBuildings extends Capability {
    buildings: Department[];
}

export interface ExternalLoanLoanability extends Capability {
    externalLoanRecordId: number;
    url: string;
}

export interface StandardOrderability extends CapabilityOnBuildings {
    name: 'standardOrderability';
}

export interface StandardReservability extends CapabilityOnBuildings {
    name: 'standardReservability';
}

export interface StandardVisitOrderability extends CapabilityOnBuildings {
    name: 'standardVisitOrderability';
}

export interface StandardVisitReservability extends CapabilityOnBuildings {
    name: 'standardVisitReservability';
}

export interface SlotVisitOrderability extends CapabilityOnBuildings {
    name: 'slotVisitOrderability';
    timeslots: RequestableTimeslot[];
}

export interface Downloadability extends Capability {
    name: 'downloadability';
    links: Link[];
}

export interface Pickupability extends CapabilityOnBuildings {
    name: 'pickupability';
}

export interface VisitPickupability extends CapabilityOnBuildings {
    name: 'visitPickupability';
}

export interface FlexibooksLoanability extends ExternalLoanLoanability {
    name: 'flexibooksLoanability';
}

export interface LevnaknihovnaLoanability extends ExternalLoanLoanability {
    name: 'levnaknihovnaLoanability';
}

export interface PalmknihyLoanability extends ExternalLoanLoanability {
    name: 'palmknihyLoanability';
}

export interface PalmknihyAudioLoanability extends ExternalLoanLoanability {
    name: 'palmknihyAudioLoanability';
}

export interface PalmknihyPdfLoanability extends ExternalLoanLoanability {
    name: 'palmknihyPdfLoanability';
}

export interface SeekingProvisionCapability extends Capability {
    name: 'seekingProvidability';
}

export interface MailRequestability extends Capability {
    name: 'mailRequestability';
}

interface CapabilityNameToLoanRequestMap {
    mailRequestability: MailLoanRequestRequest;
    seekingProvidability: ProvidedSeekingRequestCreationRequest;
    pickupability: void;
    visitPickupability: void;
    standardOrderability: StandardOrderRequest;
    standardVisitOrderability: StandardOrderRequest;
    standardReservability: StandardReservationRequest;
    standardVisitReservability: StandardReservationRequest;
    slotVisitOrderability: SlotOrderRequest;
    downloadability: void;
    palmknihyLoanability: ExternalLendingRequest;
    palmknihyAudioLoanability: ExternalLendingRequest;
    palmknihyPdfLoanability: ExternalLendingRequest;
    levnaknihovnaLoanability: ExternalLendingRequest;
    flexibooksLoanability: ExternalLendingRequest;
}

export type LoanRequestTypes = CapabilityNameToLoanRequestMap[keyof CapabilityNameToLoanRequestMap];

export type LoanRequestItem = ({
    document: Document;
    exemplar?: Exemplar;
} | {
    document?: Document;
    exemplar: Exemplar;
}) & {
    fromDate?: string,
    toDate?: string;
};

export interface LoanRequest {
    items: LoanRequestItem[];
}

export interface NotAnonymousLoanRequest extends LoanRequest {
    requester: User;
}

export interface DocumentCapabilitySlotsRequest {
    document: Rec;
    requester: User;
    requestDepartment: Department;
    scopeDateRange: DateRange;
}

export interface StandardLoanRequest extends NotAnonymousLoanRequest {
    desiredBuildings: Department[];
    deadline: string;
    forVisitation: boolean;
    ignoreDocumentWasLoanedInPast: boolean;
}

export interface StandardOrderRequest extends StandardLoanRequest {
    desiredBuilding: Department;
}

export interface ExternalLendingRequest {
    externalLoanRecordId: number;
    requester: User;
}

export type StandardReservationRequest = StandardLoanRequest;

export interface SlotOrderRequest extends StandardOrderRequest {
    dateRange: DateRange;
}

export type ProvidedSeekingRequestCreationRequest = NotAnonymousLoanRequest;

export type MailLoanRequestRequest = LoanRequest;

export interface RequestableTimeslot extends Identified<string> {
    from: string;
    to: string;
    orderable: boolean;
}

export interface Exemplar extends LabeledIdentified<number> {
    accessNumber?: string;
    account: unknown;
    acquisitionWay: AcquisitionWay;
    acquisitionYear: number;
    attachments: string;
    barCode: string;
    binding: boolean;
    bindingIssueRange: string;
    bundledVolume: boolean;
    bundledVolumeIssueRange: string;
    bundledVolumeNumber: string;
    bundledVolumeYear: string;
    creationDate: string;
    customValue: NamedLabeledIdentified<string>;
    department: Department;
    designation: string;
    designationProperty: string;
    discarded: boolean;
    discardionId: number;
    fileDirectory: Directory;
    firstExistingDesignationProperty: string;
    fond: Fond;
    internalNote: string;
    invoiceItem: number;
    invoiceNumber: string;
    issue: boolean;
    issueEvidenceNumber: string;
    issueName: string;
    lastModificationDate: string;
    loanCategory: LoanCategory;
    location: Location;
    note: string;
    order: number;
    owner: string;
    price: Price;
    quantity: number;
    recordId: UUID;
    requestable: boolean;
    signature: string;
    signatureSorter: string;
    status: ExemplarStatus;
    supplier: string;
    thematicGroup: ThematicGroup;
    type: ExemplarType;
    vazbaId: number;
    volumeId: number;
    volumeNumber: string;
}

export interface ExemplarWithDocumentAndDiscardion extends Exemplar, Discardion {
    documentIsbns: Labeled[];
    documentMainAuthors: <AUTHORS>
    record: LabeledReference<string>;
    discardionDiscardNumber?: string;
}

export interface Dimensions {
    width: number;
    height: number;
}

export interface Regal {
    map: Int8Array;
    mapWidth: number;
    mapHeight: number;
    information: string;
    withMap: boolean;
    dimensions?: Dimensions;
}

export interface RecordExport extends NamedLabeledIdentified<string> {
    recordOperationTypes: RecordOperationType[] | null;
    fonds: Fond[] | null;
    withPerio: boolean;
    withNonperio: boolean;
    withNonexemplared: boolean;
    forbiddenRecordIds: UUID[] | null;
    sqlClause: string | null;
}

export interface ExemplarStatus extends NamedLabeledIdentified<number> {
    lendable: boolean;
    reservable: boolean;
    discarded: boolean;
}

export interface Discardion extends Identified<number> {
    exemplar: Exemplar;
    discardNumber: string;
    reason: string;
    replacementWay: ReplacementWay;
    cost: number;
    consultationNumber: string;
}

export type ReplacementWay = NamedLabeledIdentified<string>

export interface ThematicGroup extends NamedLabeledIdentified<number> {
    order: number;
}

export interface LoanCategory extends NamedLabeledIdentified<string> {
    lendable: boolean;
    onSite: boolean;
    renewable: boolean;
    circular: boolean;
    reminderable: boolean;
    reservable: boolean;
    forDislocation: boolean;
    chunkable: boolean;
    returningImmediately: boolean;
}

export interface AcquisitionWay extends NamedLabeledIdentified<string> {
    order: number;
}

export enum ExemplarType {
    EXEMPLAR = 0,
    ISSUE = 1,
    BINDING = 2
}

export interface ViewableExemplar extends Exemplar {
    availability: AppserverDataBackedExemplarAvailability;
    holder?: LabeledIdentified<number> | undefined;
    holderShowable: boolean;
    holderDetailShowable: boolean;
    editable: boolean;
    discardable: boolean;
    deletable: boolean;
    discardionRestorable: boolean;
}

export interface AppserverDataBackedExemplarAvailability extends Availability {
    text: string;
    lentToDate: string;
    errorNumber: number;
    errorText: string;
    appserverData: ExemplarAvailabilityAppserverData;
    lent: boolean;
    error: boolean;
}

export interface ExemplarAvailabilityAppserverData {
    error: boolean;
    errors: AppserverError[];
    lentToDate: string;
}

export interface AppserverError {
    error: boolean;
    errorNumber: number;
    errorMessage: string;
}

export interface EditableFieldList {
    fields: EditableField[];
}

export interface RecordEditation extends Identified<string>, EditableFieldList {
    authority: boolean;
    draft: boolean;
    deleted: boolean;
    fond: Fond;
    text: string;
    revisionSaved: boolean;
    fondSelectionEditor: ValueEditorBindings;
    showIndicators: boolean;
    showFieldIdentifiers: boolean;
    directoryId: any;
    recordId: UUID;
    addableFieldTypes: EditableFieldType[];
    usedFieldTypes: EditableFieldType[];
}

export interface WithCode {
    code: string;
}

export interface EditableField extends LabeledIdentified<UUID>, WithCode, EditableFieldList {
    idPath: string;
    fieldTypeText: string;
    recordLink: FondedRecordLabeledReference | null;
    typeId: FieldTypeId;
    value?: FieldValue;
    addableFieldTypes?: EditableFieldType[];
}

export interface EditableFieldType extends FieldType {
    reorderable: boolean;
    displayType: FieldDisplayType;
    editNote: string;
    entryElement: boolean;
    subfieldTypes: EditableFieldType[];
    singleLinkedRecordSubfieldTypeId?: FieldTypeId;
    editor?: ValueEditorBindings;
}

export interface FieldDisplayType extends LabeledIdentified<number>, Identified<number> {
}

export interface Rec extends NamedLabeledIdentified<UUID>, FieldList {
    type: Subkind;
    fond: Fond;
    valid: boolean;
    external: boolean;
    status: RecordStatus;
    detailTableRows: TableRow[];
    directoryId: number;
    cover: IdentifiedFile;
    lastUpdateDate: string;
    alternativeNames: string[];
    deleted: boolean;
    active: boolean;
    seeAlsos?: LabeledIdentified<string>[];
    exports?: AnyObject;
    periodical: boolean;
    exemplarable: boolean;
    downloadable: boolean;
    volumeable: boolean;
    withForeignOccurrences: boolean;
    threadId?: UUID;
}

export type RecordStatus = LabeledIdentified<number>

export interface RecordHolding extends Identified<UUID> {
    recordId: UUID;
    department: Department;
    discardionEventId?: string;
    deletionEventId?: string;
}

export interface RecordRevision {
    record: LabeledIdentified<string>;
    date: string;
    content: string;
}

export interface ReferenceFieldValue {
    recordId: UUID;
}

export type ContentFieldValue<E> = Valuable<E>;

export type SetFieldValueRequest = ReferenceFieldValue | ContentFieldValue<any>;

export interface RecordSearchParams extends StaticSearchParams {
    kind: [Kind.KIND_RECORD];
    subkind?: Subkind;
    rootFond?: TypeOrIdentifiedStub<Fond>[];
    focusedFieldTypeId?: FieldTypeId;
    name?: string;
    onlyDrafts?: boolean;
}

export interface RecordCreateParams {
    kind?: [Kind.KIND_RECORD];
    subkind?: Subkind;
    fond?: Fond;
    rootFond?: Fond;
    focusedFieldTypeId?: FieldTypeId;
    initialValues?: Record<FieldTypeId, SetFieldValueRequest>;
}

export interface FieldList {
    fields: ViewableField[];
}

export interface RecordOperation extends Identified<any> {
    user: BasicUser;
    recordOperationType: RecordOperationType;
    date: string;
    department: Department;
    record: Rec;
}

export type RecordOperationType = NamedLabeledIdentified<number>

export interface ForeignOccurrence extends Identified<number> {
    databaseId: number;
    databaseName: string;
    itemUrl: string;
}

export interface SearchHistoryEntryDto {
    date: string;
    title: string;
    url: string;
    totalElements: number;
    qt: Restriction;
}

export interface RawBodyMailSendRequest {
    recipientEmails?: string[];
    subject?: string;
    body?: string;
    attachments: IdentifiedFile[];
}

export interface ExactUserRawBodyMailSendRequest {
    recipients: User[];
    subject?: string;
    body?: string;
    attachments: IdentifiedFile[];
}

export interface SmsSendRequest {
    recipients: User[];
    body: string;
}

export interface Ordered {
    order: number;
}

export interface OrderedNamedIdentified<ID> extends NamedIdentified<ID>, Ordered {

}

export interface ValueEditorProvider {
    editor: ValueEditorBindings;
}

export interface ViewableSearchField extends LabeledIdentified<string>, ValueEditorProvider {
}

export interface Facet extends LabeledIdentified<string>, ValueEditorProvider {
}

export interface Comment extends Identified<string> {
    recordId: UUID;
    creator: number;
    date: string;
    content: string;
    deletable: boolean;
}

export interface CommentCreationRequest {
    creator?: number;
    record: string;
    content?: string;
}

export interface Rating extends Identified<number> {
    id: number;
    recordId: UUID;
    count: number;
    value: number;
    lastRatingDate: Date;
    formattedValue: string;
    result?: string;
}

export type Language = LabeledIdentified<string>;

export interface Category {
    id: string;
    text: string;
}

export interface Localization {
    code: string;
    depId: number;
    translations: {[key: string]: any};
}

export interface Translation {
    code: string;
    depId: number;
    language: string;
    value: string;
}

export interface Volume extends LabeledIdentified<number> {
    recordId: UUID;
    volumeNumber: string;
    issueQuantity: number;
    year: string;
    date: string;
    description: string;
    periodicity: VolumePeriodicity;
    periodicityMultiplier: number;
    withIssues: boolean;
}

export type VolumePeriodicity = LabeledIdentified<number>

export interface UserPreference {
    id: string;
    customized: boolean;
    editable: boolean;
    key: any;
    keyId: number;
    keyText: string;
    user: BasicUser;
    effectiveValue: any;
    editor: ValueEditorBindings;
    defaultValue: any;
    datatype: Datatype;
    customValue: any;
    state?: State;
    editedValue?: any;
}

export interface ViewableSettingChain<VALUE = any> {
    id: string;
    defaultValue: VALUE;
    name: string;
    section: string;
    description: string;
    datatype: Datatype;
    type: SettingTypeDto<VALUE>;
    chain: ViewableSettingValue<VALUE>[];
    effectiveSettingValue: VALUE;
    editor: ValueEditorBindings;
}

export interface SettingTypeDto<VALUE = any> {
    description: string;
    defaultValue: VALUE;
    datatype: Datatype;
}

export interface ViewableSettingValue<VALUE = any> extends Identified<string> {
    value: VALUE;
    note: string;
    department: LabeledIdentified<number>;
    fond: LabeledIdentified<number>;
    default: boolean;
    editable: boolean;
    overridable: boolean;
    effective: boolean;
    temporary?: boolean;
    newlyCreated?: boolean;
    errorText?: string;
}

export interface TransferableSettingsValue<VALUE = any> {
    section: string;
    name: string;
    departmentId: number;
    fondId: number;
    value: VALUE;
    note: string;
}

export interface Datatype {
    name: string;
    nestedDatatype?: Datatype;
}

export interface PortaroState {
    runningDays: number;
    version: string;
    versionDetail: string;
    coverSearchState: string[][];
    maxMemory: number;
    totalMemory: number;
    usedMemory: number;
    procesorsCount: number;
    operatingSystem: string;
    operatingSystemArchitecture: string;
    operatingSystemVersion: string;
    operatingSystemUsername: string;
    javaHome: string;
    javaVersion: string;
    processId: number;
    appserverUrl: string;
    appserverVersion: string;
    databaseDriverClassName: string;
    databaseUrl: string;
    startTime: string;
    licence: Licence;
}

export interface Licence {
    rootUrl: string;
    testing: boolean;
    editorLimit: number;
    documentLimit: number;
    licenceVersion: Valuable<string>;
    maintenanceExpirationDate: string;
    enabledModules: string[];
}

export interface ClientSession extends Identified<string> {
    sessionId: string;
    creationDate: string;
    ipAdress: IpAddress;
    userAgent: string;
    httpSessionId: string;
    requestEvents: LoginEvent[];
    currentDepartment: Department;
    endDate: string;
    internalAccess: boolean;
    bot: boolean;
}

export interface IpAddress {
    value: string;
    loopback: boolean;
}

export interface LoginEvent extends Identified<string> {
    creationDate: string;
    currentDepartment: Department;
    httpSessionId: string;
    initiatorUser: User;
    ipAddress: IpAddress;
    sessionId: string;
    url: string;
    userAgent: string;
}

export interface HierarchyTreeNode<ID> extends LabeledIdentified<ID> {
    level: number; // level numbering starts at 1
    type: string;
    current: boolean;
}

export interface HierarchyTree<ID, NODE extends HierarchyTreeNode<ID>> {
    nodes: NODE[];
}

export interface RecordHierarchyTreeNode extends HierarchyTreeNode<UUID> {
    type: string;
    see: boolean;
    seeAlso: boolean;
    parent: boolean;
    child: boolean;
}

export type RecordDescriptor = LabeledIdentified<UUID>

export interface DateRange {
    fromDate?: string,
    toDate?: string
}

export interface SearchParams {
    kind?: Kind[];
    type?: SearchType;
    pageNumber?: number;
    pageSize?: number;
    q?: string;
    department?: any[];
    sorting?: Sorting;
    facetRestriction?: Conjunction;
    prefix?: string;
    diacriticalPrefix?: string;
    afterPosition?: string;
    beforePosition?: string;
}

export const DOCUMENT_LEADER_CODE = 'dleader';
export const AUTHORITY_LEADER_CODE = 'aleader';
export const DOCUMENT_FIELD_TYPE_ID_NUMBER_PREFIX = 'd';
export const AUTHORITY_FIELD_TYPE_ID_NUMBER_PREFIX = 'a';

export type FieldTypeId = string & {__brand: 'FieldTypeId'};
export type FieldId = string & {__brand: 'FieldId'};
export type FieldCode = string;

export type WholeFieldSelector = FieldCode;
export type SubfieldedFieldSelector = {[key: FieldCode]: FieldSelector};
export type SingleFieldSelector = SubfieldedFieldSelector | WholeFieldSelector;
export type MultiFieldSelector = SingleFieldSelector[];
export type FieldSelector = SingleFieldSelector | MultiFieldSelector;

export interface StaticSearchParams extends SearchParams {
    kind: Kind[];
    type: SearchType;
    recordRelatedRecord?: UUID;
    fieldId?: FieldTypeId;
    qt?: string;
}

export interface UserSearchParams extends StaticSearchParams {
    userType: UserType;
    userServiceProp: UserServicePropertySearch;
}

export interface RemoteValidationDto<MODEL> {
    fieldName: string;
    value: MODEL;
    formModel?: AnyObject;
    id?: number;
}

export interface Validity {
    property: string;
    type: string;
    valid: boolean;
    message: string;
}

export interface ViewableSearch<PARAMS extends SearchParams, ITEM> extends Identified<UUID> {
    result: PagedParameteredResult<ITEM, PARAMS>;
    title: string;
    subtitle?: string;
    type: string;
    availableSortings: Sorting[];
    forms: Form[];
    availableExports: Link[];
    availableIntents: Intent[];
    defaultExpandedFacets: string[];
    features: string[];
}

export type Intent =
    GoToIntent
    | ModifySearchParamsIntent
    | PermissionedIdentifiedIntent<unknown>;

interface IntentBase {
    type: string;
}

export interface GoToIntent extends IntentBase {
    type: 'go-to';
    link: Link;
}

export interface ModifySearchParamsIntent extends IntentBase, Labeled {
    type: 'modify-search-params';
    newParams: SearchParams;
}

export interface PermissionedIdentifiedIntent<ID> extends IntentBase, Identified<ID> {
    type: 'identified-intent';
    id: ID;
    permission: {
        allowed: boolean
    };
}

export interface StaticLink {
    url: string;
    text: string;
}

export interface Link extends StaticLink {
    note?: string;
}

export interface HtmlLink extends StaticLink {
    class?: string;
    title?: string;
}

export interface Sorting extends LabeledIdentified<string> {
    field: string;
    asc: boolean;
}

export interface Pageable {
    pageSize: number;
    pageNumber: number;
}

export interface PagedParameteredResult<ITEM, PARAMS extends SearchParams> extends ListResult<ITEM>, Pageable {
    params: PARAMS;
    totalPages: number;
    numberOfElements: number;
    duration: number;
    first: boolean;
    last: boolean;
    lastItemPosition: string;
    availableFacets: Facet[];
}

export interface ViewableExemplarWithDocument extends ViewableExemplar {
    document: Document;
}

export interface ConfirmableRequest {
    confirmed?: boolean;
}

export interface ExemplarRequest extends ConfirmableRequest {
    id?: number;
    document: Document;
    volume?: Volume;
    department?: Department;
    location?: Location;
    identifiers?: ExemplarIdentifierRequest[];
    loanCategory?: LoanCategory;
    thematicGroup?: ThematicGroup;
    acquisitionWay?: AcquisitionWay;
    creationDate?: string;
    customValue?: NamedLabeledIdentified<string>;
    status?: ExemplarStatus;
    price?: Price;
    owner?: string;
    acquisitionYear?: number;
    note?: string;
    account?: unknown;
    internalNote?: string;
    invoiceItem?: number;
    invoiceNumber?: string;
    attachments?: string;
    order?: number;
    supplier?: string;
    type?: ExemplarType;
    quantity?: number;
    bundledVolumeNumber?: string;
    bundledVolumeYear?: string;
    bundledVolumeIssueRange?: string;
    issueName?: string;
    issueEvidenceNumber?: string;
}

export interface ExemplarIdentifierRequest {
    accessNumber?: string;
    signature?: string;
    barCode?: string;
}

export interface VolumeCreationRequest {
    id?: number;
    document: Document;
    volumeNumber: string;
    year: string;
    issueQuantity: number;
    description: string;
    periodicity: VolumePeriodicity;
    periodicityMultiplier: number;
}

export interface VolumeEditationRequest {
    volume: Volume;
    document: Document;
    volumeNumber: string;
    year: string;
    issueQuantity: number;
    description: string;
    periodicity: Identified<number> & Labeled;
    periodicityMultiplier: number;
}

export interface Levelable {
    level: string;
}

export interface Logger extends Levelable, Identified<string> {
}

export interface RelationsRequest {
    target: Identified<number>;
    representative?: Identified<number>;
    sources: Identified<number>[];
}

export interface ReportTemplate {
    id: string;
    name: string;
    description: string;
    exportLink: string;
}

export interface ServerFolder {
    id: string;
    name: string;
    reportTemplates: ReportTemplate[];
    folderSubItems: ServerFolder[];
}

export interface PrintParameter {
    id: string;
    name: string;
    helpText: string;
}

export interface Field007DocumentCategory extends Identified<UUID>, Ordered {
    code: string;
    description: string;
}

export interface Field007Code extends Identified<UUID> {
    position: number;
    documentCategoryCode: string;
    code: string;
    description: string;
    isDefaultValue: boolean;
}

export interface Field007Label extends Identified<UUID>, Ordered {
    documentCategoryCode: string;
    description: string;
}

export interface Field007Definitions {
    documentCategories: Field007DocumentCategory[];
    codes: Field007Code[];
    labels: Field007Label[];
}

export interface Field008Code extends Identified<UUID> {
    position: number;
    documentTypeCode: string;
    code: string;
    description: string;
    isDefaultValue: boolean;
}

export interface Field008StaticCode {
    code: string;
    description: string;
}

export interface Field008Position {
    label: string;
    codes: Field008StaticCode[];
}

export interface Field008PositionWithDictionaryValue {
    label: string;
    values: LabeledIdentified<string>[];
}

export interface Field008DocumentType extends Identified<number> {
    code: string;
    description: string;
}

export interface Field008Label extends Ordered {
    documentTypeCode: string;
    description: string;
    isDefined: boolean;
}

export interface Field008Definitions {
    documentTypes: Field008DocumentType[];
    codes: Field008Code[];
    labels: Field008Label[];
    placeOfPublication: Field008PositionWithDictionaryValue;
    language: Field008PositionWithDictionaryValue;
    publicationStatus: Field008Position;
    modifiedRecord: Field008Position;
    catalogingSource: Field008Position;
}

export interface AuthorityField008StaticCode {
    code: string;
    description: string;
}

export interface AuthorityField008Position {
    label: string;
    codes: AuthorityField008StaticCode[];
}

export interface AuthorityField008Definitions {
    directOrIndirectGeographicSubdivision: AuthorityField008Position;
    romanizationScheme: AuthorityField008Position;
    languageOfCatalog: AuthorityField008Position;
    kindOfRecord: AuthorityField008Position;
    descriptiveCatalogingRules: AuthorityField008Position;
    subjectHeadingSystemThesaurus: AuthorityField008Position;
    typeOfSeries: AuthorityField008Position;
    numberedOrUnnumberedSeries: AuthorityField008Position;
    headingUseMainOrAddedEntry: AuthorityField008Position;
    headingUseSubjectAddedEntry: AuthorityField008Position;
    headingUseSeriesAddedEntry: AuthorityField008Position;
    typeOfSubjectSubdivision: AuthorityField008Position;
    typeOfGovernmentAgency: AuthorityField008Position;
    referenceEvaluation: AuthorityField008Position;
    recordUpdateInProcess: AuthorityField008Position;
    undifferentiatedPersonalName: AuthorityField008Position;
    levelOfEstablishment: AuthorityField008Position;
    modifiedRecord: AuthorityField008Position;
    catalogingSource: AuthorityField008Position;
}

export interface LocationRequest extends OrderedNamedIdentified<number>, ConfirmableRequest {
    code: string;
    departments: Department[];
}

export interface SdiRequest extends NamedLabeledIdentified<number> {
    user: BasicUser;
    department: Department;
    query: string;
    periodicity: Periodicity;
    createDate: string;
    terminationDate: string;
    active: boolean;
    deletable: boolean;
}

export type Periodicity = LabeledIdentified<number>

export interface SdiSending extends Identified<number> {
    sdiRequestId: number;
    date: string;
    recordsCount: number;
    error: string;
}

export interface SdiRequestCreationRequest extends Partial<NamedIdentified<number>> {
    user: BasicUser;
    search: ViewableSearch<RecordSearchParams, Document>;
}

export interface SdiRequestEditRequest extends Identified<number> {
    active: boolean;
}

export interface FacetType extends NamedLabeledIdentified<number>, Ordered {
    definition?: string;
    exemplarType: boolean;
    enabled: boolean;
    sorting: Sorting;
    definitionType: LabeledIdentified<number>;
    scope: LabeledIdentified<number>;
    datatype?: string;
}

export interface Filter extends NamedIdentified<number> {
    filters?: Filter[];
    matcher?: Matcher;
    matchResult?: MatchResult;
    description?: string;
}

export interface Matcher {
    name?: string;
    description?: string;
    sourceData?: SourceData;
    matchers?: Matcher[];
    target?: Matcher;
}

export interface MatchResult {
    allowed: boolean;
    denied: boolean;
    messageCode: string | null;
}

export interface SourceData extends Identified<number> {
    type: string | null;
    negated: boolean;
    value: string | null;
    description: string | null;
}

export interface RenewalResponse extends ActionResponseWithText, ActionResponseWithActionLabel {
    numberOfDays: number;
    renewalsCount: number;
    returnDate: string;
}

export interface DirectoryInsight {
    withSubdirectories: boolean;
    withNonCoverFiles: boolean;
    sampleFiles: IdentifiedFile[];
}

export interface DirectoryNestedInsight {
    allFilesCount: number;
    allSubdirsCount: number;
}

export interface Currency extends NamedLabeledIdentified<string>, Ordered {
    exchangeRate: number;
}

export interface Price {
    amount: number;
    currency: Currency;
}

export interface Seeking extends Identified<string> {
    providersView: boolean;
    state: LabeledIdentified<string>;
    desiredExemplar: SeekingDesiredExemplar;
    eventuallyPhotocopyable: boolean;
    eventuallyReservable: boolean;
    eventuallyOnSiteLendable: boolean;
    note?: string;
    seekerReferenceId?: string;
    requesterObtainDeadlineDate?: string;
    department: DepartmentDescriptor;
    createDate: string;
    commenceDate?: string;
    cancelled: boolean;
    activeProvision?: SeekingProvision;
    intents: PermissionedIdentifiedIntent<SeekingIntentId>[];
    ziskejSync?: {
        webUrl: string
    };
}

export interface SeekedSeeking extends Seeking {
    requester?: BasicUser;
    eventuallyAbroadDeliverable: boolean;
    pendingProvisions: SeekingProvision[];
    endedProvisions: SeekingProvision[];
}

export interface ProvidedSeeking extends Seeking {
    seeker: BasicUser;
    activeProvision: SeekingProvision;
}

export interface SeekingProvision extends Identified<string> {
    state: string;
    provider?: BasicUser;
    deliveryChannel: LabeledIdentified<string>;
    providerReferenceId?: string;
    price: Price;
    exemplarIdentifiers?: Valuable<string>[];
    seekerActivateDate?: string;
    providerAcceptDate?: string;
    providerAcceptCondition?: string;
    seekerAcceptDate?: string;
    providerReserveDate?: string;
    providerSendDate?: string;
    seekerReceiveDate?: string;
    seekerSendDate?: string;
    providerReceiveDate?: string;
    seekerCancelDate?: string;
    providerCancelDate?: string;
    intents: PermissionedIdentifiedIntent<SeekingProvisionIntentId>[];
}

export interface SeekingDesiredExemplar {
    document: Document;
    volume: string | null;
    page: string | null;
    article: string | null;
    note: string | null;
}

export type CacheMode = 'longterm' | 'none';

export type AsyncProcessStatusState = 'STARTING' | 'RUNNING' | 'FAILED' | 'CANCELLED' | 'FINISHED';

export interface AsyncProcessStatus<ID> extends Identified<ID> {
    totalUnitsCount: number;
    processedUnitsCount: number;
    state: AsyncProcessStatusState;
}

export interface DocumentTabsConfiguration {
    exemplars: TabPriorityAndShowability;
    articles: TabPriorityAndShowability;
    parts: TabPriorityAndShowability;
    citation: TabPriorityAndShowability;
    none: TabPriorityAndShowability;
    toc: TabPriorityAndShowability;
    detail: TabPriorityAndShowability;
    marc: TabPriorityAndShowability;
    loans: TabPriorityAndShowability;
    operations: TabPriorityAndShowability;
    revisions: TabPriorityAndShowability;
}

export interface TabPriorityAndShowability {
    priority: number;
    showable: boolean;
}

export interface ErroredFieldResponse extends Labeled {
    type: string;
}

export interface ViewableField extends LabeledIdentified<UUID>, WithCode, FieldList {
    fieldTypeText: string;
    raw?: string | null;
    recordLink: FondedRecordLabeledReference | null;
    typeId: FieldTypeId;
    url: boolean;
    error: ErroredFieldResponse | null;
}

export type ViewableFieldWithError = ViewableField & NonNullableProperties<Pick<ViewableField, 'error'>>

export interface AuthorityTabsConfiguration {
    attachments: TabPriorityAndShowability;
    detail: TabPriorityAndShowability;
    marc: TabPriorityAndShowability;
    operations: TabPriorityAndShowability;
}

export interface PortaroVersion extends Labeled {
    value: string;
    businessVersion: string;
    date: string;
    branch: string;
    humanReadableString: string;
    humanReadableDetailString: string;
    lastCommitMessage: string;
    developVersion: boolean;
}

export const DA_AND_NUMBER_REGEX = /^[da]\d+$/;
export const IND_1_CODE = 'ind1';
export const IND_2_CODE = 'ind2';
export const TOC_CODE = 'toc';
export const IS_FIELD_INDICATOR = (subfield: WithCode) => [IND_1_CODE, IND_2_CODE].includes(subfield.code);

export interface FlattenedTreeListNode {
    depth: number;
    treeGraphElements: TreeGraphElement[];
}

export const TreeGraphElements = {
    SPACE: 'SPACE',
    LINE: 'LINE',
    T_JUNCTION: 'T_JUNCTION',
    L_JUNCTION: 'L_JUNCTION'
} as const;

export type TreeGraphElement = EnumValues<typeof TreeGraphElements>;
