<script lang="ts">
    import type {
        GridField,
        GridFieldType,
        RecordRow,
        SimpleGridFieldValue
    } from '../../../lib/types';
    import type {Column, Row} from '@tanstack/svelte-table';
    import {cleanup, isNullOrUndefined} from 'shared/utils/custom-utils';
    import ReferenceFieldValue from './values/ReferenceFieldValue.svelte';
    import ScalarFieldValue from './values/ScalarFieldValue.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import {
        resolveGridFieldAttributeValue
    } from '../../../lib/grid-utils';
    import EditableFieldValueContainer from './values/EditableFieldValueContainer.svelte';
    import {onDestroy} from 'svelte';
    import type {FieldId} from 'typings/portaro.be.types';
    import {getGridCellContext} from 'shared/ui-widgets/grid/grid-cell/grid-cell.context';
    import type { NonEmptyArray} from 'shared/utils/array-utils';
    import {byProperty, findFirst} from 'shared/utils/array-utils';
    import {asNotEmpty, isEmpty} from 'shared/utils/array-utils';
    import {EMPTY} from 'rxjs';
    import {Subject, switchMap} from 'rxjs';
    import {catchError, debounceTime, filter, tap} from 'rxjs/operators';
    import {fromPromise} from 'rxjs/internal/observable/innerFrom';
    import ErrorFieldValue from './values/ErrorFieldValue.svelte';
    import OptionFieldValue from './values/OptionFieldValue.svelte';
    import type {FieldIdAndFieldValue} from 'src/features/record-grid/components/cells/data-cell/types';
    import {generateIdOfInitialField, isInvalid} from 'src/features/record-grid/components/cells/data-cell/utils';
    import {
        hasRecordReference,
        isErrorGridFieldValue,
        isOptionGridFieldValue
    } from 'src/features/record-grid/lib/types-utils';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import {isFieldValueEqual} from 'src/features/record-grid/lib/grid-fields';

    export let values: GridField<SimpleGridFieldValue>[] | undefined;
    export let column: Column<RecordRow>;
    export let fieldType: GridFieldType;
    export let row: Row<RecordRow>;
    export let recordGridDataManager: RecordGridDataManager;

    const {contentDependentSate$} = getGridCellContext();

    const change$ = new Subject<FieldIdAndFieldValue<SimpleGridFieldValue>>();
    const subscription = change$.pipe(
        tap(({fieldId, value}) => recordGridDataManager.updateTableDataCellState(row.original, fieldId, value)),
        debounceTime(1500),
        filter(({fieldId, value}) => isEqualToCurrentValue(value, fieldId)), // ignore change if value was changed from outside during debounce period
        switchMap(({fieldId, value}) =>
            fromPromise(recordGridDataManager.updateServerCellState(row.original, fieldId, value)).pipe(catchError(() => EMPTY))),
        tap(() => recordGridDataManager.requestRowRefresh(row.original))
    ).subscribe();

    $: fields = normalize(values);

    $: enabled = resolveGridFieldAttributeValue(column.columnDef.meta.enabled, row.original.fond.id);
    $: editable = resolveGridFieldAttributeValue(column.columnDef.meta.editable, row.original.fond.id)
    $: required = resolveGridFieldAttributeValue(column.columnDef.meta.required, row.original.fond.id);
    $: contentDependentSate$.set({disabled: !enabled, invalid: isInvalid(fields, required)});

    onDestroy(() => {
        cleanup(subscription);
    })

    function isEqualToCurrentValue(changedValue: SimpleGridFieldValue, fieldId: FieldId) {
        const currentValue = findFirst(values, byProperty('fieldId', fieldId));
        return isFieldValueEqual(currentValue, changedValue);
    }

    function editCell(value: SimpleGridFieldValue, fieldId: FieldId) {
        change$.next({fieldId, value});
    }

    function normalize(gridFields: GridField<SimpleGridFieldValue>[]): NonEmptyArray<FieldIdAndFieldValue<GridField<SimpleGridFieldValue>>> {
        if (isNullOrUndefined(gridFields) || isEmpty(gridFields)) {
            return [{value: null, fieldId: generateIdOfInitialField(fieldType.id)}];
        }

        return asNotEmpty(gridFields.map((gridField) => ({value: gridField, fieldId: gridField.fieldId})));
    }
</script>

{#if enabled}
    {#if fields.length === 1}
        {@const field = fields[0]}
        <div title="{field.fieldId}">
            <EditableFieldValueContainer value={field.value}
                                         {editable}
                                         {fieldType}
                                         on:cell-edit={(event) => editCell(event.detail, field.fieldId)}/>
        </div>
    {:else}
        <div class="value-container">
            <ul>
                {#each fields as field}
                    <li title="{field.fieldId}">
                        {#if hasRecordReference(field.value)}
                            <ReferenceFieldValue value={field.value}/>
                            <!-- ReferenceFieldValue already uses ChipTag -->
                        {:else if isErrorGridFieldValue(field.value)}
                            <KpChipTag chipSize="xs">
                                <ErrorFieldValue value={field.value}/>
                            </KpChipTag>
                        {:else if isOptionGridFieldValue(field.value)}
                            <OptionFieldValue value={field.value}/>
                            <!-- OptionFieldValue already uses ChipTag -->
                        {:else}
                            <KpChipTag chipSize="xs">
                                <ScalarFieldValue value={field.value}/>
                            </KpChipTag>
                        {/if}
                    </li>
                {/each}
            </ul>
        </div>
    {/if}
{/if}



<style lang="less">
    ul {
        display: inline-flex;
        gap: 3px;
    }

    li {
        display: inline;
    }

    .value-container {
        display: flex;
        align-items: center;
    }
</style>