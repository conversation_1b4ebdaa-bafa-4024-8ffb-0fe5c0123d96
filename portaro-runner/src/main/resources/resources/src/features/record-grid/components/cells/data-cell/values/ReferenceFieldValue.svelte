<script lang="ts">
    import type {GridFieldValueWithReference} from '../../../../lib/types';
    import {exists} from 'shared/utils/custom-utils';
    import Label from 'shared/components/kp-label/Label.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import SutorFondChip from 'src/features/sutor/pages/record-detail/components/SutorFondChip.svelte';

    export let value: GridFieldValueWithReference;
</script>

{#if value.text}
    {#if exists(value.recordReference.fond?.id)}
        <SutorFondChip fondId="{value.recordReference.fond.id}" fondName="{value.recordReference.fond.text}" chipSize="xs">
            <Label labeled="{value.recordReference}"/>
        </SutorFondChip>
    {:else}
        <KpChipTag additionalClasses="kp-grid-complex-value"
                   chipStyle="info"
                   chipSize="xs">

            <Label labeled="{value.recordReference}"/>
        </KpChipTag>
    {/if}
{/if}

<style lang="less">
    :global {
        .kp-grid-complex-value a {
            color: white;
        }
    }
</style>
