<script lang="ts">
    import type {FieldTypeId, Fond, RecordSearchParams, UUID} from 'typings/portaro.be.types';
    import {createSvelteTable} from '@tanstack/svelte-table';
    import type {Updater} from '@tanstack/table-core';
    import type {SvelteComponentConstructor} from 'core/types';
    import {writable} from 'svelte/store';
    import {getCurrentLanguage, getInjector, getLocalization, getLogger} from 'core/svelte-context/context';
    import {getCoreRowModel, getExpandedRowModel} from '@tanstack/svelte-table';
    import {
        defaultCellContent,
        defaultRowId, extractCoordinates,
        getStateOf, replaceTableData, tryToFindGridCell,
        tryToFindRowByElement,
        updateTableState
    } from 'shared/ui-widgets/grid/utils';
    import {createRender} from 'svelte-render';
    import {exists, isNull, isNullOrUndefined} from 'shared/utils/custom-utils';
    import {RecordGridService} from './services/record-grid.service';
    import {
        arrayShallowEqual,
        ascendingOrderComparator,
        isNotEmpty
    } from 'shared/utils/array-utils';
    import {onDestroy} from 'svelte';
    import {getSearchContext} from '../search/kp-search-context/search-context';
    import {combineLatestWith, distinctUntilChanged, filter, map, tap} from 'rxjs/operators';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {recordGridTableSettingsStorage} from './lib/settings-storage';
    import KpGenericGrid from 'shared/ui-widgets/grid/KpGenericGrid.svelte';
    import KpPageableSearchResults from '../search/kp-search-results/KpPageableSearchResults.svelte';
    import HeaderCell from './components/cells/HeaderCell.svelte';
    import ControlCell from './components/cells/ControlCell.svelte';
    import type {
        GridField,
        GridFieldType,
        GridLayout,
        RecordRow, SimpleGridFieldValue
    } from './lib/types';
    import type {
        ColumnDef,
        DisplayColumnDef,
        TableOptions,
        ColumnPinningState,
        ColumnSizingState,
        VisibilityState
    } from '@tanstack/svelte-table';
    import {findExpandableColumns} from './lib/grid-utils';
    import DataCell from './components/cells/data-cell/DataCell.svelte';
    import ControlFooterCell from './components/cells/ControlFooterCell.svelte';
    import {isEqual} from 'lodash-es';
    import KpContextMenuButton from 'shared/components/kp-context-menu/KpContextMenuButton.svelte';
    import {COLUMN_TYPES} from 'src/features/record-grid/lib/constants';
    import {extractAllSimpleGridFields, getFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {createHierarchicalRows} from 'src/features/record-grid/lib/record-row';
    import {asSupported} from 'src/features/record-grid/lib/grid-value-editors';
    import KpHorizontalSeparator from 'shared/ui-widgets/separator/KpHorizontalSeparator.svelte';
    import {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import {ToastMessageService} from 'shared/components/kp-toast-messages/toast-message.service';
    import {createGridSettings} from 'shared/ui-widgets/grid/grid-settings';
    import type {GridContext} from 'shared/ui-widgets/grid/grid.context';
    import {getGridContext, setGridContext} from 'shared/ui-widgets/grid/grid.context';
    import {BehaviorSubject} from 'rxjs';


    export let fond: Fond;
    export let gridLayout: GridLayout;
    export let insertableFonds: Fond[];
    export let inFullscreenPage = false;
    export let stickyHeader = false;
    export let pinControlColumn = false;
    export let expandedFirstLevel = false;
    export let relatedRecordId: UUID = null;
    export let hierarchyFieldTypeId: FieldTypeId | null = null;
    export let includeAddNewRow = true;
    export let gridContext: GridContext<RecordRow> | null = null;
    export let controlCellComponent: SvelteComponentConstructor;

    const logger = getLogger();
    const toastMessageService = getInjector().getByClass(ToastMessageService);
    const localize = getLocalization();
    const locale = getCurrentLanguage();
    const recordGridService = getInjector().getByClass(RecordGridService);
    const searchManager = getSearchContext<RecordRow, RecordSearchParams>();

    const expandableColumnsIds = exists(hierarchyFieldTypeId) ? [hierarchyFieldTypeId] : findExpandableColumns(gridLayout).map((fieldType) => fieldType.id);
    const isExpandable = isNotEmpty(expandableColumnsIds) && !inFullscreenPage;

    let {columnPinning, columnSizing, columnVisibility} = recordGridTableSettingsStorage.loadFondSettings(fond);
    $: recordGridTableSettingsStorage.saveFondSettings(fond, {
        columnPinning,
        columnSizing,
        columnVisibility
    });

    const options = createTableOptions(gridLayout, insertableFonds);
    export let table$ = createSvelteTable(options);

    const settings = createGridSettings<RecordRow>({
        columnPinningEnabled: true,
        columnResizingEnabled: true,
        condensed: true,
        mainGroupsDivided: true,
        stickyHeader,
        colorAccented: true,
        fullPageView: inFullscreenPage,
        selectionEnabled: true,
        editable: true,
        showOnlyTopAndBottomHeaders: true
    });

    setGridContext<RecordRow>(table$, settings);
    gridContext = getGridContext<RecordRow>();
    const {stateManager, command$} = gridContext;


    const flatTableData$ = new BehaviorSubject<RecordRow[]>([]);
    const draftRows$ = new BehaviorSubject<RecordRow[]>([]);

    const dataSubscription = flatTableData$.pipe(
        map((rows) => isExpandable ? createHierarchicalRows(rows, expandableColumnsIds, relatedRecordId) : rows),
        filter((rows) => exists(rows)),
        distinctUntilChanged<RecordRow[]>(isEqual),
        combineLatestWith(draftRows$),
        map(([flat, drafts]) => [...flat, ...drafts]),
        tap((rows) => replaceTableData(options, rows))
    ).subscribe();

    const lastSearch = searchManager.getLastSearch$();
    const searchSubscription = searchManager.getPagination()
        .getPaginationData$()
        .pipe(map((data) => data.items), distinctUntilChanged(arrayShallowEqual))
        .subscribe(flatTableData$);

    const draftSearchSubscription = searchManager.createDerivedSearch((params) => ({...params, pageNumber: 1, onlyDrafts: true})) // assume there is not more than 1 page of drafts
        .getPagination()
        .getPaginationData$()
        .pipe(map((data) => data.items), distinctUntilChanged(arrayShallowEqual))
        .subscribe(draftRows$);

    const recordGridDataManager = new RecordGridDataManager(table$, stateManager, flatTableData$, draftRows$, command$, recordGridService, searchManager, localize, locale, logger, toastMessageService);

    onDestroy(() => {
        unsubscribeAllSubscriptions(dataSubscription, searchSubscription, draftSearchSubscription);
        recordGridDataManager.disconnect();
    });

    function createDataColumns(layout: GridLayout): ColumnDef<RecordRow>[] {
        return layout.fieldTypes.sort(ascendingOrderComparator).map((column) => createDataColumn(column));
    }

    function createDataColumn(column: GridFieldType): ColumnDef<RecordRow> {
        if (isNullOrUndefined(column.fieldTypes) || column.fieldTypes.length === 0) {
            return {
                id: column.id,
                meta: {
                    text: column.text,
                    columnType: COLUMN_TYPES.DATA_COLUMN,
                    editorType: asSupported(column.editor.defaultValue.type),
                    datatype: column.datatype,
                    enabled: column.enabled,
                    editable: column.editable,
                    required: column.required,
                    dependentFieldTypeIds: column.dependentFieldTypeIds
                },
                accessorFn: (row) => extractAllSimpleGridFields(getFieldByFieldTypeIdRecursive(row, column.id)),
                header: () => createRender(HeaderCell, {text: column.text, label: column.id}),
                cell: (ctx) => {
                    return createRender(DataCell, {
                        values: ctx.cell.getValue<GridField<SimpleGridFieldValue>[]>(),
                        column: ctx.column,
                        fieldType: column,
                        row: ctx.row,
                        recordGridDataManager
                    });
                }
            };
        }
        return {
            id: column.id,
            meta: {text: column.text, columnType: COLUMN_TYPES.TOP_COLUMN},
            header: () => createRender(HeaderCell, {text: column.text, label: column.id}),
            columns: column.fieldTypes.map((subColumn) => createDataColumn(subColumn))
        };
    }

    function createControlColumn(fonds: Fond[]): DisplayColumnDef<RecordRow> {
        const shouldRenderFooter = includeAddNewRow && isNotEmpty(fonds);
        return {
            id: 'EDIT',
            meta: {text: '', columnType: COLUMN_TYPES.TOP_COLUMN},
            enableHiding: false,
            header: () => createRender(HeaderCell, {text: '', label: ''}),
            cell: ({row}) => createRender(controlCellComponent ?? ControlCell, {
                recordGridDataManager,
                row,
                rowExpansionEnabled: isExpandable
            }),
            ...shouldRenderFooter ? {
                footer: () => createRender(ControlFooterCell, {
                    recordGridDataManager,
                    insertableFonds: fonds
                })
            } : {}
        };
    }

    function createTableOptions(layout: GridLayout, fonds: Fond[]) {
        const optionsData: TableOptions<RecordRow> = {
            data: [],
            columns: [createControlColumn(fonds), ...createDataColumns(layout)],
            getSubRows: (row) => row.subrows ?? [],
            getCoreRowModel: getCoreRowModel(),
            getExpandedRowModel: getExpandedRowModel(),
            getRowId: defaultRowId(),
            onColumnPinningChange: handleOnColumnPinningChange,
            onColumnSizingChange: handleOnColumnSizingChange,
            columnResizeDirection: 'ltr',
            onColumnVisibilityChange: handleOnColumnVisibilityChange,
            state: {
                columnPinning,
                columnSizing,
                columnVisibility
            },
            defaultColumn: {
                cell: defaultCellContent(),
                enableResizing: true,
                enablePinning: true,
                enableHiding: true
            },
            manualPagination: true
        };

        if (pinControlColumn && (!exists(columnPinning.left) || !columnPinning.left.includes('EDIT'))) {
            columnPinning.left = [...(columnPinning.left ?? []), 'EDIT'];
        }

        return writable<TableOptions<RecordRow>>(optionsData);
    }

    function handleOnColumnPinningChange(updater: Updater<ColumnPinningState>) {
        columnPinning = getStateOf(columnPinning, updater);
        updateTableState(options, {columnPinning});
    }

    function handleOnColumnSizingChange(updater: Updater<ColumnSizingState>) {
        columnSizing = getStateOf(columnSizing, updater);
        updateTableState(options, {columnSizing});
    }

    function handleOnColumnVisibilityChange(updater: Updater<VisibilityState>) {
        columnVisibility = getStateOf(columnVisibility, updater);
        updateTableState(options, {columnVisibility});
    }

    function handleDuplicateRow(contextElementRef: Element) {
        const row = tryToFindRowByElement(contextElementRef, $table$);
        if (isNull(row)) {
            return;
        }
        recordGridDataManager.duplicateRow(row);
    }

    function handleDeleteRow(contextElementRef: Element) {
        const row = tryToFindRowByElement(contextElementRef, $table$);
        if (isNull(row)) {
            return;
        }
        recordGridDataManager.deleteRow(row);
    }

    function handleCopyCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.copyCell(extractCoordinates(cellElement));
    }

    function handlePasteCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.pasteCell(extractCoordinates(cellElement));
    }

    function handleCutCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.cutCell(extractCoordinates(cellElement));
    }

    function handleDeleteCell(contextElementRef: Element) {
        const cellElement = tryToFindGridCell(contextElementRef);
        if (isNullOrUndefined(cellElement)) {
            return;
        }
        recordGridDataManager.deleteCell(extractCoordinates(cellElement));
    }
</script>

<div class="kp-record-grid-display" class:fullscreen={inFullscreenPage}>
    <KpPageableSearchResults pagination="{searchManager.getPagination()}"
                             showPageButtons="{exists($lastSearch) && exists($lastSearch.features) && $lastSearch.features.includes('randomPageAccessing')}"
                             loadNextButtonAdditionalClasses="load-next-results-btn"
                             loadPreviousButtonAdditionalClasses="load-previous-results-btn"
                             showEmptyResultsMessage="{false}">

        <KpGenericGrid {table$} {expandedFirstLevel} additionalClasses="record-grid" additionalContainerClasses="record-grid-container">
            <svelte:fragment slot="grid-context-menu" let:contextElementRef>
                <KpContextMenuButton icon="copy" on:click={() => handleCopyCell(contextElementRef)}>
                    {localize(/* @kp-localization commons.Zkopirovat */ 'commons.Zkopirovat')}
                    buňku
                </KpContextMenuButton>

                <KpContextMenuButton icon="paste" on:click={() => handlePasteCell(contextElementRef)}>
                    {localize(/* @kp-localization commons.Paste */ 'commons.Paste')}
                    buňku
                </KpContextMenuButton>

                <KpContextMenuButton icon="scissors" on:click={() => handleCutCell(contextElementRef)}>
                    {localize(/* @kp-localization commons.Cut */ 'commons.Cut')}
                    buňku
                </KpContextMenuButton>

                <KpContextMenuButton icon="trash" on:click={() => handleDeleteCell(contextElementRef)}>
                    {localize(/* @kp-localization commons.Smazat */ 'commons.Smazat')}
                    buňku
                </KpContextMenuButton>

                <KpHorizontalSeparator width="100%"/>

                <KpContextMenuButton icon="duplicate" on:click={() => handleDuplicateRow(contextElementRef)}>
                    Duplikovat řádek
                </KpContextMenuButton>

                <KpContextMenuButton icon="trash" on:click={() => handleDeleteRow(contextElementRef)}>
                    Smazat řádek
                </KpContextMenuButton>
            </svelte:fragment>
        </KpGenericGrid>
    </KpPageableSearchResults>
</div>

<style lang="less">
    @import (reference) "bootstrap-less/bootstrap/variables";
    @import (reference) "styles/portaro.variables.less";

    .kp-record-grid-display {
        display: flex;
        flex-direction: column;

        &:not(.fullscreen) {
            gap: @spacing-xl;
        }
    }

    :global {
        .kp-record-grid-display {
            .add-new-record-btn {
                height: 40px;
            }

            .record-grid-container {
                border: 1px solid @table-border-color;
            }

            &:not(.fullscreen) .record-grid-container {
                border-radius: @border-radius-base;
            }

            &.fullscreen .record-grid-container {
                border-right: none;
                border-left: none;
            }

            .load-next-results-btn,
            .load-previous-results-btn {
                width: 25%;
                min-width: 200px;
                align-self: center;
            }
        }
    }
</style>
