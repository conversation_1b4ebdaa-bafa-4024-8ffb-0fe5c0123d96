<script lang="ts">
    import type {TabButton} from 'shared/ui-widgets/tabset/types';
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import {createSutorEmployeesContext} from './employees.context';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {onMount} from 'svelte';
    import {getInjector} from 'core/svelte-context/context';
    import {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
    import {FOND_PERSON} from 'src/features/sutor/sutor-fonds';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import EmployeesAllTab from './tab-pages/EmployeesAllTab.svelte';
    import EmployeesAttendanceTab from './tab-pages/attendance/EmployeesAttendanceTab.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';

    const searchManagerBuilderFactoryService = getInjector().getByClass(SearchManagerBuilderFactoryService);
    const context = createSutorEmployeesContext();

    let loading = true;

    const tabs = {
        'tab-all': {component: EmployeesAllTab},
        'tab-attendance': {component: EmployeesAttendanceTab}
    };

    const tabButtons: TabButton[] = [
        {
            id: 'tab-all',
            label: 'Všechni pracovníci',
            icon: 'users',
            tabPageWithoutPadding: true,
            tabPageContainerClass: 'record-grid-tab-page'
        },
        {
            id: 'tab-attendance',
            label: 'Docházka',
            icon: 'clock'
        }
    ];

    onMount(() => {
        createSearchManagers();
        loading = false;
    });

    function createSearchManagers() {
        const allEmployeesSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createStaticParams());
        context.allEmployeesSearchManager = allEmployeesSearchManagerBuilder.createLocalSearch();
        context.allEmployeesSearchManager.newSearch();

        const attendanceSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createStaticParams());
        context.attendanceSearchManager = attendanceSearchManagerBuilder.createLocalSearch();
        context.attendanceSearchManager.newSearch();
    }

    function createStaticParams(): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: FOND_PERSON.fond}],
            pageSize: 100
        };
    }
</script>

<ErpPageLayout pageClass="sutor-employees-page" gap="0px" withoutContentPadding {loading}>
    <ErpHeadingBar slot="heading" title="Evidovaní pracovníci" icon="users"/>

    <ErpTabbedSubpagesContainer {tabButtons}
                                let:activeTab
                                additionalClasses="employees-tabbed-subpages">

        <svelte:component this="{tabs[activeTab].component}" {...tabs[activeTab].props ?? {}}/>
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>