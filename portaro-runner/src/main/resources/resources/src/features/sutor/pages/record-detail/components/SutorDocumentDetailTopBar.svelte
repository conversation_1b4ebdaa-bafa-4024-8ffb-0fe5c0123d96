<script lang="ts">
    import type {RecordHolding, User} from 'typings/portaro.be.types';
    import type {UserDetailView} from 'src/features/user/types';
    import {getLocalization} from 'core/svelte-context/context';
    import {getPageContext} from 'shared/layouts/page-context';
    import {onDestroy} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {UserRoles} from 'shared/services/current-auth.service';
    import KpGenericTopbar from 'shared/components/kp-generic-topbar/KpGenericTopbar.svelte';
    import SutorFondChip from './SutorFondChip.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import SutorDocumentDetailSettingsPopover from './SutorDocumentDetailSettingsPopover.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import type {DocumentDetailReactivePageData, DocumentDetailStaticPageData} from '../../../../record/kp-document-detail-page/types';

    export let user: User | null = null;
    export let userDetailView: UserDetailView | null = null;

    const localize = getLocalization();
    const pageContext = getPageContext<DocumentDetailStaticPageData, DocumentDetailReactivePageData>();
    const model = pageContext.staticData.model;

    let recordHoldings: RecordHolding[];
    const reactiveDataUnsubscribe = pageContext.reactiveData.subscribe((data) => recordHoldings = data.recordHoldings);
    onDestroy(reactiveDataUnsubscribe);

    $: filteredHoldings = (recordHoldings ?? []).filter((holding) => !exists(holding.discardionEventId) && !exists(holding.deletionEventId));
</script>

<KpGenericTopbar height="var(--sutor-topbar-height)" gap="24px">
    <div class="title-container">
        <SutorFondChip chipSize="sm" fondId="{model.record.fond.id}" fondName="{model.record.fond.text}">{model.record.fond.text}</SutorFondChip>

        {#if exists(user)}
            {#if exists(user.deletionEventId)}
                <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-deleted">
                    {localize(/* @kp-localization commons.Deleted */ 'commons.Deleted')}
                </KpChipTag>
            {/if}

            {#if user.readerAccounts[0]?.blocked}
                <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-blocked">
                    {localize(/* @kp-localization ctenar.TransactionsBlocked */ 'ctenar.TransactionsBlocked')}
                </KpChipTag>
            {/if}

            {#if user.readerAccounts[0]?.registrationExpired}
                <KpChipTag chipStyle="danger-new" dataQa="reader-role-label-registration-expired">
                    {localize(/* @kp-localization ctenar.RegistrationExpired */ 'ctenar.RegistrationExpired')}
                </KpChipTag>
            {/if}

            {#if user.role.includes(UserRoles.ROLE_READER)}
                <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-reader">
                    {localize(/* @kp-localization user.Reader */ 'user.Reader')}
                </KpChipTag>
            {/if}

            {#if user.role.includes(UserRoles.ROLE_LIBRARIAN)}
                <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-librarian">
                    {localize(/* @kp-localization user.Librarian */ 'user.Librarian')}
                </KpChipTag>
            {/if}

            {#if user.role.includes(UserRoles.ROLE_LIBRARY)}
                <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-library">
                    {localize(/* @kp-localization user.MvsLibrary */ 'user.MvsLibrary')}
                </KpChipTag>
            {/if}

            {#if user.role.includes(UserRoles.ROLE_SUPPLIER)}
                <KpChipTag chipStyle="accent-blue-new" dataQa="reader-role-label-supplier">
                    {localize(/* @kp-localization user.Supplier */ 'user.Supplier')}
                </KpChipTag>
            {/if}

            <KpUserAvatar {user} sizePx="{24}"/>
        {/if}

        <span class="record-name">{model.record.name}</span>
    </div>

    {#if filteredHoldings.length > 0}
        <KpVerticalSeparator height="20px"/>

        <div class="holdings-container">
            {#each filteredHoldings as recordHolding(recordHolding.id)}
                <KpChipTag chipSize="xs">
                    {pipe(recordHolding.department, loc())}
                </KpChipTag>
            {/each}
        </div>
    {/if}

    <div class="full-width-spacer"></div>

    <SutorDocumentDetailSettingsPopover {user} {userDetailView}/>
</KpGenericTopbar>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";

    .title-container {
        display: flex;
        align-items: center;
        gap: @spacing-m;
    }

    .holdings-container {
        display: flex;
        align-items: center;
        gap: @spacing-s;
    }
</style>