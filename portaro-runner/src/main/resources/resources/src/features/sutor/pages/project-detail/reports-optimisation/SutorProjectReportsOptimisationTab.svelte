<script lang="ts">
    import type {Document, RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {TabButton, TabId} from 'shared/ui-widgets/tabset/types';
    import type {OptimisationSumsResponse} from 'src/features/sutor/pages/project-detail/reports-optimisation/types';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {RecordGridService} from 'src/features/record-grid/services/record-grid.service';
    import {getCurrentLanguage, getInjector} from 'core/svelte-context/context';
    import {onDestroy, onMount} from 'svelte';
    import {createSutorReportsOptimisationContext} from './sutor-reports-optimisation-context';
    import {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
    import {SutorReportsOptimisationService} from './services/sutor-reports-optimisation.service';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {priceFormatter} from 'shared/utils/pipes';
    import {pipe} from 'core/utils';
    import {exists} from 'shared/utils/custom-utils';
    import {fly} from 'svelte/transition';
    import {SUTOR_REPORTS_OPTIMISATION_PAGE} from 'src/features/sutor/sutor-constants';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import ReportOptimisationEditationTab from 'src/features/sutor/pages/project-detail/reports-optimisation/editation/ReportOptimisationEditationTab.svelte';
    import KpLoadingBlock from 'shared/components/kp-loading/KpLoadingBlock.svelte';
    import ReportOptimisationOverviewsTab from './overviews/ReportOptimisationOverviewsTab.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import LabeledValue from 'src/features/sutor/pages/project-detail/reports-optimisation/overviews/components/LabeledValue.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import KpVerticalSeparator from 'shared/ui-widgets/separator/KpVerticalSeparator.svelte';

    export let record: Document;
    export let reportedProject: boolean;

    const recordGridService = getInjector().getByClass(RecordGridService);
    const service = getInjector().getByClass(SutorReportsOptimisationService);
    const searchManagerBuilderFactoryService = getInjector().getByClass(SearchManagerBuilderFactoryService);
    const context = createSutorReportsOptimisationContext(record, service);
    const priceFormat = priceFormatter(getCurrentLanguage());

    let loading = true;

    let optimisedInstallationLogbook: RecordRow | null = null;
    let overviewedInstallationLogbook: RecordRow | null = null;
    let activeTab: TabId = 'tab-editation';
    let tabToSelect: TabId | null = null;

    let optimisationSums: OptimisationSumsResponse | null = null;

    const optimisedInstallationLogbookUnsubscribe = context.optimisedInstallationLogbookRecord$.subscribe((currentOptimisedInstallationLogbook) => optimisedInstallationLogbook = currentOptimisedInstallationLogbook);
    // When the signal is set, we need to switch to the overview tab
    const openOverviewSignalUnsubscribe = context.openOverviewSignal$.subscribe((installationLogbookRecord) => {
        overviewedInstallationLogbook = installationLogbookRecord;

        if (exists(installationLogbookRecord)) {
            tabToSelect = 'tab-overviews';
        }
    });

    // Tabs setup
    const tabs = {};
    const tabButtons: TabButton[] = [
        {
            id: 'tab-editation',
            label: 'Náklady k optimalizaci',
            icon: 'list',
            tabPageWithoutPadding: true
        },
        {
            id: 'tab-overviews',
            label: 'Přehledy',
            icon: 'info',
            tabPageWithoutPadding: true
        }
    ];

    onMount(async () => {
        const optimisationFond = await recordGridService.getFond(SUTOR_REPORTS_OPTIMISATION_PAGE.fond);

        // We don't want to wait for this - just let it load in the background
        service.getReportSums(record).then((response) => optimisationSums = response);

        tabs['tab-overviews'] = {component: ReportOptimisationOverviewsTab};
        tabs['tab-editation'] = {
            component: ReportOptimisationEditationTab,
            props: {
                fond: optimisationFond,
                reportedProject
            }
        };

        createSearchManagers();
        loading = false;
    });

    onDestroy(() => {
        unsubscribeAllSubscriptions(optimisedInstallationLogbookUnsubscribe, openOverviewSignalUnsubscribe);
    });

    function createSearchManagers() {
        const optimisationTableSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createOptimisationTableStaticParams());
        context.optimisationTableSearchContext = optimisationTableSearchManagerBuilder.createLocalSearch(true);
    }

    function createOptimisationTableStaticParams(): RecordSearchParams {
        return {
            recordRelatedRecord: context.projectRecord.id,
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: SUTOR_REPORTS_OPTIMISATION_PAGE.fond}],
            pageSize: 100
        };
    }

    const handleUnselectOptimisedInstallationLogbook = () => {
        context.setOptimisedInstallationLogbookRecord(null);
    };

    const handleUnselectOverviewedInstallationLogbook = () => {
        context.setOpenOverviewSignal(null);
        tabToSelect = 'tab-editation';
    };

    const handleTabChange = (event: CustomEvent<TabId>) => {
        if (event.detail === 'tab-editation') {
            context.setOptimisedInstallationLogbookRecord(null);
            context.setOpenOverviewSignal(null);
        }
    };
</script>

<KpLoadableContainer fillSize {loading}>
    <ErpTabbedSubpagesContainer {tabButtons}
                                bind:activeTab
                                bind:tabToSelect
                                on:tab-change={handleTabChange}
                                additionalClasses="reports-optimisation-tabbed-subpages"
                                withoutUrlManagement>

        <svelte:fragment slot="additional-content">
            {#if activeTab === 'tab-overviews' && exists(overviewedInstallationLogbook)}
                <KpVerticalSeparator height="16px"/>

                <div class="additional-content-container" in:fly={{x: 10, duration: 250}}>
                    <KpButton buttonStyle="brand-orange-new" on:click={handleUnselectOverviewedInstallationLogbook}>
                        <IconedContent icon="arrow-small-left">Zpět na tabulku</IconedContent>
                    </KpButton>
                </div>
            {/if}

            {#if activeTab === 'tab-editation'}
                <KpVerticalSeparator height="16px"/>

                {#if exists(optimisedInstallationLogbook)}
                    <div class="additional-content-container" in:fly={{x: 10, duration: 250}}>
                        <KpButton buttonStyle="brand-orange-new" on:click={handleUnselectOptimisedInstallationLogbook}>
                            <IconedContent icon="cross-circle">Zavřít optimalizaci</IconedContent>
                        </KpButton>

                        <LabeledValue label="Optimalizovaný MD">{optimisedInstallationLogbook.name}</LabeledValue>
                    </div>
                {:else}
                    <div class="additional-content-container" in:fly={{x: 10, duration: 250}}>
                        <small class="text-muted">Celkové součty:</small>

                        {#if !exists(optimisationSums)}
                            <KpLoadingBlock size="xs"/>
                        {:else}
                            <LabeledValue label="Vykázáno">{pipe(optimisationSums.reportedPrice, priceFormat)}</LabeledValue>
                            <LabeledValue label="Účtováno">{pipe(optimisationSums.chargedPrice, priceFormat)}</LabeledValue>
                        {/if}
                    </div>
                {/if}
            {/if}
        </svelte:fragment>

        <svelte:component this="{tabs[activeTab].component}" {...tabs[activeTab].props ?? {}}/>
    </ErpTabbedSubpagesContainer>
</KpLoadableContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .additional-content-container {
        display: flex;
        align-items: center;
        gap: @spacing-l;

        small {
            white-space: nowrap;
        }
    }
</style>