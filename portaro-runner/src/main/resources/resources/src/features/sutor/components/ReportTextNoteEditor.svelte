<script lang="ts">
    import type {TextValueEditorOptions} from 'shared/value-editors/internal/editors/text/types';
    import {fly} from 'svelte/transition';
    import KpValueEditor from 'shared/value-editors/kp-value-editor/KpValueEditor.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    export let textNote: string;
    export let editable: boolean;

    export let editing = false;

    function getAceEditorOptions(): TextValueEditorOptions {
        return {
            type: 'rich-textarea',
            aceOptions: {
                showPrintMargin: false
            }
        };
    }
</script>

<div class="report-text-note-editor">
    <Flex alignItems="center" gap="m">
        <span class="editor-heading">Poznámka</span>

        {#if editable}
            <KpIconButton noBackground icon="edit" on:click={() => editing = !editing}/>
        {/if}
    </Flex>

    {#key editing}
        <div class="anim-container" in:fly={{y: 10, duration: 250}}>
            {#if !editing}
                <span class="text-note">{textNote ? textNote : '-'}</span>
            {:else}
                <KpValueEditor type="text"
                               options="{getAceEditorOptions()}"
                               bind:model="{textNote}"/>

                <small class="small-text">Poznámka se uloží po schválení/odmítnutí výkazu</small>
            {/if}
        </div>
    {/key}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .report-text-note-editor {
        display: flex;
        flex-direction: column;
        gap: @spacing-xs;

        .editor-heading {
            font-size: @font-size-small;
            color: @themed-text-muted;
        }

        .text-note {
            white-space: pre-wrap;
            font-weight: 500;
            font-size: @font-size-large;
        }

        .small-text {
            color: @themed-text-muted-label;
        }
    }

    :global {
        .report-text-note-editor .ace-editor-wrapper {
            .ace-editor {
                height: 200px;
                min-height: 200px;
            }
        }
    }
</style>