<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getSutorReportsOptimisationContext} from '../../sutor-reports-optimisation-context';
    import {get} from 'svelte/store';
    import {createEventDispatcher, onDestroy} from 'svelte';
    import {getTextFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
    import {FOND_REPORT} from 'src/features/sutor/sutor-fonds';
    import KpBarebonesTableSelectableRow from 'shared/ui-widgets/table/barebones/KpBarebonesTableSelectableRow.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import Checkbox from 'shared/ui-widgets/checkbox/Checkbox.svelte';

    export let record: RecordRow;
    export let checked: boolean;

    const workerField = getFirstFieldByFieldTypeIdRecursive(record, FOND_REPORT.workerFieldId);
    const projectField = getFirstFieldByFieldTypeIdRecursive(record, FOND_REPORT.projectFieldId);
    const jobTypeField = getFirstFieldByFieldTypeIdRecursive(record, FOND_REPORT.WORK.jobTypeFieldId);
    const workPlaceField = getFirstFieldByFieldTypeIdRecursive(record, FOND_REPORT.WORK.placeFieldId);

    const context = getSutorReportsOptimisationContext();
    const dispatch = createEventDispatcher<{'user-selected': {used: boolean, report: RecordRow}}>();

    let openedUser = get(context.openedUser$);
    let usedForOptimisation = get(context.usedReportsForOptimisation$);
    const openedUserUnsubscribe = context.openedUser$.subscribe((currentOpenedUser) => openedUser = currentOpenedUser);
    const usedForOptimisationUnsubscribe = context.usedReportsForOptimisation$.subscribe((currentUsedForOptimisation) => usedForOptimisation = currentUsedForOptimisation);

    onDestroy(() => {
        openedUserUnsubscribe();
        usedForOptimisationUnsubscribe();
    });

    const handleCheckboxChange = (event: CustomEvent<{checked: boolean}>) => {
        dispatch('user-selected', {
            used: event.detail.checked,
            report: record
        });
    };
</script>

<KpBarebonesTableSelectableRow on:click={() => context.setOpenedUser(record)}
                               selected="{openedUser?.id === record.id}"
                               highlightStyle="{usedForOptimisation.includes(record.id) ? 'success' : null}">

    <td>
        <Checkbox {checked} on:change={handleCheckboxChange}/>
    </td>

    <!-- Worker -->
    <td>
        <GridFieldValue field="{workerField}">
            {#if hasRecordReference(workerField)}
                <KpUserAvatar user="{null}"
                              sizePx="{18}"
                              userId="{workerField.recordReference.id}"
                              userName="{getTextFromGridFieldValue(workerField)}"/>
            {/if}

            {getTextFromGridFieldValue(workerField)}
        </GridFieldValue>
    </td>

    <!-- Division -->
    <td>-</td>

    <!-- Job type -->
    <td>
        <GridFieldValue field="{jobTypeField}"/>
    </td>

    <!-- Place -->
    <td>
        <GridFieldValue field="{workPlaceField}"/>
    </td>

    <!-- Project -->
    <td>
        <GridFieldValue lengthLimit="{25}" field="{projectField}"/>
    </td>
</KpBarebonesTableSelectableRow>