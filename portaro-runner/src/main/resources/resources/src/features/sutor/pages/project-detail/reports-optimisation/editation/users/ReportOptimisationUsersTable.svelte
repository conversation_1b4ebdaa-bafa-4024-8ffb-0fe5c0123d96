<script lang="ts">
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getSutorReportsOptimisationContext} from '../../sutor-reports-optimisation-context';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import {getDateFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {exists} from 'shared/utils/custom-utils';
    import {SUTOR_REPORTS_OPTIMISATION_PAGE} from 'src/features/sutor/sutor-constants';
    import {FOND_INSTALLATION_LOGBOOK} from 'src/features/sutor/sutor-fonds';
    import KpPageableSearchResults from '../../../../../../search/kp-search-results/KpPageableSearchResults.svelte';
    import KpBarebonesTable from 'shared/ui-widgets/table/barebones/KpBarebonesTable.svelte';
    import ReportOptimisationUserItem from './ReportOptimisationUserItem.svelte';
    import KpSearchContext from 'src/features/search/kp-search-context/KpSearchContext.svelte';
    import KpSearchToolbar from 'src/features/search/kp-search-toolbar/KpSearchToolbar.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import LabeledValue from 'src/features/sutor/pages/project-detail/reports-optimisation/overviews/components/LabeledValue.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import KpPluralizeText from 'shared/components/kp-pluralize-text/KpPluralizeText.svelte';

    const context = getSutorReportsOptimisationContext();

    export let optimisedInstallationLogbook: RecordRow;

    let selectedUsers: RecordRow[] = [];
    let saving = false;

    function createUsersListStaticParams(): RecordSearchParams {
        const date = getDateFromGridFieldValue(getFirstFieldByFieldTypeIdRecursive(optimisedInstallationLogbook, FOND_INSTALLATION_LOGBOOK.dateFieldId));

        return {
            qt: `{"and": [{"not": {"field": "recordRelatedRecord", "eq": {"value": "${context.projectRecord.id}"}}}, {"field": "reportDate", "eq": {"value": "${date.toISODate()}"}}]}`,
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: SUTOR_REPORTS_OPTIMISATION_PAGE.userListFond}],
            pageSize: 100
        };
    }

    const handleUserSelection = (event: CustomEvent<{used: boolean, report: RecordRow}>) => {
        if (event.detail.used) {
            if (exists(selectedUsers.find((report) => report.id === event.detail.report.id))) {
                return;
            }

            selectedUsers = [...selectedUsers, event.detail.report];
            return;
        }

        selectedUsers = selectedUsers.filter((report) => report.id !== event.detail.report.id);
    };

    const handleUseForOptimisation = async () => {
        saving = true;

        const successfullyAddedIds = await context.service.useForOptimisationBulk(context.projectRecord, optimisedInstallationLogbook, selectedUsers);

        successfullyAddedIds.forEach((id) => {
            if (exists(id)) {
                context.addUsedReportForOptimisation(id);
                selectedUsers = selectedUsers.filter((report) => report.id !== id);
            }
        });

        saving = false;
    };

    const handleCancelSelection = () => {
        selectedUsers = [];
    };
</script>

<Flex direction="column" gap="xxl" fillAvailableSpace>
    <Flex alignItems="center" gap="ml">
        <LabeledValue label="Vybráno k optimalizaci">
            <KpPluralizeText count="{selectedUsers.length}"
                             zeroCountText="0 pracovníků"
                             oneCountText="1 pracovník"
                             twoThreeFourCountText="{selectedUsers.length} pracovníci"
                             moreText="{selectedUsers.length} pracovníků"/>
        </LabeledValue>

        <Spacer flex="{1}"/>

        <KpButton isDisabled="{saving || selectedUsers.length === 0}" on:click={handleCancelSelection}>
            <IconedContent icon="cross-circle">
                Zrušit označení
            </IconedContent>
        </KpButton>

        <KpButton isDisabled="{saving || selectedUsers.length === 0}" buttonStyle="success-new" on:click={handleUseForOptimisation}>
            <IconedContent icon="check-circle">
                Použít hromadně k optimalizaci
            </IconedContent>
        </KpButton>
    </Flex>

    <KpSearchContext staticParams="{createUsersListStaticParams()}" localSearch let:searchManager>
        <KpSearchToolbar/>

        <KpPageableSearchResults pagination="{searchManager.getPagination()}" let:paginationData>
            <KpBarebonesTable fontSize="12px">
                <tr slot="header">
                    <th class="checkbox-header"></th>
                    <th>Pracovník</th>
                    <th>Divize</th>
                    <th>Pracovní pozice</th>
                    <th>Místo</th>
                    <th>Zakázka</th>
                </tr>

                <svelte:fragment slot="body">
                    {#each paginationData.items as record(record.id)}
                        <ReportOptimisationUserItem checked="{exists(selectedUsers.find((e) => e.id === record.id))}"
                                                    on:user-selected={handleUserSelection}
                                                    {record}/>
                    {/each}
                </svelte:fragment>
            </KpBarebonesTable>
        </KpPageableSearchResults>
    </KpSearchContext>
</Flex>

<style lang="less">
    .checkbox-header {
        width: 36px;
    }
</style>