<script lang="ts">
    import type {DraggableDashboardCard} from 'src/features/erp/components/erp-draggable-dashboard/types';
    import type {SutorHomePageData} from 'src/features/sutor/pages/homepage/types';
    import ErpDraggableDashboard from 'src/features/erp/components/erp-draggable-dashboard/ErpDraggableDashboard.svelte';
    import EmptyCardContent from 'src/features/sutor/pages/homepage/parts/cards/EmptyCardContent.svelte';
    import LinksCard from 'src/features/sutor/pages/homepage/parts/cards/LinksCard.svelte';

    export let homepageData: SutorHomePageData;

    const cards: DraggableDashboardCard[] = [
        {
            id: 1,
            title: 'Rychlé odkazy',
            colSpan: 3,
            cardContentComponent: LinksCard,
            componentProps: {homepageData}
        },
        {
            id: 2,
            title: 'Zak<PERSON>zky',
            colSpan: 2,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 3,
            title: '<PERSON>sobn<PERSON> kalend<PERSON>ř',
            colSpan: 1,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 4,
            title: 'Uživatelé Sutin 2.0',
            colSpan: 1,
            cardContentComponent: EmptyCardContent
        },
        {
            id: 5,
            title: 'Finanční toky',
            colSpan: 2,
            cardContentComponent: EmptyCardContent
        }
    ];
</script>

<ErpDraggableDashboard {cards}/>