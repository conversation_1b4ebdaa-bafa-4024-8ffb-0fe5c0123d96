<script lang="ts">
    import {getSutorProjectsContext} from 'src/features/sutor/pages/projects/projects.context';
    import {FOND_PROJECT} from 'src/features/sutor/sutor-fonds';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';

    const context = getSutorProjectsContext();
</script>

<KpRecordGrid injectedSearchManager="{context.allProjectsSearchManager}"
              fondOrFondId="{FOND_PROJECT.fond}"/>