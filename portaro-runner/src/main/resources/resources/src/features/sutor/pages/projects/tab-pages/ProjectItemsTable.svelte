<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {fly} from 'svelte/transition';
    import {SUTOR_PROJECTS_PAGE} from 'src/features/sutor/sutor-constants';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';

    export let record: RecordRow;
    export let shown = false;
    export let columns: number;
    export let projectType: 'reported' | 'contracted';
    export let width: number;

    let shownBefore = false;
    $: if (shown) shownBefore = true;

    const projectTypeItemsFond = {
        'reported': SUTOR_PROJECTS_PAGE.reportedProjectItemsFond,
        'contracted': SUTOR_PROJECTS_PAGE.contractedProjectItemsFond
    };
</script>

{#if shown || shownBefore}
    <tr class="project-items-table-row"
        class:row-hidden={!shown}>
        <td colspan="{columns}" class="reported-project-items-table-container">
            <div class="inner-container"
                 in:fly={{y: 10, duration: 250}}
                 style:max-width="{width}px">

                <KpRecordGrid fondOrFondId="{projectTypeItemsFond[projectType]}"
                              relatedRecordId="{record.id}"/>
            </div>
        </td>
    </tr>
{/if}

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    .project-items-table-row.row-hidden {
        display: none;
    }

    .reported-project-items-table-container {
        display: table-cell;
        width: 100%;

        .inner-container {
            display: flex;
            width: 100%;
            flex-direction: column;
            padding-top: @spacing-ml;
            margin-bottom: @spacing-xl;
            padding-left: @spacing-m;
            border-left: 2px solid var(--accent-blue-new);
        }
    }

    :global {
        .reported-project-items-table-container .kp-record-grid-container {
            gap: @spacing-m;
        }
    }
</style>