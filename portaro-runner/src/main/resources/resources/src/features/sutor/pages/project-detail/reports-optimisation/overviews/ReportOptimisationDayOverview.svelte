<script lang="ts">
    import type {CalendarDate} from 'shared/components/kp-calendar/types';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {DayOptimisationChartDataPoint, DayOptimisationOverviewResponse} from 'src/features/sutor/pages/project-detail/reports-optimisation/types';
    import type {DonutChartDataPoint} from 'shared/components/kp-charts/donut-chart/types';
    import {calendarDateFromLuxon, luxonDateFromCalendarDate} from 'shared/components/kp-calendar/utils';
    import {getSutorReportsOptimisationContext} from 'src/features/sutor/pages/project-detail/reports-optimisation/sutor-reports-optimisation-context';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getDateFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {getColorFromString} from 'shared/utils/color-util';
    import {get} from 'svelte/store';
    import {DateTime} from 'luxon';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {FOND_INSTALLATION_LOGBOOK} from 'src/features/sutor/sutor-fonds';
    import OptimisationOverviewContainer from './components/OptimisationOverviewContainer.svelte';
    import OptimisationOverviewHeading from './components/OptimisationOverviewHeading.svelte';
    import DayOverviewJobTypeCard from './components/DayOverviewJobTypeCard.svelte';
    import ErpRowCalendar from 'src/features/erp/components/erp-row-calendar/ErpRowCalendar.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpLoadableContainer from 'shared/layouts/containers/KpLoadableContainer.svelte';
    import KpDonutChart from 'shared/components/kp-charts/donut-chart/KpDonutChart.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';

    const context = getSutorReportsOptimisationContext();
    const openOverviewSignalInstallationLogbook = get(context.openOverviewSignal$);

    let loading = true;
    let selectedDate = getSelectedDate(openOverviewSignalInstallationLogbook);
    let selectedInstallationLogbook: RecordRow | null = openOverviewSignalInstallationLogbook;
    let dayOptimisationOverviewResponse: DayOptimisationOverviewResponse | null;

    onMount(async () => {
        if (!exists(selectedInstallationLogbook)) {
            loading = false;
            return;
        }

        await loadDayOptimisationOverview(selectedDate);
    });

    function getSelectedDate(installationLogbook: RecordRow): CalendarDate {
        const dateField = exists(installationLogbook) ? getFirstFieldByFieldTypeIdRecursive(installationLogbook, FOND_INSTALLATION_LOGBOOK.dateFieldId) : null;
        const date = getDateFromGridFieldValue(dateField);

        if (!exists(date)) {
            return calendarDateFromLuxon(DateTime.now());
        }

        return calendarDateFromLuxon(date);
    }

    async function loadDayOptimisationOverview(date: CalendarDate) {
        loading = true;
        const projectRecord = context.projectRecord;
        dayOptimisationOverviewResponse = await context.service.getDayOverview(projectRecord, selectedInstallationLogbook, luxonDateFromCalendarDate(date));
        loading = false;
    }

    function transformDataIntoChartData(dataPoints: DayOptimisationChartDataPoint[]): DonutChartDataPoint[] {
        return dataPoints.map((dataPoint) => {
            return {
                label: dataPoint.jobTypeName,
                value: dataPoint.price.amount,
                color: getColorFromString(dataPoint.jobTypeName, 70)
            };
        });
    }

    function getHeadingText(installationLogbook: RecordRow): string {
        if (!exists(installationLogbook)) {
            return 'Přehled';
        }

        return `Přehled pro ${installationLogbook.name}`;
    }

    const handleDateSelected = async (event: CustomEvent<CalendarDate>) => {
        selectedDate = event.detail;
        selectedInstallationLogbook = null;
        await loadDayOptimisationOverview(selectedDate);
    };
</script>

<OptimisationOverviewContainer>
    {#if !exists(openOverviewSignalInstallationLogbook)}
        <ErpRowCalendar startDate="{selectedDate}"
                        selectedDate="{selectedDate}"
                        on:date-selected={handleDateSelected}/>
    {/if}

    <OptimisationOverviewHeading heading="{getHeadingText(selectedInstallationLogbook)}"
                                 printDisabled="{!exists(selectedInstallationLogbook)}"
                                 printButtonLabel="Tisk montážního deníku"/>

    {#if !exists(selectedInstallationLogbook)}
        <IconedContent icon="info" orientation="vertical" align="center" justify="center">
            <span class="load-error-label">Nejdříve vyberte montážní deník</span>
        </IconedContent>
    {:else}
        <KpLoadableContainer fillSize {loading} loadError="{!loading && !exists(dayOptimisationOverviewResponse)}">
            <Flex direction="column" gap="xxl">
                {#if dayOptimisationOverviewResponse.jobTypeSummaries.length === 0}
                    <IconedContent icon="info" orientation="vertical" align="center" justify="center">
                        <span class="load-error-label">Nejsou k dispozici žádná data</span>
                    </IconedContent>
                {:else}
                    <Flex gap="xxl">
                        <div class="chart-container">
                            <KpDonutChart size="200px" withoutSideLegend data="{transformDataIntoChartData(dayOptimisationOverviewResponse.chartData.reportedDataPoints)}"/>
                            <div class="chart-label">Vykázaná data</div>
                        </div>

                        <div class="chart-container">
                            <KpDonutChart size="200px" withoutSideLegend data="{transformDataIntoChartData(dayOptimisationOverviewResponse.chartData.chargedDataPoints)}"/>
                            <div class="chart-label">Účtovaná data</div>
                        </div>
                    </Flex>

                    <div class="job-type-summaries-container">
                        {#each dayOptimisationOverviewResponse.jobTypeSummaries as jobTypeSummary}
                            <DayOverviewJobTypeCard {jobTypeSummary}/>
                        {/each}
                    </div>
                {/if}
            </Flex>
        </KpLoadableContainer>
    {/if}
</OptimisationOverviewContainer>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .chart-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: @spacing-ml;

        .chart-label {
            color: @themed-text-muted;
        }
    }

    .job-type-summaries-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-xl;
    }
</style>