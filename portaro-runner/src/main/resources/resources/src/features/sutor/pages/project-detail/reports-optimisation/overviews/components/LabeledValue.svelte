<script lang="ts">
    export let label: string;
    export let alignToEnd = false;
</script>

<div class="labeled-value-container" class:align-to-end={alignToEnd}>
    <span class="value-label">{label}</span>

    <span class="value">
        <slot/>
    </span>
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .labeled-value-container {
        display: flex;
        flex-direction: column;
        align-items: start;
        gap: @spacing-xs;

        &.align-to-end {
            align-items: end;
        }

        .value-label {
            white-space: nowrap;
            font-size: @font-size-small;
            color: @themed-text-muted;
        }

        .value {
            font-weight: 500;
            white-space: nowrap;
            font-size: @font-size-large;
        }
    }
</style>