<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getIdFromGridFieldValue, getTextFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
    import {SUTOR_PROJECTS_SETTINGS} from 'src/features/sutor/sutor-constants';
    import KpBarebonesTableSelectableRow from 'shared/ui-widgets/table/barebones/KpBarebonesTableSelectableRow.svelte';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';

    export let record: RecordRow;
    export let rowSelected: boolean;

    const projectNameField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.projectNameFieldId);
    const projectNumberField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.projectNumberFieldId);
    const userField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.responsibleUserFieldId);
    const customerField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.customerFieldId);
    const contractorField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.contractorFieldId);
    const contractField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.contractFieldId);
    const parentProjectField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.parentProjectFieldId);
    const typeField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.typeFieldId);
    const stateField = getFirstFieldByFieldTypeIdRecursive(record, SUTOR_PROJECTS_SETTINGS.stateFieldId);
</script>

<KpBarebonesTableSelectableRow selected="{rowSelected}" on:click>
    <td>
        <GridFieldValue href="/#!/records/{record.id}"
                        value="{getTextFromGridFieldValue(projectNameField)}"
                        fondId="{record.fond.id}"/>
    </td>

    <td>
        <GridFieldValue field="{projectNumberField}"/>
    </td>

    <td>
        <GridFieldValue field="{userField}">
            {#if hasRecordReference(userField)}
                <KpUserAvatar user="{null}"
                              sizePx="{18}"
                              userId="{userField.recordReference.id}"
                              userName="{getTextFromGridFieldValue(userField)}"/>
            {/if}

            {getTextFromGridFieldValue(userField)}
        </GridFieldValue>
    </td>

    <td>
        <GridFieldValue field="{customerField}"/>
    </td>

    <td>
        <GridFieldValue field="{contractorField}"/>
    </td>

    <td>
        <GridFieldValue field="{contractField}"/>
    </td>

    <td>
        <GridFieldValue field="{parentProjectField}"/>
    </td>

    <td>
        <KpChipTag chipSize="xs" chipStyle="{SUTOR_PROJECTS_SETTINGS.projectTypeChipStyles[getIdFromGridFieldValue(typeField)] ?? 'default'}">
            {getTextFromGridFieldValue(typeField)}
        </KpChipTag>
    </td>

    <td>
        <KpChipTag chipSize="xs">
            {getTextFromGridFieldValue(stateField)}
        </KpChipTag>
    </td>
</KpBarebonesTableSelectableRow>