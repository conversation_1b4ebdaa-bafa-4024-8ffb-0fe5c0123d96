import type {FieldTypeId} from 'typings/portaro.be.types';

export const FOND_PROJECT = {
    fond: 1,

    // All supported project subfonds
    subfonds: [4, 5],

    projectNameFieldId: 'd1110.a' as FieldTypeId,
    projectNumberFieldId: 'd1110.z' as FieldTypeId,
    responsibleUserFieldId: 'd1112.main' as FieldTypeId,
    customerFieldId: 'd1010.a' as FieldTypeId,
    contractorFieldId: 'd1012.main' as FieldTypeId,
    contractFieldId: 'd1015.main' as FieldTypeId,
    stateFieldId: 'd1105' as FieldTypeId,
    typeFieldId: 'd1106.t' as FieldTypeId,
    parentProjectFieldId: 'd1120.main' as FieldTypeId,

    // Project type chip styles
    TYPE_CHIP_STYLES: {
        'INTERNAL': 'accent-blue-new',
        'EXTERNAL': 'brand-orange-new'
    },

    // Search facets
    FACETS: {
        state: 'SUTOR_STAV_ZAKAZKA'
    },

    // Subfonds
    REPORTED: {
        fond: 5
    },
    CONTRACTED: {
        fond: 4,

        startDateFieldId: 'd1041.d' as FieldTypeId,
        endDateFieldId: 'd1043.d' as FieldTypeId,
        priceFieldId: 'd1150.c' as FieldTypeId,
        priceCurrencyFieldId: 'd1150.j' as FieldTypeId
    }
};

export const FOND_REFERENCE = {
    fond: 80,

    // Reference state chip styles
    STATE_CHIP_STYLES: {
        'NEW': 'accent-blue-new',
        'DONE': 'success-new',
        'INPROGRESS': 'brand-orange-new'
    } as const,

    // Search facets
    FACETS: {
        state: 'SUTOR_STAV_PRUVODKA'
    },

    nameFieldId: 'd8000.a' as FieldTypeId,
    stateFieldId: 'd8005.h' as FieldTypeId,
    userFieldId: 'd8010' as FieldTypeId
};

export const FOND_PERSON = {
    fond: 7,

    positionFieldId: 'd1725' as FieldTypeId,
    employerFieldId: 'd1750' as FieldTypeId,
    firstNameFieldId: 'd1710.b' as FieldTypeId,
    surnameFieldId: 'd1710.a' as FieldTypeId,
    titleFieldId: 'd1710.c' as FieldTypeId
};

export const FOND_INSTALLATION_LOGBOOK = {
    fond: 78,

    dateFieldId: 'd1681.d' as FieldTypeId
};

export const FOND_REPORT = {
    fond: 10,

    // All supported report subfonds
    subfondsForApproval: [20, 21, 22, 26, 37],

    // Shared fields
    projectFieldId: 'd1001.main' as FieldTypeId,
    projectItemFieldId: 'd2025.main' as FieldTypeId,
    dateFieldId: 'd2030.d' as FieldTypeId,
    stateFieldId: 'd2096.s' as FieldTypeId,
    textNoteFieldId: 'd2091.a' as FieldTypeId,
    descriptionFieldId: 'd2010.a' as FieldTypeId,
    referenceFieldId: 'd2012.a' as FieldTypeId,
    installationLogbookFieldId: 'd2025' as FieldTypeId,
    workerFieldId: 'd2050.main' as FieldTypeId,
    machineFieldId: 'd2060.main' as FieldTypeId,

    // Report state chip styles
    STATE_CHIP_STYLES: {
        'TO_CONFIRM': 'accent-blue-new',
        'CONFIRMED': 'success-new',
        'RETURNED': 'danger-new'
    } as const,

    // Search facets
    FACETS: {
        state: 'SUTOR_STAV_VYKAZ'
    },

    // Subfonds
    MACHINE_WITH_OPERATOR: {
        fond: 20
    },
    WORK: {
        fond: 21,

        typeFieldId: 'd2035.t' as FieldTypeId,
        placeFieldId: 'd2094.a' as FieldTypeId,
        partnerFieldId: 'd2093.a' as FieldTypeId,
        jobTypeFieldId: 'd2040' as FieldTypeId,
        reportedStandardHoursFieldId: 'd2070.m' as FieldTypeId,
        reportedOvertimeHoursFieldId: 'd2070.n' as FieldTypeId
    },
    MACHINE: {
        fond: 22
    },
    ABSENCE: {
        fond: 26,

        typeFieldId: 'd2035.t' as FieldTypeId,
        reportedStandardHoursFieldId: 'd2070.m' as FieldTypeId
    },
    OPERATION: {
        fond: 37
    },
    OPTIMISATION: {
        fond: 24,

        typeFieldId: 'd2035.t' as FieldTypeId
    }
};