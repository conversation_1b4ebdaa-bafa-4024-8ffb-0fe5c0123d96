<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {getIdFromGridFieldValue, getTextFromGridFieldValue} from '../../../../../sutor-utils';
    import {getSutorReportsOptimisationContext} from '../../sutor-reports-optimisation-context';
    import {hasRecordReference} from 'src/features/record-grid/lib/types-utils';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {onMount} from 'svelte';
    import {exists} from 'shared/utils/custom-utils';
    import {FOND_REPORT} from '../../../../../sutor-fonds';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpSingularFileViewer from '../../../../../../media-viewer/KpSingularFileViewer.svelte';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import Flex from 'shared/layouts/flex/Flex.svelte';
    import LabeledValue from 'src/features/sutor/pages/project-detail/reports-optimisation/overviews/components/LabeledValue.svelte';
    import GridFieldValue from 'shared/ui-widgets/grid/GridFieldValue.svelte';
    import Spacer from 'shared/layouts/flex/Spacer.svelte';
    import ReportTextNoteEditor from 'src/features/sutor/components/ReportTextNoteEditor.svelte';

    export let reportRecord: RecordRow;
    export let optimisedInstallationLogbook: RecordRow;

    const context = getSutorReportsOptimisationContext();

    const stateField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.stateFieldId);
    const textNoteField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.textNoteFieldId);
    const dateField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.dateFieldId);
    const workTypeField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.WORK.typeFieldId);
    const referenceField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.referenceFieldId);
    const projectItemField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.projectItemFieldId);
    const workerField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.workerFieldId);
    const projectField = getFirstFieldByFieldTypeIdRecursive(reportRecord, FOND_REPORT.projectFieldId);

    let saving = false;
    let referencePdfFile: ViewableFile | null = null;
    let textNoteModel = getTextFromGridFieldValue(textNoteField, '');

    onMount(async () => {
        referencePdfFile = await context.service.getReportReferencePdfFile(referenceField);
    });

    const handleUseForOptimisation = async () => {
        saving = true;
        const success = await context.service.useForOptimisation(context.projectRecord, optimisedInstallationLogbook, reportRecord);

        if (exists(success)) {
            context.addUsedReportForOptimisation(reportRecord.id);
        }

        saving = false;
    };
</script>

<Flex class="report-optimisation-user-detail" direction="column" gap="xxl">
    <Flex class="state-buttons-container" alignItems="center" gap="s">
        <KpButton buttonStyle="success-new" isDisabled="{saving}" on:click={handleUseForOptimisation}>
            <IconedContent icon="check-circle">Použít k optimalizaci</IconedContent>
        </KpButton>

        <Spacer flex="1"/>

        <LabeledValue label="Stav schválení" alignToEnd>
            <KpChipTag chipSize="sm"
                       chipStyle="{FOND_REPORT.STATE_CHIP_STYLES[getIdFromGridFieldValue(stateField)] ?? 'default'}">

                {getTextFromGridFieldValue(stateField)}
            </KpChipTag>
        </LabeledValue>
    </Flex>

    <Flex>
        {#if exists(workerField)}
            <GridFieldValue field="{workerField}">
                {#if hasRecordReference(workerField)}
                    <KpUserAvatar user="{null}"
                                  sizePx="{32}"
                                  userId="{workerField.recordReference.id}"
                                  userName="{getTextFromGridFieldValue(workerField)}"/>
                {/if}

                <span class="username">{getTextFromGridFieldValue(workerField)}</span>
            </GridFieldValue>
        {:else}
            LINK NEEXISTUJE
        {/if}
    </Flex>

    <Flex class="details-container" wrap="wrap" columnGap="xxl" rowGap="ml">
        <LabeledValue label="Zakázka">
            <GridFieldValue lengthLimit="{25}" field="{projectField}" chipSize="sm"/>
        </LabeledValue>

        <LabeledValue label="Položka zakázky">
            <GridFieldValue lengthLimit="{25}" field="{projectItemField}" chipSize="sm"/>
        </LabeledValue>

        <LabeledValue label="Datum">
            <GridFieldValue field="{dateField}" chipSize="sm"/>
        </LabeledValue>

        <LabeledValue label="Typ">
            <GridFieldValue field="{workTypeField}" chipSize="sm"/>
        </LabeledValue>
    </Flex>

    {#if exists(referencePdfFile)}
        <Flex direction="column" gap="s">
            <span class="heading-text">Průvodka</span>

            <div class="verbis-viewer-container">
                <KpSingularFileViewer file="{referencePdfFile}"/>
            </div>
        </Flex>
    {/if}

    <ReportTextNoteEditor bind:textNote={textNoteModel} editable="{false}"/>
</Flex>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .username {
        font-size: @font-size-xl;
    }

    .heading-text {
        font-size: @font-size-small;
        color: @themed-text-muted;
    }

    .verbis-viewer-container {
        position: relative;
        height: 500px;
        border-top: 1px solid @themed-border-default;
        border-bottom: 1px solid @themed-border-default;
        margin-left: calc(@spacing-xl * -1);
        margin-right: calc(@spacing-xxl * -1);
        width: calc(@spacing-xl + 100% + @spacing-xxl);
    }
</style>