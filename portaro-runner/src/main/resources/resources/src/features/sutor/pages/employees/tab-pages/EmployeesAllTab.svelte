<script lang="ts">
    import {getSutorEmployeesContext} from 'src/features/sutor/pages/employees/employees.context';
    import {FOND_PERSON} from 'src/features/sutor/sutor-fonds';
    import KpRecordGrid from 'src/features/record-grid/KpRecordGrid.svelte';

    const context = getSutorEmployeesContext();
</script>

<KpRecordGrid injectedSearchManager="{context.allEmployeesSearchManager}"
              fondOrFondId="{FOND_PERSON.fond}"
              searchType="record-name"/>