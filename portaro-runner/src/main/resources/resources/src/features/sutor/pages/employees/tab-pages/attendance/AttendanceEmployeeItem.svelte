<script lang="ts">
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import {getFirstFieldByFieldTypeIdRecursive} from 'src/features/record-grid/lib/grid-fields';
    import {getTextFromGridFieldValue} from 'src/features/sutor/sutor-utils';
    import {heightResizeAnimation} from 'shared/svelte-actions/use.height-resize-animation';
    import {exists} from 'shared/utils/custom-utils';
    import {noop} from 'rxjs';
    import {slide} from 'svelte/transition';
    import {FOND_PERSON} from 'src/features/sutor/sutor-fonds';
    import KpUserAvatar from 'shared/components/kp-user-avatar/KpUserAvatar.svelte';
    import KpChipTag from 'shared/ui-widgets/chip/KpChipTag.svelte';
    import KpIconButton from 'shared/ui-widgets/button/KpIconButton.svelte';
    import SutorAttendanceCalendar from 'src/features/sutor/components/attendance-calendar/SutorAttendanceCalendar.svelte';

    export let record: RecordRow;

    const firstNameField = getFirstFieldByFieldTypeIdRecursive(record, FOND_PERSON.firstNameFieldId);
    const surnameField = getFirstFieldByFieldTypeIdRecursive(record, FOND_PERSON.surnameFieldId);
    const titleField = getFirstFieldByFieldTypeIdRecursive(record, FOND_PERSON.titleFieldId);
    const positionField = getFirstFieldByFieldTypeIdRecursive(record, FOND_PERSON.positionFieldId);

    const slideAnimParams = {y: 10, duration: 250};

    let attendanceShown = false;

    function getFullName(): string {
        return `${titleField?.text ?? ''} ${firstNameField?.text ?? ''} ${surnameField?.text ?? ''}`.trim();
    }
</script>

<div class="employee-attendance-item">
    <div class="info-row"
         on:click={() => attendanceShown = !attendanceShown}
         on:keydown={noop}
         aria-controls="attendance-calendar-container"
         aria-expanded="{attendanceShown}"
         role="button"
         tabindex="0">

        <KpUserAvatar user="{null}"
                      sizePx="{64}"
                      userId="{record.id}"
                      userName="{record.id}"/>

        <div class="texts-container">
            <span class="user-name">{getFullName()}</span>

            {#if exists(positionField)}
                <KpChipTag chipSize="xs">
                    {getTextFromGridFieldValue(positionField)}
                </KpChipTag>
            {/if}
        </div>

        <KpIconButton href="/#!/records/{record.id}" icon="link" noBackground/>

        <div class="collapse-button-container">
            <KpIconButton icon="{attendanceShown ? 'angle-small-up' : 'angle-small-down'}"
                          on:click={() => attendanceShown = !attendanceShown}/>
        </div>
    </div>

    {#if attendanceShown}
        <div class="attendance-calendar-container" transition:slide={slideAnimParams}>
            <div use:heightResizeAnimation={{disabled: false, duration: 250}}>
                <SutorAttendanceCalendar userRecordId="{record.id}"
                                         type="month-with-summary"
                                         withoutLoadAnimations="{true}"/>
            </div>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .employee-attendance-item {
        display: flex;
        flex-direction: column;

        .info-row {
            gap: @spacing-m;
            position: relative;
            display: flex;
            align-items: center;
            cursor: pointer;
            isolation: isolate;
            transition: background-color 0.3s ease-in-out;

            &:hover:before {
                opacity: 1;
            }

            &:before {
                content: '';
                opacity: 0;
                z-index: -1;
                position: absolute;
                top: calc(@spacing-s * -1);
                left: calc(@spacing-m * -1);
                bottom: calc(@spacing-s * -1);
                right: calc(@spacing-m * -1);
                background-color: @themed-body-bg-blue-highlighted;
                border-radius: @border-radius-large;
                transition: opacity 0.3s ease-in-out;
            }

            .texts-container {
                display: flex;
                flex-direction: column;
                gap: @spacing-xs;

                .user-name {
                    font-size: @font-size-large;
                    font-weight: 500;
                }
            }

            .collapse-button-container {
                margin-left: auto;
            }
        }

        .attendance-calendar-container {
            margin-top: @spacing-ml;
        }
    }
</style>
