<script lang="ts">
    import type {TabButton} from 'shared/ui-widgets/tabset/types';
    import type {RecordSearchParams} from 'typings/portaro.be.types';
    import {getInjector} from 'core/svelte-context/context';
    import {createSutorProjectsContext} from 'src/features/sutor/pages/projects/projects.context';
    import {SearchManagerBuilderFactoryService} from 'src/features/search/search-manager/search-manager-factory.service';
    import {onMount} from 'svelte';
    import {Kind, SearchType} from 'shared/constants/portaro.constants';
    import CriterionFactory from 'src/features/search/search-criteria/criterion-factories/criterion.factory';
    import CriterionMarshaller from 'src/features/search/search-criteria/criterion-marshallers/criterion-marshaller';
    import {SUTOR_PROJECT_STATES, SUTOR_PROJECTS_SETTINGS} from 'src/features/sutor/sutor-constants';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpTabbedSubpagesContainer from 'src/features/erp/components/erp-tabbed-subpages/ErpTabbedSubpagesContainer.svelte';
    import SutorProjectsAllTab from './tab-pages/SutorProjectsAllTab.svelte';
    import SutorProjectsContractedTab from './tab-pages/contracted/SutorProjectsContractedTab.svelte';
    import SutorProjectsReportedTab from './tab-pages/reported/SutorProjectsReportedTab.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';

    const searchManagerBuilderFactoryService = getInjector().getByClass(SearchManagerBuilderFactoryService);
    const context = createSutorProjectsContext();
    const criterionFactory = new CriterionFactory();
    const criterionMarshaller = new CriterionMarshaller(criterionFactory);

    const inProgressStateCriterion = criterionFactory.createInCriterion(
        {id: SUTOR_PROJECTS_SETTINGS.statusFacet, text: SUTOR_PROJECTS_SETTINGS.statusFacet},
        {value: [SUTOR_PROJECT_STATES.IN_PROGRESS]}
    );

    const tabs = {
        'tab-all': {component: SutorProjectsAllTab},
        'tab-contracted': {component: SutorProjectsContractedTab},
        'tab-reported': {component: SutorProjectsReportedTab}
    };

    const tabButtons: TabButton[] = [
        {
            id: 'tab-all',
            label: 'Všechny zakázky',
            icon: 'info',
            tabPageWithoutPadding: true,
            tabPageContainerClass: 'record-grid-tab-page'
        },
        {
            id: 'tab-contracted',
            label: 'Zakázky smluvní',
            icon: 'money-check-edit'
        },
        {
            id: 'tab-reported',
            label: 'Zakázky vykazované',
            icon: 'edit'
        }
    ];

    let loading = true;

    onMount(() => {
        createSearchManagers();
        loading = false;
    });

    function createSearchManagers() {
        const allProjectsSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createProjectsStaticParams(SUTOR_PROJECTS_SETTINGS.allProjectsFond));
        context.allProjectsSearchManager = allProjectsSearchManagerBuilder.createLocalSearch();
        context.allProjectsSearchManager.newSearch();

        const contractedProjectsSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createProjectsStaticParams(SUTOR_PROJECTS_SETTINGS.contractedProjectsFond));
        context.contractedProjectsSearchManager = contractedProjectsSearchManagerBuilder.createLocalSearch();
        context.contractedProjectsSearchManager.newSearch();

        const reportedProjectsSearchManagerBuilder = searchManagerBuilderFactoryService.createBuilder<RecordSearchParams, any>().withStaticParams(createProjectsStaticParams(SUTOR_PROJECTS_SETTINGS.reportedProjectsFond));
        context.reportedProjectsSearchManager = reportedProjectsSearchManagerBuilder.createLocalSearch();
        context.reportedProjectsSearchManager.newSearch();
    }

    function createProjectsStaticParams(fondId: number): RecordSearchParams {
        return {
            kind: [Kind.KIND_RECORD],
            type: SearchType.TYPE_TABLE,
            rootFond: [{id: fondId}],
            pageSize: 100,
            facetRestriction: criterionMarshaller.fromCriterionsToConjunction([inProgressStateCriterion])
        };
    }
</script>

<ErpPageLayout pageClass="sutor-projects-page" gap="0px" withoutContentPadding {loading}>
    <ErpHeadingBar slot="heading" title="Zakázky" icon="check-circle"/>

    <ErpTabbedSubpagesContainer {tabButtons}
                                let:activeTab
                                additionalClasses="projects-tabbed-subpages">

        <svelte:component this="{tabs[activeTab].component}" {...tabs[activeTab].props ?? {}}/>
    </ErpTabbedSubpagesContainer>
</ErpPageLayout>