<script lang="ts">
    import {tooltip} from 'shared/ui-widgets/tooltip/use.tooltip';
    import {exists} from 'shared/utils/custom-utils';
    import {formatAbsenceReasons, formatDurationStringToHours} from 'src/features/sutor/components/attendance-calendar/utils';
    import type {
        AbsenceDurationItem,
        DescriptedDurationItem,
        DurationItem
    } from 'src/features/sutor/components/attendance-calendar/types';

    export let durationItem: DurationItem | null = null;
    export let header = false;
    export let topBordered = false;

    function isDescriptedDurationItem(item?: DurationItem): item is DescriptedDurationItem {
        return exists(item) && 'description' in item;
    }

    function isAbsenceDurationItem(item?: DurationItem): item is AbsenceDurationItem {
        return exists(item) && 'reasons' in item;
    }
</script>

<div class="attendance-calendar-cell"
     class:header-cell={header}
     class:top-bordered-cell={topBordered}
     class:errored-cell={isDescriptedDurationItem(durationItem) && exists(durationItem.errorDescription)}
     use:tooltip={{enabled: isDescriptedDurationItem(durationItem), content: isDescriptedDurationItem(durationItem) ? (durationItem.errorDescription ?? durationItem.description) : '', role: 'tooltip'}}>

    {#if exists(durationItem)}
        {formatDurationStringToHours(durationItem.duration)}

        {#if isAbsenceDurationItem(durationItem)}
            <br/>
            {formatAbsenceReasons(durationItem.reasons)}
        {/if}
    {/if}

    {#if exists($$slots.default)}
        <slot/>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .attendance-calendar-cell {
        height: 40px;
        display: flex;
        padding: 0 @spacing-sm;
        align-items: center;
        justify-content: center;

        &.header-cell {
            font-weight: 500;
            border-bottom: 1px solid @themed-border-default;
        }

        &.errored-cell {
            background-color: @themed-body-bg-red-highlighted;
            color: var(--danger-red);
        }

        &.top-bordered-cell {
            border-top: 1px solid @themed-border-default;
        }

        &:not(.header-cell) {
            transition: background-color 0.3s ease-in-out;

            &:hover {
                background-color: @themed-panel-bg;
            }
        }
    }
</style>