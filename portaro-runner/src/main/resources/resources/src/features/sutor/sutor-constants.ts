// States
export const SUTOR_REFERENCE_STATES = {
    NEW: 'NEW',
    DONE: 'DONE',
    IN_PROGRESS: 'INPROGRESS'
} as const;

export const SUTOR_REPORT_STATES = {
    CONFIRMED: 'CONFIRMED',
    IN_PROGRESS: 'IN_PROGRESS',
    TO_CONFIRM: 'TO_CONFIRM',
    RETURNED: 'RETURNED'
} as const;

export const SUTOR_PROJECT_STATES = {
    IN_PROGRESS: '50'
};

// Page settings
export const SUTOR_REPORTS_PROCESSING_PAGE = {
    fond: 90
};

export const SUTOR_REPORTS_APPROVING_PAGE = {
    fond: 91,
    reportRefreshEvent: 'sutor-reports-approving-refresh-event'
};

export const SUTOR_REPORTS_OPTIMISATION_PAGE = {
    fond: 102,
    userListFond: 21
};

export const SUTOR_PROJECTS_PAGE = {
    reportedProjectItemsFond: 78,
    contractedProjectItemsFond: 76
};