import type {FieldTypeId} from 'typings/portaro.be.types';

export const SUTOR_RECORD_DETAIL_SETTINGS = {
    projectFonds: [4, 5],
    projectReported: 5,
    userRecordFond: 7
};

export const SUTOR_REPORTS_REFERENCES_SETTINGS = {
    fond: 80,
    statusFacet: 'SUTOR_STAV_PRUVODKA',
    fileNameFieldId: 'd8000' as FieldTypeId,
    stateFieldId: 'd8005.h' as FieldTypeId,
    userFieldId: 'd8010' as FieldTypeId,
    referenceStateChipStyles: {
        'NEW': 'accent-blue-new',
        'DONE': 'success-new',
        'INPROGRESS': 'brand-orange-new'
    }
};

export const SUTOR_REFERENCE_STATES = {
    NEW: 'NEW',
    DONE: 'DONE',
    IN_PROGRESS: 'INPROGRESS'
} as const;

export const SUTOR_REPORT_STATES = {
    CONFIRMED: 'CONFIRMED',
    IN_PROGRESS: 'IN_PROGRESS',
    TO_CONFIRM: 'TO_CONFIRM',
    RETURNED: 'RETURNED'
} as const;

export const SUTOR_PROJECT_STATES = {
    IN_PROGRESS: '50'
};

export const SUTOR_REPORTS_PROCESSING_SETTINGS = {
    fond: 90,
    reportReferenceFieldId: 'd2012.a' as FieldTypeId,
    reportStateFieldId: 'd2096.s' as FieldTypeId,
    nameFieldId: 'd8000.a' as FieldTypeId,
    stateFieldId: 'd8005.h' as FieldTypeId,
    stateChipStyles: {
        'INPROGRESS': 'accent-blue-new',
        'DONE': 'success-new'
    }
};

export const SUTOR_REPORTS_APPROVING_SETTINGS = {
    fond: 91,

    reportStateFacet: 'SUTOR_STAV_VYKAZ',
    dateFieldId: 'd2030.d' as FieldTypeId,
    projectItemFieldId: 'd2025.main' as FieldTypeId,
    typeFieldId: 'd2035.t' as FieldTypeId,
    stateFieldId: 'd2096.s' as FieldTypeId,
    textNoteFieldId: 'd2091.a' as FieldTypeId,
    referenceFieldId: 'd2012.a' as FieldTypeId,
    // Work type fields
    workTimeFieldId: 'd2070.m' as FieldTypeId,
    workOvertimeFieldId: 'd2070.n' as FieldTypeId,
    workWorkerFieldId: 'd2050.main' as FieldTypeId,
    // Machine type fields
    machineNameFieldId: 'd2060' as FieldTypeId,
    machineTimeFieldId: 'd2073.m' as FieldTypeId,
    machineKilometersFieldId: 'd2073.k' as FieldTypeId,
    stateChipStyles: {
        'TO_CONFIRM': 'accent-blue-new',
        'CONFIRMED': 'success-new',
        'RETURNED': 'danger-new'
    } as const,
    reportRefreshEvent: 'sutor-reports-approving-refresh-event'
};

export const SUTOR_REPORTS_OPTIMISATION_SETTINGS = {
    fond: 102,
    mdFond: 78,
    userListFond: 21,
    projectFieldId: 'd1001.main' as FieldTypeId,
    optimisationReport: {
        fond: 24,
        projectFieldId: 'd1001.main' as FieldTypeId,
        dateFieldId: 'd2030.d' as FieldTypeId,
        typeFieldId: 'd2035.t' as FieldTypeId,
        workerFieldId: 'd2050.main' as FieldTypeId,
        descriptionFieldId: 'd2010.a' as FieldTypeId,
        installationLogbookFieldId: 'd2025' as FieldTypeId
    }
};

export const SUTOR_PROJECTS_SETTINGS = {
    allProjectsFond: 1,
    contractedProjectsFond: 4,
    reportedProjectsFond: 5,
    projectNameFieldId: 'd1110.a' as FieldTypeId,
    projectNumberFieldId: 'd1110.z' as FieldTypeId,
    responsibleUserFieldId: 'd1112.main' as FieldTypeId,
    customerFieldId: 'd1010.a' as FieldTypeId,
    contractorFieldId: 'd1012.main' as FieldTypeId,
    contractFieldId: 'd1015.main' as FieldTypeId,
    stateFieldId: 'd1105' as FieldTypeId,
    typeFieldId: 'd1106.t' as FieldTypeId,
    parentProjectFieldId: 'd1120.main' as FieldTypeId,
    reportedProjectItemsFond: 78,
    contractedProjectItemsFond: 76,
    contractedFields: {
        startDateFieldId: 'd1041.d' as FieldTypeId,
        endDateFieldId: 'd1043.d' as FieldTypeId,
        priceFieldId: 'd1150.c' as FieldTypeId,
        priceCurrencyFieldId: 'd1150.j' as FieldTypeId
    },
    statusFacet: 'SUTOR_STAV_ZAKAZKA',
    projectTypeChipStyles: {
        'INTERNAL': 'accent-blue-new',
        'EXTERNAL': 'brand-orange-new'
    }
};

export const SUTOR_EMPLOYEES_SETTINGS = {
    employeesFond: 7,
    positionFieldId: 'd1725' as FieldTypeId,
    employerFieldId: 'd1750' as FieldTypeId,
    firstNameFieldId: 'd1710.b' as FieldTypeId,
    surnameFieldId: 'd1710.a' as FieldTypeId,
    titleFieldId: 'd1710.c' as FieldTypeId,
    employeesAttendanceFond: 101
};

export const SUTOR_WORK_REPORT_FIELDS = {
    jobTypeFieldId: 'd2040' as FieldTypeId,
    workPlaceFieldId: 'd2094.a' as FieldTypeId
};

export const SUTOR_INSTALLATION_LOGBOOK_FIELDS = {
    dateFieldId: 'd1681.d' as FieldTypeId
};

export const SUTOR_REPORTS = {
    reportFonds: [20, 21, 22, 26, 37],

    // Shared fields
    projectFieldId: 'd1001.main' as FieldTypeId,
    projectItemFieldId: 'd2025.main' as FieldTypeId,
    dateFieldId: 'd2030.d' as FieldTypeId,
    stateFieldId: 'd2096.s' as FieldTypeId,
    textNoteFieldId: 'd2091.a' as FieldTypeId,
    referenceFieldId: 'd2012.a' as FieldTypeId,

    // For work, absence, operation, machine with operator
    workerFieldId: 'd2050.main' as FieldTypeId,

    // For machine, machine with operator
    machineFieldId: 'd2060.main' as FieldTypeId,

    // Subfonds
    MACHINE_WITH_OPERATOR: {
        fond: 20
    },
    WORK: {
        fond: 21,
        typeFieldId: 'd2035.t' as FieldTypeId,
        placeFieldId: 'd2094.a' as FieldTypeId,
        partnerFieldId: 'd2093.a' as FieldTypeId,
        reportedStandardHoursFieldId: 'd2070.m' as FieldTypeId,
        reportedOvertimeHoursFieldId: 'd2070.n' as FieldTypeId,
    },
    MACHINE: {
        fond: 22
    },
    ABSENCE: {
        fond: 26,
        typeFieldId: 'd2035.t' as FieldTypeId,
        reportedStandardHoursFieldId: 'd2070.m' as FieldTypeId
    },
    OPERATION: {
        fond: 37
    }
};