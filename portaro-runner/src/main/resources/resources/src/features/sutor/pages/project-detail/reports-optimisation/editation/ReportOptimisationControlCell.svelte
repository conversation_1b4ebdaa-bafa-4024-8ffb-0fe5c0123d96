<script lang="ts">
    import type {Row} from '@tanstack/svelte-table';
    import type {RecordRow} from 'src/features/record-grid/lib/types';
    import type {RecordGridDataManager} from 'src/features/record-grid/lib/record-grid-data-manager';
    import {getSutorReportsOptimisationContext} from 'src/features/sutor/pages/project-detail/reports-optimisation/sutor-reports-optimisation-context';
    import {FOND_INSTALLATION_LOGBOOK} from 'src/features/sutor/sutor-fonds';
    import ControlCell from 'src/features/record-grid/components/cells/ControlCell.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let recordGridDataManager: RecordGridDataManager;
    export let row: Row<RecordRow>;
    export let rowExpansionEnabled = false;

    const context = getSutorReportsOptimisationContext();

    const handleOptimiseClick = () => {
        context.setOptimisedInstallationLogbookRecord(row.original);
    };

    const handleOpenOverviewClick = () => {
        context.setOpenOverviewSignal(row.original);
    };
</script>

<ControlCell {recordGridDataManager} {row} {rowExpansionEnabled} expandButtonHidden>
    {#if row.original.fond.id === FOND_INSTALLATION_LOGBOOK.fond}
        <KpButton buttonSize="xs" buttonStyle="brand-orange-new" on:click={handleOptimiseClick}>
            <IconedContent icon="user-add">Optimalizovat</IconedContent>
        </KpButton>

        <KpButton buttonSize="xs" on:click={handleOpenOverviewClick}>
            <IconedContent icon="chart-histogram">Přehled</IconedContent>
        </KpButton>
    {/if}
</ControlCell>