<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import type {PaymentGatewayRedirectModalModel} from 'src/features/payment/types';
    import {getLocalization} from 'core/svelte-context/context';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpButtonStyleAnchor from 'shared/ui-widgets/button/KpButtonStyleAnchor.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    export let model: PaymentGatewayRedirectModalModel;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
</script>

<KpModalContent {modalWindowActions} additionalClasses="payment-confirmation-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            {localize(/* @kp-localization payment.ConfirmPayment */ 'payment.ConfirmPayment')}
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        <p class="header-title">
            <IconedContent align="center" justify="center" orientation="vertical" icon="check-circle">
                <strong>{localize(/* @kp-localization payment.PaymentConfirmationAmountLabel */ 'payment.PaymentConfirmationAmountLabel')}: {model.sumToPay}Kč </strong>
            </IconedContent>
        </p>

        <KpClassicTable hoverRows verticallyCentered additionalContainerClasses="gateway-payment-table-container">
            <svelte:fragment slot="body">
                {#each model.debts as debt}
                    <tr>
                        <td style="width: 50%">
                            {pipe(debt.type, loc())}
                        </td>

                        <td>
                            {debt.sum}Kč
                        </td>
                    </tr>
                {/each}
            </svelte:fragment>
        </KpClassicTable>

        <div class="pay-button-container">
            <KpButtonStyleAnchor href="{model.redirectUrl}" buttonStyle="brand-orange-new">
                <IconedContent icon="check-circle">
                    {localize(/* @kp-localization payment.Zaplatit */ 'payment.Zaplatit')} {model.sumToPay}Kč
                </IconedContent>
            </KpButtonStyleAnchor>

            <small class="text-muted text-center">
                {model.infoText}
            </small>
        </div>
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    @modal-body-padding: 15px;

    .header-title {
        font-size: @font-size-large;
        text-align: center;
        margin-top: @spacing-sm;
        margin-bottom: @spacing-s;
    }

    .pay-button-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
        justify-content: center;
        align-items: center;
    }

    :global {
        .payment-confirmation-modal {
            .header-title .uicon {
                color: var(--success-green);
            }

            .gateway-payment-table-container {
                margin-right: calc(@modal-body-padding * -1);
                margin-left: calc(@modal-body-padding * -1);
                width: calc(100% + @modal-body-padding * 2);
                border-bottom: 1px solid @themed-border-muted;

                tr > td {
                    &:first-child {
                        padding-left: @modal-body-padding !important;
                    }

                    &:last-child {
                        padding-right: @modal-body-padding !important;
                    }
                }
            }
        }
    }
</style>