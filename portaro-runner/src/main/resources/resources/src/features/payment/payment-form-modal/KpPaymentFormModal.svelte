<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLocalization, getSanitize} from 'core/svelte-context/context';
    import {KpPaymentFormPresenter} from '../kp-payment-form.presenter';
    import {onMount} from 'svelte';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import Checkbox from 'shared/ui-widgets/checkbox/Checkbox.svelte';
    import KpClassicTable from 'shared/ui-widgets/table/classic/KpClassicTable.svelte';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';
    import type {
        AmountType,
        ContraDebtablePaymentItemRequest,
        PayRequestFormPackage,
        ViewablePaymentRequestItem
    } from '../types';

    export let model: PayRequestFormPackage;
    export let modalWindowActions: ModalWindowActions;

    const localize = getLocalization();
    const sanitizeHtml = getSanitize();
    const injector = getInjector();
    const presenter = injector.getByToken<KpPaymentFormPresenter>(KpPaymentFormPresenter.presenterName);
    const paymentProviderNames = {
        'cash': localize(/* @kp-localization payment.CashLabel */ 'payment.CashLabel'),
        'terminal-virtual': localize(/* @kp-localization payment.TerminalVirtualLabel */ 'payment.TerminalVirtualLabel'),
        'gateway-gopay': localize(/* @kp-localization payment.GopayGatewayLabel */ 'payment.GopayGatewayLabel'),
        'gateway-gpwebpay': localize(/* @kp-localization payment.GpwebpayGatewayLabel */ 'payment.GpwebpayGatewayLabel'),
        'gateway-csob': localize(/* @kp-localization payment.CsobGatewayLabel */ 'payment.CsobGatewayLabel')
    }

    let amounts: ViewablePaymentRequestItem[] = [];
    let amountTypes: AmountType[];

    onMount(async () => {
        amounts = presenter.createAmounts(model.formObject);
        amountTypes = await presenter.getAmountTypes();
    });

    $: sumsEditable = model?.sumsEditable;
    $: typesEditable = model?.typesEditable;
    $: totalSum = amounts
        .filter((amount) => isSendable(amount))
        .reduce((acc, amount) => acc + amount.sum, 0);
    $: submittable = amounts.some((item) => isSendable(item));

    function isSendable(newAmount: ViewablePaymentRequestItem): boolean {
        return newAmount.enabled && newAmount.sum > 0 && newAmount.sum <= newAmount.maxSum;
    }

    function addDirectPayment() {
        const amount = {
            owner: model.formObject.itemsSpec.directPaymentOwner,
            sum: 0,
            type: {...amountTypes[0]},
        } as ContraDebtablePaymentItemRequest;

        amounts = [...amounts, presenter.createPaymentRequestItem(amount, true, true)];
    }

    function submit() {
        if (!submittable) {
            return;
        }

        const payRequest = {
            provider: model.formObject.provider,
            payer: model.formObject.payer,
            cashierDepartment: model.formObject.cashierDepartment,
            itemsSpec: model.formObject.itemsSpec,
            amounts: amounts.filter((item) => isSendable(item))
        };

        modalWindowActions.submitPromise(presenter.pay(payRequest));
    }

    function isContinuableProviderType(): boolean {
        return model.formObject.provider !== 'cash' && model.formObject.provider !== 'terminal-virtual';
    }

    function containsOnlyNotVisibleEditors(): boolean {
        const notVisibleEditors = model.formSetting.fields.filter((field) => field.editor.isVisible === false);
        return notVisibleEditors.length === model.formSetting.fields.length;
    }
</script>

<KpModalContent {modalWindowActions} additionalClasses="payment-selection-dialog">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            {localize(/* @kp-localization payment.Zaplatit */ 'payment.Zaplatit')}
        </KpModalTitle>
    </svelte:fragment>

    <form slot="body" on:submit|preventDefault={submit}>
        <p class="header-title">
            <strong>{@html pipe(localize(/* @kp-localization payment.VybertePoplatkyKZaplaceni */ 'payment.VybertePoplatkyKZaplaceni'), sanitizeHtml)}</strong>
        </p>

        <KpClassicTable hoverRows verticallyCentered additionalContainerClasses="payment-table-container">
            <svelte:fragment slot="body">
                {#each amounts as amount}
                    <tr class:text-muted={!isSendable(amount)}>
                        <td class="select-checkbox-container">
                            <Checkbox id={`${amount?.type?.name}-checkbox`} bind:checked={amount.enabled}/>
                        </td>

                        <td class="amount-types-container">
                            {#if amount.direct && typesEditable}
                                <span>
                                    <select class="form-control" bind:value={amount.type.id}>
                                        {#each amountTypes as type (type.id)}
                                            {@const label = type.text}
                                            <option {label} value={type.id}>{label}</option>
                                        {/each}
                                    </select>
                                </span>
                            {:else}
                                <span>
                                    {pipe(amount.type, loc())}
                                </span>
                            {/if}
                        </td>

                        <td>
                            {#if amount.direct || sumsEditable}
                                <span>
                                    <input class="form-control"
                                           type="number"
                                           min="0"
                                           max={amount.maxSum}
                                           bind:value={amount.sum}/>
                                </span>
                            {:else}
                                <span>
                                    {amount.sum}Kč
                                </span>
                            {/if}
                        </td>
                    </tr>
                {/each}
            </svelte:fragment>
        </KpClassicTable>

        {#if model.directPaymentAddable}
            <KpButton buttonSize="sm"
                      additionalClasses="add-payment-button"
                      on:click={() => addDirectPayment()}>

                <IconedContent icon="add">
                    {localize(/* @kp-localization payment.AddDirectPayment */ 'payment.AddDirectPayment')}
                </IconedContent>
            </KpButton>
        {/if}

        {#if !containsOnlyNotVisibleEditors()}
            <p class="universal-form-paragraph">
                <KpUniversalForm formSettings={model.formSetting}
                                 model={model.formObject}
                                 on:submit={() => submit()}/>
            </p>
        {/if}

        <div class="pay-button-container">
            <KpButton buttonType="submit"
                      buttonStyle="{isContinuableProviderType() ? 'accent-blue-new' : 'brand-orange-new'}"
                      isDisabled={!submittable}>

                {#if isContinuableProviderType()}
                    <IconedContent icon="arrow-right">
                        {localize(/* @kp-localization payment.Continue */ 'payment.Continue')}
                    </IconedContent>
                {:else}
                    <IconedContent icon="check-circle">
                        {localize(/* @kp-localization payment.Zaplatit */ 'payment.Zaplatit')} {totalSum}Kč
                    </IconedContent>
                {/if}
            </KpButton>

            <small class="text-muted text-center">
                {paymentProviderNames[model.formObject.provider]}
            </small>
        </div>
    </form>

    <svelte:fragment slot="footer">
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

<style lang="less">
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro.variables.less";

    @modal-body-padding: 15px;

    .header-title {
        font-size: @font-size-large;
        text-align: center;
        margin-top: @spacing-sm;
        margin-bottom: @spacing-xl;
    }

    .select-checkbox-container {
        text-align: center;
    }

    .amount-types-container {
        width: 50%;
    }

    .universal-form-paragraph {
        margin-top: @spacing-xl;
    }

    .pay-button-container {
        display: flex;
        flex-direction: column;
        gap: @spacing-m;
        justify-content: center;
        align-items: center;
        margin-bottom: @spacing-sm;
    }

    :global {
        .payment-selection-dialog {
            .add-payment-button {
                border-top: none;
                border-top-left-radius: 0;
            }

            .payment-table-container {
                margin-right: calc(@modal-body-padding * -1);
                margin-left: calc(@modal-body-padding * -1);
                width: calc(100% + @modal-body-padding * 2);
                border-bottom: 1px solid @themed-border-muted;

                tr > td {
                    &:first-child {
                        padding-left: @modal-body-padding !important;
                    }

                    &:last-child {
                        padding-right: @modal-body-padding !important;
                    }
                }
            }

            .universal-form-paragraph .form-group {
                margin-bottom: 0;
            }
        }
    }
</style>
