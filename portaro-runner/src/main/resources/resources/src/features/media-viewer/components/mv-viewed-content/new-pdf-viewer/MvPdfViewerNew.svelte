<script lang="ts">
    import type {ViewableFile} from 'typings/portaro.be.types';
    import {createEventDispatcher, onMount} from 'svelte';
    import {loadPdfJsModule} from 'src/features/media-viewer/components/mv-viewed-content/new-pdf-viewer/pdfjs';

    export let file: ViewableFile;

    const dispatch = createEventDispatcher<{
        'load-start': void,
        'load-success': void,
        'load-error': void
    }>();

    let canvas: HTMLCanvasElement;
    let pdfDoc: any = null;
    let pageNum = 1;
    let pageRendering = false;
    let pageNumPending: number | null = null;
    let scale = 1.5;

    const renderPage = (num: number) => {
        pageRendering = true;
        pdfDoc.getPage(num).then((page: any) => {
            const viewport = page.getViewport({scale});

            const context = canvas.getContext('2d');
            canvas.height = viewport.height;
            canvas.width = viewport.width;

            const renderContext = {
                canvasContext: context!,
                viewport
            };
            const renderTask = page.render(renderContext);

            renderTask.promise.then(() => {
                pageRendering = false;
                if (pageNumPending !== null) {
                    renderPage(pageNumPending);
                    pageNumPending = null;
                }
            });
        });
    };

    const queueRenderPage = (num: number) => {
        if (pageRendering) {
            pageNumPending = num;
        } else {
            renderPage(num);
        }
    };

    const onPrevPage = () => {
        if (pageNum <= 1) return;
        pageNum--;
        queueRenderPage(pageNum);
    };

    const onNextPage = () => {
        if (pageNum >= pdfDoc.numPages) return;
        pageNum++;
        queueRenderPage(pageNum);
    };

    const zoomIn = () => {
        scale += 0.25;
        queueRenderPage(pageNum);
    };

    const zoomOut = () => {
        if (scale <= 0.5) return;
        scale -= 0.25;
        queueRenderPage(pageNum);
    };

    onMount(async () => {
        const pdfJsModule = await loadPdfJsModule();

        pdfJsModule.getDocument(`/files/${file.id}`).promise.then((pdfDoc_: any) => {
            pdfDoc = pdfDoc_;
            renderPage(pageNum);
        });

        dispatch('load-success');
    });
</script>

<div class="mv-pdf-viewer">
    <div class="controls">
        <button on:click={onPrevPage}>Prev</button>
        <button on:click={onNextPage}>Next</button>
        <button on:click={zoomOut}>-</button>
        <button on:click={zoomIn}>+</button>
        <span>Page {pageNum}</span>
    </div>

    <canvas bind:this={canvas} class="pdf-canvas"></canvas>
</div>

<style lang="less">
    @import (reference) "src/features/media-viewer/media-viewer.variables.less";

    .mv-pdf-viewer {
        position: absolute;
        display: flex;
        flex-direction: column;
        overflow-y: scroll;
        padding: @spacing-ml;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: var(--viewer-content-bg);
    }
</style>