<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
    import type {Field7Model} from 'src/features/record-editation/field7-and-field8-editors/utils/types';
    import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import {exists} from 'shared/utils/custom-utils';
    import {createPayloadActionResponse} from 'src/modals/modal-utils';
    import {Field7EditorServiceFactory} from './field7-editor-service.factory';
    import {onMount} from 'svelte';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import {getCodesByPosition, getLabelsAndCodesByDocumentCategoryCode} from 'src/features/record-editation/field7-and-field8-editors/utils/utils';
    import type {Field007Code, Field007Label} from 'typings/portaro.be.types';
    import {UnknownDocumentCategoryCodeError} from 'src/features/record-editation/field7-and-field8-editors/field7-editor-modal/field7-errors';
    import {ascendingOrderComparator} from 'shared/utils/array-utils';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {Field} from 'node_modules/svelte-forms/types';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import type {Observable} from 'rxjs';
    import type {
        Field7EditorModalModel
    } from 'src/features/record-editation/field7-and-field8-editors/field7-editor-modal/types';

    export let model: Field7EditorModalModel;
    export let modalWindowActions: ModalWindowActions;

    const logger = getLogger();
    const modalDialogService = getInjector().getByClass(ModalDialogService);
    const editorService = getInjector().getByClass(Field7EditorServiceFactory).createService(model.definitions);

    let formModel: Field7Model;
    let formSettings: KpUniversalFormSettings<Field7Model>; // form fields definition
    let lastDocumentCategoryCode: string;

    let formRef: {getFormController: () => FormGroup<Field7Model>};
    $: if (exists(formRef)) {
        formState$ = formRef.getFormController().getFieldState$();
    }
    let formState$: Observable<Field<any>>;
    $: isFormValid = $formState$?.valid ?? false;

    let error: Error | null = null;
    $: hasError = exists(error);

    onMount(async () => {

        const allowedDocumentCategories = model.definitions.documentCategories
                                              .filter((documentType) => model.fond.documentCategories.includes(documentType.code))
                                              .sort(ascendingOrderComparator);

        if (allowedDocumentCategories.length === 0) {
            error = new Error('Žádné povolené kategorie dokumentu - změňte nastavení fondu');
            return; // show error and stop form initialization
        }

        formSettings = editorService.initializeFormsFirstSegment(allowedDocumentCategories); // initialize form

        try {
            formModel = editorService.parseCode(model.value);
        } catch (err: unknown) {
            logger.error(error);
            if (err instanceof UnknownDocumentCategoryCodeError) {
                const errorMessage = `pro kód kategorie dokumentu [${err.unknownCategoryCode}] nebyla nalezena definovaná hodnota`;
                await modalDialogService.openModalWindow('confirmationDialog', {text: `Pole 007 má chybnou strukturu (${errorMessage}). Chcete nastavit výchozí hodnoty?`});
            } else {
                throw new Error('Unknown field7 parsing error');
            }
        }

        // the current value could be invalid because the document category is not included in valid allowedDocumentCategories defined by the fond
        // or documentCategory could be undefined because value could not be parsed
        if (!allowedDocumentCategories.includes(formModel?.documentCategory)) { // empty or invalid field7 value
            formModel = {documentCategory: allowedDocumentCategories.at(0)}; // set first possible allowedDocumentCategories as default value
            updateFormAndResetModel(formModel.documentCategory.code);
        } else { // editing existing field7 value
            updateForm(formModel.documentCategory.code);
        }

        lastDocumentCategoryCode = formModel.documentCategory.code
    })

    function onChange() {
        if (formModel.documentCategory.code !== lastDocumentCategoryCode) { // changed document category
            updateFormAndResetModel(formModel.documentCategory.code); // reset form based on the new document category
            lastDocumentCategoryCode = formModel.documentCategory.code; // save the last document category
        }
    }

    function submit() {
        if (!isFormValid || hasError) {
            return;
        }

        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(editorService.serializeModel(formModel))));
    }

    function updateForm(code: string) {
        const [labels, codes] = getLabelsAndCodesByDocumentCategoryCode(model.definitions, code);
        formSettings = editorService.initializeFormsSecondSegment(formSettings, labels, codes); // change form according to document category code
    }

    function updateFormAndResetModel(code: string) {
        const [labels, codes] = getLabelsAndCodesByDocumentCategoryCode(model.definitions, code);
        formSettings = editorService.initializeFormsSecondSegment(formSettings, labels, codes); // change form according to document category code
        resetModel(labels, codes); // reset model - sets default values (all fields except document category)
    }

    function resetModel(labels: Field007Label[], codes: Field007Code[]) {
        formModel = {documentCategory: formModel.documentCategory};
        labels.forEach((label) => editorService.setPositionsDefaultValueToModel(formModel, label.order, getCodesByPosition(codes, label.order)))
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="field7-editor-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            Kódy fyzického popisu
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(formSettings)}
            <KpUniversalForm formId="{formSettings.id}"
                             {formSettings}
                             bind:model={formModel}
                             on:model-change={onChange}
                             bind:this={formRef}/>
        {/if}

        {#if hasError}
            <div class="alert alert-danger text-center">
                <span>{error.message}</span>
            </div>
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary" isDisabled="{!isFormValid || hasError}" on:click={submit}>
            OK
        </KpButton>
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

