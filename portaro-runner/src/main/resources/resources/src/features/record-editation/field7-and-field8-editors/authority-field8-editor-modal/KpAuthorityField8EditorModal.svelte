<script lang="ts">
    import type {ModalWindowActions} from 'shared/modal-dialogs/types';
    import {getInjector, getLogger} from 'core/svelte-context/context';
    import KpModalContent from 'src/modals/kp-modal/KpModalContent.svelte';
    import KpButton from 'shared/ui-widgets/button/KpButton.svelte';
    import KpModalFooterCloseButton from 'src/modals/kp-modal/KpModalFooterCloseButton.svelte';
    import KpModalHeaderCloseButton from 'src/modals/kp-modal/KpModalHeaderCloseButton.svelte';
    import {ModalDialogService} from 'shared/modal-dialogs/modal-dialog.service';
    import type {AuthorityField8Model} from '../utils/types';
    import type {KpUniversalFormSettings} from 'shared/value-editors/kp-universal-form/types';
    import {exists} from 'shared/utils/custom-utils';
    import {createPayloadActionResponse} from 'src/modals/modal-utils';
    import type {FormGroup} from 'shared/value-editors/internal/forms/form-group';
    import type {Field} from 'node_modules/svelte-forms/types';
    import KpUniversalForm from 'shared/value-editors/kp-universal-form/KpUniversalForm.svelte';
    import {onMount} from 'svelte';
    import {IncorrectSectionFormatError, UnknownSectionsCodesError} from './authority-field8-errors';
    import {AuthorityField8EditorServiceFactory} from './authority-field8-editor-service.factory';
    import KpModalTitle from 'src/modals/kp-modal/KpModalTitle.svelte';
    import type {Observable} from 'rxjs';
    import type {
        AuthorityField8EditorModalModel
    } from 'src/features/record-editation/field7-and-field8-editors/authority-field8-editor-modal/types';

    export let model: AuthorityField8EditorModalModel;
    export let modalWindowActions: ModalWindowActions;

    const logger = getLogger();
    const modalDialogService = getInjector().getByClass(ModalDialogService);
    const editorService = getInjector().getByClass(AuthorityField8EditorServiceFactory).createService(model.definitions);

    let formModel: AuthorityField8Model;
    let formSettings: KpUniversalFormSettings<AuthorityField8Model>; // form fields definition

    let formRef: {getFormController: () => FormGroup<AuthorityField8Model>};
    $: if (exists(formRef)) {
        formState$ = formRef.getFormController().getFieldState$();
    }
    let formState$: Observable<Field<any>>;
    $: isFormValid = $formState$?.valid ?? false;

    onMount(async () => {
        formSettings = editorService.initializeForm();

        try {
            formModel = editorService.parseCode(model.value); // parse code for every document type
        } catch (err: unknown) {
            await handleParsingErrors(err);
        }

        if (!formModel) { // empty or invalid field8 value (because model was not parsed)
            formModel = editorService.createDefaultModel();
        }
    });

    function submit() {
        if (!isFormValid) {
            return;
        }

        modalWindowActions.submitPromise(Promise.resolve(createPayloadActionResponse(editorService.serializeModel(formModel))));
    }

    async function handleParsingErrors(err: unknown) {
        logger.error(err);
        if (err instanceof IncorrectSectionFormatError) {
            const errorMessage = `hodnota [${err.sectionValue}] sekce [${err.section}] má chybný formát`;
            await askUserToUseDefaults(errorMessage);
        } else if (err instanceof UnknownSectionsCodesError) {
            const errorMessage = `pro kódy ze sekcí [${err.sectionsWithUnknownCodes.join(', ')}] nebyla nalezena definovaná hodnota`;
            await askUserToUseDefaults(errorMessage);
        } else {
            throw new Error('Unknown field8 parsing error');
        }
    }

    async function askUserToUseDefaults(errorMessage: string) {
        await modalDialogService.openModalWindow('confirmationDialog', {text: `Pole 008 má chybnou strukturu (${errorMessage}). Chcete nastavit výchozí hodnoty?`})
        formModel = null;
    }
</script>


<KpModalContent {modalWindowActions} additionalClasses="authority-field8-editor-modal">
    <svelte:fragment slot="header">
        <KpModalHeaderCloseButton/>

        <KpModalTitle>
            Údaje pevné délky (autoritní)
        </KpModalTitle>
    </svelte:fragment>

    <svelte:fragment slot="body">
        {#if exists(formSettings)}
            <KpUniversalForm formId="{formSettings.id}"
                             {formSettings}
                             bind:model={formModel}
                             bind:this={formRef}/>
        {/if}
    </svelte:fragment>

    <svelte:fragment slot="footer">
        <KpButton buttonStyle="primary" isDisabled="{!isFormValid}" on:click={submit}>
            OK
        </KpButton>
        <KpModalFooterCloseButton/>
    </svelte:fragment>
</KpModalContent>

