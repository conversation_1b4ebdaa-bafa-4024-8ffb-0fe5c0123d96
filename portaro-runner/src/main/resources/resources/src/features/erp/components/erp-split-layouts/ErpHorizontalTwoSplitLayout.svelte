<script lang="ts">
    import type {DraggableResizerEventData} from 'src/features/erp/components/erp-split-layouts/erp-draggable-resizer/types';
    import type {CssSize} from 'shared/ui-widgets/types';
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import {exists} from 'shared/utils/custom-utils';
    import {onDestroy, tick} from 'svelte';
    import {get, writable} from 'svelte/store';
    import {persistentStorageLocal} from 'shared/persistent-storage/persistent-storage';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpDraggableResizer from 'src/features/erp/components/erp-split-layouts/erp-draggable-resizer/ErpDraggableResizer.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';

    export let pageClass: string;
    export let ratios: [number, number] = [1, 1];
    export let gap: CssSize = '0px';
    export let withContentPadding = false;
    export let withoutLogo = false;
    export let resizeDisabled = false;
    export let headingIcon: UIcons | null = null;

    let windowWidth = window.innerWidth;
    let oldWindowWidth = window.innerWidth;

    let rightPanelElement: HTMLElement;
    const rightPanelWidthRatio$ = writable(ratios[1] / (ratios[0] + ratios[1]));
    const rightPanelWidthPersistentUnsubscribe = resizeDisabled ? undefined : persistentStorageLocal(rightPanelWidthRatio$, `${pageClass}-right-panel-width-ratio`);
    let beforeResizingRightPanelWidth: number | null = null;

    $: updateRightPanelWidth(windowWidth);

    onDestroy(() => {
        if (exists(rightPanelWidthPersistentUnsubscribe)) {
            rightPanelWidthPersistentUnsubscribe();
        }
    });

    const handleResize = (event: CustomEvent<DraggableResizerEventData>) => {
        if (!exists(beforeResizingRightPanelWidth) && event.detail.resizing) {
            beforeResizingRightPanelWidth = get(rightPanelWidthRatio$) * windowWidth;
        }

        if (!event.detail.resizing) {
            beforeResizingRightPanelWidth = null;

            if (exists(rightPanelElement)) {
                tick().then(() => $rightPanelWidthRatio$ = rightPanelElement.getBoundingClientRect().width / windowWidth);
            }
        }

        if (!exists(beforeResizingRightPanelWidth) && !event.detail.resizing) {
            return;
        }

        $rightPanelWidthRatio$ = (beforeResizingRightPanelWidth - event.detail.delta) / windowWidth;
    };

    const handleReset = () => {
        $rightPanelWidthRatio$ = ratios[1] / (ratios[0] + ratios[1]);
    };

    function updateRightPanelWidth(innerWidth: number) {
        const oldRightPanelWidthRadio = (get(rightPanelWidthRatio$) * innerWidth) / oldWindowWidth;
        $rightPanelWidthRatio$ = (oldRightPanelWidthRadio * innerWidth) / innerWidth;
        oldWindowWidth = innerWidth;
    }
</script>

<svelte:window bind:innerWidth={windowWidth}/>

<ErpPageLayout {pageClass} {gap} {withoutLogo} withoutContentPadding="{!withContentPadding}">
    {#if exists($$slots.heading)}
        <ErpHeadingBar slot="heading" icon="{headingIcon}">
            <slot name="heading"/>
        </ErpHeadingBar>
    {/if}

    {#if exists($$slots.topbar)}
        <slot name="topbar"/>
    {/if}

    <div class="page-content-container" class:resize-disabled={resizeDisabled}>
        <div class="left-panel-container" style:--resize-disabled-ratio="{ratios[0]}">
            <div class="left-panel-inner">
                <slot name="left-panel"/>
            </div>
        </div>

        <ErpDraggableResizer disabled="{resizeDisabled}"
                             orientation="vertical"
                             on:resize={handleResize}
                             on:reset={handleReset}/>

        <div class="right-panel-container"
             style:--right-panel-width="{$rightPanelWidthRatio$ * windowWidth}px"
             style:--resize-disabled-ratio="{ratios[1]}"
             bind:this={rightPanelElement}>

            <div class="right-panel-inner">
                <slot name="right-panel"/>
            </div>
        </div>
    </div>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .page-content-container {
        display: flex;
        width: 100%;
        flex: 1 1 0;
        overflow: hidden;

        &.resize-disabled {
            .left-panel-container {
                flex: var(--resize-disabled-ratio);
            }

            .right-panel-container {
                flex: var(--resize-disabled-ratio);
            }
        }

        &:not(.resize-disabled) .right-panel-container {
            width: var(--right-panel-width);
        }

        .left-panel-container {
            .flex-grow();
            overflow-y: scroll;

            .left-panel-inner {
                .flex-grow();
                padding: @spacing-l @spacing-xl @spacing-xl @spacing-xxl;
            }
        }

        .right-panel-container {
            overflow-y: scroll;
            display: flex;
            flex-direction: column;

            .right-panel-inner {
                .flex-grow();
                padding: @spacing-l @spacing-xxl @spacing-xl @spacing-xl;
            }
        }
    }
</style>