<script lang="ts">
    import type {DraggableResizerEventData} from 'src/features/erp/components/erp-split-layouts/erp-draggable-resizer/types';
    import type {CssSize} from 'shared/ui-widgets/types';
    import {exists} from 'shared/utils/custom-utils';
    import {onDestroy, tick} from 'svelte';
    import {get, writable} from 'svelte/store';
    import {persistentStorageLocal} from 'shared/persistent-storage/persistent-storage';
    import ErpPageLayout from 'src/features/erp/components/ErpPageLayout.svelte';
    import ErpDraggableResizer from 'src/features/erp/components/erp-split-layouts/erp-draggable-resizer/ErpDraggableResizer.svelte';
    import ErpHeadingBar from 'src/features/erp/components/ErpHeadingBar.svelte';

    export let pageClass: string;
    export let ratios: [number, number] = [1, 1];
    export let gap: CssSize = '0px';
    export let withContentPadding = false;
    export let withoutPanelPadding = false;
    export let resizeDisabled = false;

    let windowHeight = window.innerHeight;
    let oldWindowHeight = window.innerHeight;

    let bottomPanelElement: HTMLElement;
    const bottomPanelHeightRatio$ = writable(ratios[1] / (ratios[0] + ratios[1]));
    const bottomPanelHeightPersistentUnsubscribe = resizeDisabled ? undefined : persistentStorageLocal(bottomPanelHeightRatio$, `${pageClass}-bottom-panel-height-ratio`);
    let beforeResizingBottomPanelHeight: number | null = null;

    $: updateBottomPanelHeight(windowHeight);

    onDestroy(() => {
        if (exists(bottomPanelHeightPersistentUnsubscribe)) {
            bottomPanelHeightPersistentUnsubscribe();
        }
    });

    const handleResize = (event: CustomEvent<DraggableResizerEventData>) => {
        if (!exists(beforeResizingBottomPanelHeight) && event.detail.resizing) {
            beforeResizingBottomPanelHeight = get(bottomPanelHeightRatio$) * windowHeight;
        }

        if (!event.detail.resizing) {
            beforeResizingBottomPanelHeight = null;

            if (exists(bottomPanelElement)) {
                tick().then(() => $bottomPanelHeightRatio$ = bottomPanelElement.getBoundingClientRect().height / windowHeight);
            }
        }

        if (!exists(beforeResizingBottomPanelHeight) && !event.detail.resizing) {
            return;
        }

        $bottomPanelHeightRatio$ = (beforeResizingBottomPanelHeight - event.detail.delta) / windowHeight;
    };

    const handleReset = () => {
        $bottomPanelHeightRatio$ = ratios[1] / (ratios[0] + ratios[1]);
    };

    function updateBottomPanelHeight(innerHeight: number) {
        const oldBottomPanelHeightRatio = (get(bottomPanelHeightRatio$) * innerHeight) / oldWindowHeight;
        $bottomPanelHeightRatio$ = (oldBottomPanelHeightRatio * innerHeight) / innerHeight;
        oldWindowHeight = innerHeight;
    }
</script>

<svelte:window bind:innerHeight={windowHeight}/>

<ErpPageLayout {pageClass} {gap} withoutContentPadding="{!withContentPadding}">
    {#if exists($$slots.heading)}
        <ErpHeadingBar slot="heading">
            <slot name="heading"/>
        </ErpHeadingBar>
    {/if}

    {#if exists($$slots.topbar)}
        <slot name="topbar"/>
    {/if}

    <div class="page-content-container" class:resize-disabled={resizeDisabled}>
        <div class="top-panel-container">
            <div class="top-panel-inner"
                 class:without-panel-padding={withoutPanelPadding}
                 style:--resize-disabled-ratio="{ratios[0]}">

                <slot name="top-panel"/>
            </div>
        </div>

        <ErpDraggableResizer disabled={resizeDisabled}
                             orientation="horizontal"
                             on:resize={handleResize}
                             on:reset={handleReset}/>

        <div class="bottom-panel-container"
             style:--bottom-panel-height="{$bottomPanelHeightRatio$ * windowHeight}px"
             style:--resize-disabled-ratio="{ratios[1]}"
             bind:this={bottomPanelElement}>

            <div class="bottom-panel-inner" class:without-panel-padding={withoutPanelPadding}>
                <slot name="bottom-panel"/>
            </div>
        </div>
    </div>
</ErpPageLayout>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .page-content-container {
        .flex-grow();
        overflow: hidden;

        &.resize-disabled {
            .top-panel-container {
                flex: var(--resize-disabled-ratio);
            }

            .bottom-panel-container {
                height: var(--resize-disabled-ratio);
            }
        }

        &:not(.resize-disabled) .bottom-panel-container {
            height: var(--bottom-panel-height);
        }

        .top-panel-container {
            .flex-grow();
            overflow-y: auto;

            .top-panel-inner {
                .flex-grow();
                padding: @spacing-l @spacing-xl @spacing-xl @spacing-xxl;

                &.without-panel-padding {
                    padding: 0;
                }
            }
        }

        .bottom-panel-container {
            overflow-y: auto;
            display: flex;
            flex-direction: column;

            .bottom-panel-inner {
                .flex-grow();
                padding: @spacing-l @spacing-xxl @spacing-xl @spacing-xl;

                &.without-panel-padding {
                    padding: 0;
                }
            }
        }
    }
</style>