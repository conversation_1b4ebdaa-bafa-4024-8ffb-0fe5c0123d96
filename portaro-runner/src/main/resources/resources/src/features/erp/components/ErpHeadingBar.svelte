<script lang="ts">
    import type {CssSize} from 'shared/ui-widgets/types';
    import type {UIcons} from 'shared/ui-widgets/uicons/types';
    import {exists} from 'shared/utils/custom-utils';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import KpHeading from 'shared/components/kp-heading/KpHeading.svelte';

    export let gap: CssSize = '12px';
    export let additionalClasses = '';
    export let backgroundImageUrl: string | null = null;
    export let title: string | null = null;
    export let icon: UIcons | null = null;
</script>

<div class="erp-heading-bar {additionalClasses}"
     class:with-background-image="{exists(backgroundImageUrl)}"
     style:--background-image-url={`url("${backgroundImageUrl}")`}
     style:--gap="{gap}">

    <div class="heading-container">
        {#if exists(icon)}
            <span class="icon-container">
                <UIcon lineHeight="1" {icon}/>
            </span>
        {/if}

        {#if exists(title)}
            <KpHeading type="h1">{title}</KpHeading>
        {:else}
            <slot/>
        {/if}
    </div>

    {#if exists($$slots['additional-content'])}
        <div class="additional-content-container">
            <slot name="additional-content"/>
        </div>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro-erp.less";

    .erp-heading-bar {
        width: 100%;
        display: flex;
        position: relative;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid @themed-border-default;
        padding: @spacing-xl @main-padding-horizontal;
        background-image: var(--background-image-url);
        background-size: cover;
        gap: @spacing-ml;

        .heading-container {
            display: flex;
            align-items: center;
            gap: var(--gap);

            .icon-container {
                font-size: 18px;
                display: flex;
                align-items: center;
            }
        }

        .additional-content-container {
            display: flex;
            align-items: center;
            gap: var(--gap);
        }
    }

    :global {
        .erp-heading-bar .heading-container .kp-heading.heading-h1 {
            font-size: @font-size-xl;
        }
    }
</style>