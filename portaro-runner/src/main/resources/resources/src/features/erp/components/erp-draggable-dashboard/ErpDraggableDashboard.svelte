<script lang="ts">
    import type {DraggableDashboardCard} from 'src/features/erp/components/erp-draggable-dashboard/types';
    import type {DndEvent} from 'svelte-dnd-action';
    import {dragHandleZone, dragHandle} from 'svelte-dnd-action';
    import {flip} from 'svelte/animate';
    import ErpCard from 'src/features/erp/components/erp-card/ErpCard.svelte';
    import EmptyCardContent from 'src/features/sutor/pages/homepage/parts/cards/EmptyCardContent.svelte';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';

    export let cards: DraggableDashboardCard[];

    const flipDurationMs = 250;

    const handleDndConsider = (event: CustomEvent<DndEvent<DraggableDashboardCard>>) => {
        cards = event.detail.items;
    };

    const handleDndFinalize = (event: CustomEvent<DndEvent<DraggableDashboardCard>>) => {
        cards = event.detail.items;
    };
</script>

<div class="erp-draggable-dashboard-cards"
     use:dragHandleZone={{ items: cards, dropTargetStyle: {}, flipDurationMs }}
     on:consider={handleDndConsider}
     on:finalize={handleDndFinalize}>

    {#each cards as card (card.id)}
        <div class="dashboard-draggable-card-wrapper"
             style:grid-column="span {card.colSpan}"
             animate:flip={{duration: flipDurationMs}}>

            <ErpCard headerFooterDivided>
                <div slot="header" class="card-header-container">
                    <span class="card-title">{card.title}</span>
                    <div use:dragHandle aria-label="drag-handle for {card.title}" class="handle">
                        <UIcon icon="menu-burger"/>
                    </div>
                </div>

                <svelte:fragment slot="body">
                    <svelte:component this="{card.cardContentComponent ?? EmptyCardContent}" {...card.componentProps ?? {}}/>
                </svelte:fragment>
            </ErpCard>
        </div>
    {/each}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";

    .erp-draggable-dashboard-cards {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: @spacing-xxl;
        width: 100%;
    }

    :global {
        .dashboard-draggable-card-wrapper {
            .card-header-container {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .card-title {
                    font-weight: 500;
                }
            }
        }
    }
</style>