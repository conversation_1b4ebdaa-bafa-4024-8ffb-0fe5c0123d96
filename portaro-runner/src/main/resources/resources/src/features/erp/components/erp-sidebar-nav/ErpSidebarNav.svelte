<script lang="ts">
    import type {Auth} from 'typings/portaro.be.types';
    import {onDestroy, onMount} from 'svelte';
    import {exists, isFunction} from 'shared/utils/custom-utils';
    import {getDateFormatter, getInjector, getLocalization} from 'core/svelte-context/context';
    import {ErpSidebarNavService} from './erp-sidebar-nav.service';
    import {unsubscribeAllSubscriptions} from 'shared/utils/observables-utils';
    import {persistentStorageLocal} from 'shared/persistent-storage/persistent-storage';
    import {createErpSidebarNavContext} from './erp-sidebar-nav-context';
    import {get, writable} from 'svelte/store';
    import {fly} from 'svelte/transition';
    import {pipe} from 'core/utils';
    import {loc} from 'shared/utils/pipes';
    import {ERP_SIDEBAR_SETTINGS_DEFAULTS} from './constants';
    import sutorLogoSrc from '../../../sutor/assets/sutor-logo-no-text.svg?assets';
    import UIcon from 'shared/ui-widgets/uicons/UIcon.svelte';
    import SidebarResizer from './components/SidebarResizer.svelte';
    import SidebarNavItem from './components/SidebarNavItem.svelte';
    import KpDropdownWrapper from 'shared/ui-widgets/dropdown/KpDropdownWrapper.svelte';
    import KpLocaleSelect from 'shared/components/kp-locale/KpLocaleSelect.svelte';
    import IconedContent from 'shared/ui-widgets/uicons/IconedContent.svelte';

    const service = getInjector().getByToken<ErpSidebarNavService>(ErpSidebarNavService.serviceName);
    const context = createErpSidebarNavContext('/', service.getMenuItems());
    const localize = getLocalization();
    const dateFormatter = getDateFormatter();

    const menuCollapsed$ = writable(ERP_SIDEBAR_SETTINGS_DEFAULTS.collapsed);
    const sidebarOpenedWidth$ = writable(ERP_SIDEBAR_SETTINGS_DEFAULTS.openedWidth);
    const menuCollapsedPersistentUnsubscribe = persistentStorageLocal(menuCollapsed$, 'erp-sidebar-menu-collapsed');
    const sidebarOpenedWidthPersistentUnsubscribe = persistentStorageLocal(sidebarOpenedWidth$, 'erp-sidebar-width');

    let sidebarResizing = false;
    $: isMenuOpened = !$menuCollapsed$;
    $: isMenuCollapsed = $menuCollapsed$;

    let currentAuth: Auth;
    let menuItems = get(context.menuItems);
    const menuItemsUnsubscribe = context.menuItems.subscribe((currentItems) => {
        // Filter out login and user profile menu item
        menuItems = currentItems.filter((item) => item.type !== 'user');
    });
    const currentAuthSubscription = service.getCurrentAuth$().subscribe((auth) => {
        currentAuth = auth;
        service.loadCurrentMenuItems().then((loadedMenuItems) => context.updateMenuItems(loadedMenuItems));
    });

    let currentRouteUnsubscribeFunc: () => void;
    let toggleCollapseUnsubscribeFunc: () => void;

    onMount(() => {
        currentRouteUnsubscribeFunc = service.registerCurrentRouteListener((routeUrl) => {
            context.setCurrentRoute(routeUrl);
        });

        toggleCollapseUnsubscribeFunc = service.registerToggleCollapseListener((collapsed) => {
            $menuCollapsed$ = collapsed;
            updateMainSidebarClass(collapsed);
        });

        updateMainPadding(get(sidebarOpenedWidth$));
        updateMainSidebarClass(get(menuCollapsed$));
    });

    onDestroy(() => {
        if (isFunction(currentRouteUnsubscribeFunc)) {
            currentRouteUnsubscribeFunc();
        }

        if (isFunction(toggleCollapseUnsubscribeFunc)) {
            toggleCollapseUnsubscribeFunc();
        }

        menuItemsUnsubscribe();
        menuCollapsedPersistentUnsubscribe();
        sidebarOpenedWidthPersistentUnsubscribe();

        unsubscribeAllSubscriptions(currentAuthSubscription);
    });

    const handleCollapserClick = () => {
        const contentElement = document.querySelector('.portaro-erp-content');

        if (exists(contentElement)) {
            contentElement.classList.add('animate-padding-left');
            setTimeout(() => contentElement.classList.remove('animate-padding-left'), 500);
        }

        $menuCollapsed$ = !$menuCollapsed$;
        updateMainSidebarClass(get(menuCollapsed$));
    };

    const handleNewWidth = (event: CustomEvent<number>) => {
        $sidebarOpenedWidth$ = event.detail;
        updateMainPadding(get(sidebarOpenedWidth$));
    };

    function updateMainSidebarClass(newMenuCollapsed: boolean) {
        const contentElement = document.querySelector('.portaro-erp-content');

        if (exists(contentElement)) {
            contentElement.classList.add(newMenuCollapsed ? 'sidebar-collapsed' : 'sidebar-opened');
            contentElement.classList.remove(newMenuCollapsed ? 'sidebar-opened' : 'sidebar-collapsed');
        }
    }

    function updateMainPadding(newSidebarWidth: number) {
        const contentElement = document.querySelector('.portaro-erp-content');

        if (exists(contentElement)) {
            (contentElement as HTMLElement).style.setProperty('--sidebar-opened-width', `${newSidebarWidth}px`);
        }
    }
</script>

<div class="erp-sidebar-nav"
     class:menu-opened={isMenuOpened}
     class:menu-collapsed={isMenuCollapsed}
     class:sidebar-resizing={sidebarResizing}
     style:--sidebar-opened-width="{$sidebarOpenedWidth$}px">

    <button class="sidebar-collapser" on:click={handleCollapserClick}>
        <div class="icon-container" class:collapsed={isMenuCollapsed}>
            <UIcon icon="angle-small-left"/>
        </div>
    </button>

    <div class="logo-container">
        <a href="/" class="no-decoration-anchor">
            <img class="erp-logo"
                 alt="Sutin logo"
                 src="{sutorLogoSrc}"/>
        </a>
    </div>

    <nav class="navigation-container">
        {#key currentAuth}
            <ul class="navigation-items" in:fly={{y: 10, duration: 250}}>
                {#each menuItems as menuItem(menuItem.id)}
                    <SidebarNavItem menuCollapsed="{isMenuCollapsed}" {menuItem}>
                        {menuItem.text}
                    </SidebarNavItem>
                {/each}
            </ul>
        {/key}
    </nav>

    {#if isMenuOpened}
        {#if service.switchableLanguages.length > 0}
            <KpDropdownWrapper additionalContainerClasses="erp-sidebar-language-dropdown">
                <IconedContent slot="button" icon="globe">Volba jazyka</IconedContent>

                <svelte:fragment slot="menu">
                    {#each service.switchableLanguages as lang}
                        <li class="{lang.id}">
                            <KpLocaleSelect locale="{lang.id}">{pipe(lang, loc())}</KpLocaleSelect>
                        </li>
                    {/each}
                </svelte:fragment>
            </KpDropdownWrapper>
        {/if}

        <small class="system-version">
            {localize(/* @kp-localization commons.VerzeSystemu */ 'commons.VerzeSystemu')}
            {pipe(service.portaroVersion.date, dateFormatter('d.M.yyyy H:mm:ss'))}
            ({service.portaroVersion.branch} {service.portaroVersion.value})
        </small>
    {/if}

    {#if isMenuOpened}
        <SidebarResizer initialWidth="{get(sidebarOpenedWidth$)}"
                        bind:dragging={sidebarResizing}
                        on:new-width={handleNewWidth}/>
    {/if}
</div>

<style lang="less">
    @import (reference) "styles/portaro.variables.less";
    @import (reference) "styles/portaro.themes.less";
    @import (reference) "styles/portaro-erp.less";

    @collapser-button-size: 24px;

    .erp-sidebar-nav {
        position: relative;
        height: 100%;
        z-index: @under-modal-z-index; // We want sidebar to be right under modals on z-indexing context
        display: flex;
        opacity: 0;
        flex-direction: column;
        background-color: @themed-body-bg;
        animation: 0.4s ease-in-out 0s 1 sidebar-slide-from-left;
        animation-fill-mode: forwards;
        transition: width 0.3s ease-in-out;

        &.sidebar-resizing {
            transition: none;
        }

        &.menu-collapsed {
            width: @sidebar-collapsed-width;
            align-items: center;

            .logo-container {
                height: @erp-topbar-height;
                margin-bottom: @spacing-ml;

                .erp-logo {
                    width: 26px;
                }
            }

            .navigation-container {
                overflow-x: hidden;
                padding-bottom: @spacing-l;

                .navigation-items {
                    align-items: center;
                }
            }
        }

        &.menu-opened {
            width: var(--sidebar-opened-width);

            .navigation-container {
                padding: @spacing-xl;
            }
        }

        .sidebar-collapser {
            position: absolute;
            background-color: @themed-body-bg;
            width: @collapser-button-size;
            height: @collapser-button-size;
            display: flex;
            align-items: center;
            justify-content: center;
            top: calc(@erp-topbar-height / 2);
            right: -32px;
            transform: translate(-50%, -50%);
            border-radius: @border-radius-default;
            border: 1px solid @themed-border-default;
            cursor: pointer;
            padding: 0;
            margin: 0;
            z-index: 2;
            transition: background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;

            .icon-container {
                transition: rotate 0.2s ease-in-out;

                &.collapsed {
                    rotate: 180deg;
                }
            }

            &:hover {
                border-color: var(--brand-orange-new);
                background-color: @themed-body-bg-orange-highlighted;
            }
        }

        .logo-container {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 150px;
            transition: height 0.3s ease-in-out;

            .erp-logo {
                width: 64px;
                transition: width 0.3s ease-in-out;
            }
        }

        .navigation-container {
            flex: 1;
            overflow-y: auto;

            .navigation-items {
                display: flex;
                flex-direction: column;
                gap: @spacing-xl;
            }
        }

        .system-version {
            text-align: center;
            padding: @spacing-l;
            color: @themed-text-muted;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }

    @keyframes sidebar-slide-from-left {
        0% {
            transform: translateX(-25%);
            opacity: 0;
        }
        100% {
            transform: translateX(0);
            opacity: 100%;
        }
    }

    :global {
        .erp-sidebar-nav .erp-sidebar-language-dropdown .dropdown-wrapper-button {
            display: flex;
            justify-content: space-between;
            color: @themed-text-muted;
            padding: @spacing-sm @spacing-xxl;
        }
    }
</style>