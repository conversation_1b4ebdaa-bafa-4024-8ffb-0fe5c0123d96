package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.cache.CacheService;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.database.FlushingJpaDeleter;
import cz.kpsys.portaro.database.FlushingJpaSaver;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.form.editor.ValueEditorByDatatypeLoader;
import cz.kpsys.portaro.security.PermissionResolver;
import cz.kpsys.portaro.setting.CoreSettingKeys;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.locale.UserLocaleResolver;
import cz.kpsys.portaro.userpreferences.*;
import cz.kpsys.portaro.userpreferences.gdpr.GdprNotSetPermissionResolver;
import cz.kpsys.portaro.userpreferences.locale.UserLocaleFromUserPreferenceResolver;
import cz.kpsys.portaro.view.web.rest.user.UserPreferenceApiController;
import jakarta.persistence.EntityManager;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.support.SimpleJpaRepository;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class UserDetailsConfig {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull CacheService cacheService;
    @NonNull ValueEditorByDatatypeLoader valueEditorByDatatypeLoader;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull SettingLoader settingLoader;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull EntityManager entityManager;
    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;


    @Bean
    public UserPreferenceApiController userPreferenceApiController() {
        return new UserPreferenceApiController(
                userPreferenceLoader(),
                userPreferenceDeleter(),
                userPreferenceCreator()
        );
    }

    @Bean
    public UserLocaleResolver userLocaleResolver() {
        return new UserLocaleFromUserPreferenceResolver(userPrefAccessor());
    }

    @Bean
    public PermissionResolver<BasicUser> gdprNotSetPermissionResolver() {
        return new GdprNotSetPermissionResolver(userPrefAccessor(), settingLoader.getDepartmentedProvider(SettingKeys.SHOW_ANONYMIZATION_QUESTION_DIALOG));
    }

    @Bean
    public UserPreferenceLoader userPreferenceLoader() {
        SpringDbUserPreferenceLoader bean = new SpringDbUserPreferenceLoader(
                jdbcTemplate,
                queryFactory,
                userPreferenceKeyLoader(),
                datatypableStringConverter);
        cacheService.registerCleaner(UserPreferenceKey.class.getSimpleName(), bean);
        return bean;
    }

    @Bean
    public Codebook<UserPreferenceKey, Integer> userPreferenceKeyLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbUserPreferenceKeyLoader(jdbcTemplate, queryFactory, datatypableStringConverter, valueEditorByDatatypeLoader, UserPreferenceKeyPermission.CODEBOOK))
                .staticCached(UserPreferenceKey.class.getSimpleName())
                .build();
    }

    @Bean
    public Saver<UserPreference, UserPreference> userPreferenceSaver() {
        return new TransactionalSaver<>(
                new UserPreferenceSaver(
                        new PostConvertingSaver<>(
                                new PreConvertingSaver<>(
                                        new UserPreferenceToEntityConverter(datatypableStringConverter),
                                        new FlushingJpaSaver<>(new SimpleJpaRepository<>(UserPreferenceEntity.class, entityManager))
                                ),
                                new DefaultUserPreferenceFromEntityConverter(basicUserLoader, userPreferenceKeyLoader())
                        ),
                        userPreferenceDeleter()
                ),
                defaultTransactionTemplateFactory.get()
        );
    }

    @Bean
    public UserPreferenceCreator userPreferenceCreator() {
        return new UserPreferenceCreator(
                userPreferenceSaver(),
                defaultTransactionTemplateFactory.get(),
                userPreferenceKeyLoader(),
                datatypableStringConverter,
                userPreferenceLoader()
        );
    }

    @Bean
    public Deleter<UserPreference> userPreferenceDeleter() {
        return new PreConvertingDeleter<>(
                        new UserPreferenceToEntityConverter(datatypableStringConverter),
                        new FlushingJpaDeleter<>(new SimpleJpaRepository<>(UserPreferenceEntity.class, entityManager))
                );
    }

    @Bean
    public UserPrefAccessor userPrefAccessor() {
        return new UserPrefAccessor(
                userPreferenceLoader(),
                userPreferenceSaver(),
                settingLoader.getDepartmentedProvider(CoreSettingKeys.DEFAULT_LOCALE)
        );
    }

}
