package cz.kpsys.portaro.config;

import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.record.RecordIdFondPair;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.FieldTypes;
import cz.kpsys.portaro.record.detail.binding.FondBoundFieldsLoader;
import cz.kpsys.portaro.record.detail.binding.UserBoundFieldsLoader;
import cz.kpsys.portaro.record.edit.EditableFieldType;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.ParameterizedSearchLoader;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiFunction;
import java.util.function.Function;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class PortaroRecordFieldConfig {

    @NonNull ParameterizedSearchLoader<MapBackedParams, User> userSearchLoader;

    @Bean
    public Map<FieldTypeId, BiFunction<Fond, FieldType, Optional<EditableFieldType>>> customEditableFieldTypes() {
        return Map.of(
                FieldTypes.FOND_FIELD_TYPE_ID, (fond, fieldType) -> Optional.of(EditableFieldType.ofAlwaysVisibleSingle(fieldType, fond)),
                FieldTypes.USER_FIELD_TYPE_ID, (fond, fieldType) -> {
                    if (!Fond.Bindings.USER.equals(fond.getBinding())) {
                        return Optional.empty();
                    }
                    return Optional.of(EditableFieldType.ofAlwaysVisibleSingle(fieldType, fond));
                }
        );
    }

    @Bean
    public Map<FieldTypeId, Function<Set<RecordIdFondPair>, Map<RecordIdFondPair, ? extends Identified<?>>>> bindingLoaders() {
        return Map.of(
                FieldTypes.FOND_FIELD_TYPE_ID, new FondBoundFieldsLoader(),
                FieldTypes.USER_FIELD_TYPE_ID, new UserBoundFieldsLoader(userSearchLoader)
        );
    }

}
