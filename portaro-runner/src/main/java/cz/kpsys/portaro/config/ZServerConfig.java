package cz.kpsys.portaro.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import cz.kpsys.portaro.app.CatalogConstants;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.cache.FallbackableCompositeCacheManager;
import cz.kpsys.portaro.commons.contextual.CompositeAllValuesContextualProvider;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.contextual.FilteredAndSortedAllValuesContextualProvider;
import cz.kpsys.portaro.commons.convert.AllSavingNoopListConverter;
import cz.kpsys.portaro.commons.convert.ListToModifiedListConverter;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.CompositeAddableAllValuesProvider;
import cz.kpsys.portaro.commons.object.ConvertingAllValuesProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.datatype.DatatypedAcceptableValuesRegistry;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordFactory;
import cz.kpsys.portaro.record.datasource.Datasource;
import cz.kpsys.portaro.record.detail.appservermarc.MarcXmlToDetailConverterImpl;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.fond.BestSuitableFondResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.search.ConvertingStandardSearch;
import cz.kpsys.portaro.record.search.factory.SearchFactoryResolverMatcherByDatasourceGroup;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryCaching;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.field.SearchField;
import cz.kpsys.portaro.search.restriction.Restriction;
import cz.kpsys.portaro.search.z.*;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Configuration
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class ZServerConfig {

    @NonNull CodebookLoaderBuilderFactory codebookLoaderBuilderFactory;
    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull SettingLoader settingLoader;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull DatatypedAcceptableValuesRegistry allDatatypeToAllValuesProviderMap;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull RecordFactory recordFactory;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull Function<String, List<Fond>> subkindToEnabledFondsExpander;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull FallbackableCompositeCacheManager cacheManager;
    @NonNull Saver<Record, Record> externalRecordRepository;
    @NonNull CompositeAddableAllValuesProvider<Datasource> allDatasetsProvider;
    @NonNull CompositeAllValuesContextualProvider<Department, Datasource> allowedDatasetsProvider;
    @NonNull CurrentAuthFondsLoader currentAuthEditableFondsLoader;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull BestSuitableFondResolver bestSuitableFondResolver;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;


    @Bean
    public Codebook<ZServer, Integer> zServerLoader() {
        return codebookLoaderBuilderFactory.create()
                .providedBy(new SpringDbZServerLoader(jdbcTemplate, queryFactory, fondLoader, enabledFondsProvider))
                .staticCached(ZServer.class.getSimpleName())
                .build();
    }

    @Bean
    public ContextualProvider<Department, List<ZServer>> allowedZServersDepartmentedProvider() {
        return new FilteredAndSortedAllValuesContextualProvider<>(
                zServerLoader(),
                settingLoader.getDepartmentedProvider(SettingKeys.ENABLED_ZSERVERS)
        ).withConditionallyShowAll(settingLoader.getDepartmentedProvider(SettingKeys.SHOW_ALL_ZSERVERS));
    }

    @Bean
    public AllValuesProvider<ZServerDatasource> allZServerDatasetsProvider() {
        return ConvertingAllValuesProvider.byItemConverter(
                zServerLoader(),
                new ZServerToZServerDatasetConverter()
        );
    }

    @Bean
    public ContextualProvider<Department, List<ZServerDatasource>> allowedZServerDatasetsProvider() {
        return allowedZServersDepartmentedProvider()
                .andThen(new ListToModifiedListConverter<>(new ZServerToZServerDatasetConverter())::convert);
    }

    @Bean
    public ZServerService zServerService() {
        ZServerServiceAppserver pureService = new ZServerServiceAppserver(
                mappingAppserver,
                zServerLoader(),
                new ZServerQueryBuilder(restrictionToZServerQueryConverter()),
                new MarcXmlToDetailConverterImpl(
                        true,
                        fieldTypesByFondLoader,
                        recordFieldStringToInstantConverter,
                        fondLoader
                ),
                bestSuitableFondResolver
        );
        return new ParametersConvertingZServerService(pureService)
                .withExpandingParam(CoreSearchParams.SUBKIND, RecordConstants.SearchParams.FOND, subkindToEnabledFondsExpander)
                .withExpandingParam(RecordConstants.SearchParams.ROOT_FOND, RecordConstants.SearchParams.FOND, enabledLoadableFondsExpander);
    }

    @Bean
    public SearchFactory zServerSearchFactory() {
        SearchFactory zServerSearchFactory = new SearchFactoryMatching(new SearchFactoryResolverMatcherByDatasourceGroup(ZServerDatasource.DATASOURCE_EXTERNAL_GROUP)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                    p.set(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx));

                    Datasource firstSupportedDatasource = allowedDatasetsProvider.getOn(ctx).stream()
                            .filter(datasource -> !customParams.has(RecordConstants.SearchParams.DATASOURCE_GROUP) || datasource.isOfGroup(customParams.get(RecordConstants.SearchParams.DATASOURCE_GROUP)))
                            // COMMENTED OUT - SUBKIND se zjevne na backend neposila
//                            .filter(datasource -> {
//                                if (!customParams.has(CoreSearchParams.SUBKIND)) {
//                                    return true;
//                                }
//                                List<String> supportedSubkinds = new ArrayList<>(2);
//                                if (customParams.get(CoreSearchParams.SUBKIND).contains(BasicMapSearchParams.SUBKIND_DOCUMENT)) {
//                                    supportedSubkinds.add(BasicMapSearchParams.SUBKIND_DOCUMENT);
//                                }
//                                if (customParams.get(CoreSearchParams.SUBKIND).contains(BasicMapSearchParams.SUBKIND_AUTHORITY)) {
//                                    supportedSubkinds.add(BasicMapSearchParams.SUBKIND_AUTHORITY);
//                                }
//                                return datasource.supportsAnyOfSubkind(supportedSubkinds);
//                            })
                            .filter(datasource -> datasource.supportsAnyOfFonds(p.get(RecordConstants.SearchParams.ROOT_FOND)))
                            .findFirst()
                            .orElseThrow(() -> new ItemNotFoundException(Datasource.class, p, Texts.ofNative("No datasource (z-server) supporting/allowed this search parameters")));
                    p.set(RecordConstants.SearchParams.DATASOURCE, firstSupportedDatasource);
                });

                List<Fond> userEditableFonds = currentAuthEditableFondsLoader.getAllByAuth(currentAuth, ctx);
                Converter<List<ZServerRecord>, List<Record>> zServerRecordsToRecordsConverter = new ZServerRecordsToRecordsConverter(recordFactory, userEditableFonds)
                        .andThen(new AllSavingNoopListConverter<>(externalRecordRepository));

                return ConvertingStandardSearch.of(new ZServerSearch(zServerService()), zServerRecordsToRecordsConverter)
                        .pageSizeChangeable(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };

        //cached zservice search
        CaffeineCacheManager zServerSearchCaching = new CaffeineCacheManager(CatalogConstants.Search.SEARCH_CACHE_NAME);
        zServerSearchCaching.setCaffeine(
                Caffeine.newBuilder()
                        .maximumSize(100)
                        .expireAfterWrite(10, TimeUnit.MINUTES)
                        .removalListener((key, value, cause) -> {
                            if (cause != RemovalCause.REPLACED) { // if id is replaced by same id, don't call removal listener
                                ((Search<?, ?, ?>) Objects.requireNonNull(value)).close();
                            }
                        }));
        cacheManager.addCacheManager(zServerSearchCaching);
        Cache searchCache = Objects.requireNonNull(cacheManager.getCache(CatalogConstants.Search.SEARCH_CACHE_NAME));
        return new SearchFactoryCaching(searchCache, zServerSearchFactory);
    }

    @Bean
    public Converter<Restriction<? extends SearchField>, String> restrictionToZServerQueryConverter() {
        return new RestrictionToZServerQueryConverter(new ZServerQueryFieldLoaderInMemory());
    }



    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        allDatatypeToAllValuesProviderMap.registerEquals(CatalogConstants.Datatype.Z_SERVER.getName(), zServerLoader());
        converterRegisterer.registerForIntegerId(ZServer.class, zServerLoader());

        searchFactoryResolver.with(zServerSearchFactory(), 2000); // order must be lower than globalSearchFactory
        allDatasetsProvider.withProvider(allZServerDatasetsProvider());
        allowedDatasetsProvider.add(allowedZServerDatasetsProvider());
    }

}
