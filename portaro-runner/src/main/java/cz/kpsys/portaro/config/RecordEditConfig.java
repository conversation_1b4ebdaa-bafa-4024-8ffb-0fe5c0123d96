package cz.kpsys.portaro.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.kpsys.portaro.appserver.config.AppserverConfigService;
import cz.kpsys.portaro.appserver.mapping.MappingAppserverService;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.cache.CacheBackedRepository;
import cz.kpsys.portaro.commons.cache.DynamicCache;
import cz.kpsys.portaro.commons.cache.GuavaTimedDynamicCache;
import cz.kpsys.portaro.commons.contextual.ContextualConsumer;
import cz.kpsys.portaro.commons.contextual.ContextualFunction;
import cz.kpsys.portaro.commons.contextual.SwitchContextualProvider;
import cz.kpsys.portaro.commons.date.StringToInstantConverter;
import cz.kpsys.portaro.commons.localization.MultiText;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AllValuesProvider;
import cz.kpsys.portaro.commons.object.IdentifiedValue;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.SeveritedException;
import cz.kpsys.portaro.commons.object.repo.*;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.databaseproperties.DatabaseConnectionSettings;
import cz.kpsys.portaro.databaseproperties.DatabaseProperties;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.directory.DirectoryLoader;
import cz.kpsys.portaro.form.editor.ValueEditorByDatatypeLoader;
import cz.kpsys.portaro.form.valueeditor.bool.BooleanValueEditorModifier;
import cz.kpsys.portaro.formconfig.valueeditor.AuthenticatedAcceptableValuesResolver;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.marcxml.convert.JacksonStringToRecordMarcDtoConverter;
import cz.kpsys.portaro.marcxml.model.LenientVerbisRecordMarcDto;
import cz.kpsys.portaro.marcxml.model.RecordMarcDto;
import cz.kpsys.portaro.marcxml.model.StrictVerbisRecordMarcDto;
import cz.kpsys.portaro.record.*;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.copy.RecordCopyRequest;
import cz.kpsys.portaro.record.deletion.*;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.detail.dflt.*;
import cz.kpsys.portaro.record.discardation.*;
import cz.kpsys.portaro.record.edit.*;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.view.DatatypeResolvingValueEditorByFieldTypeLoader;
import cz.kpsys.portaro.record.edit.view.SourceRecordSupportingRecordEditorFactory;
import cz.kpsys.portaro.record.edit.view.StandardRecordEditorFactory;
import cz.kpsys.portaro.record.edit.view.UserInteractionRecordEditation;
import cz.kpsys.portaro.record.file.DirectoryAndItsContentIdLoader;
import cz.kpsys.portaro.record.fond.BestSuitableFondResolver;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondTypeResolver;
import cz.kpsys.portaro.record.fond.InclusionLoader;
import cz.kpsys.portaro.record.holding.*;
import cz.kpsys.portaro.record.importer.marc.RecordImporter;
import cz.kpsys.portaro.record.importer.marc.RecordMarcDtoImporter;
import cz.kpsys.portaro.record.load.RecordDayIdLoader;
import cz.kpsys.portaro.record.load.RecordFieldsLoader;
import cz.kpsys.portaro.record.merge.RecordMergeRequest;
import cz.kpsys.portaro.record.operation.CachingRecordEditLevelProvider;
import cz.kpsys.portaro.record.prop.DetailRecordPropertiesGenerator;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.record.view.EditableFieldTypesToResponseConverter;
import cz.kpsys.portaro.record.view.ViewableRecordEditationFactory;
import cz.kpsys.portaro.security.*;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.CustomSetting;
import cz.kpsys.portaro.setting.CustomSettingEntity;
import cz.kpsys.portaro.setting.CustomSettingLoader;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.UserByBasicUserLoader;
import cz.kpsys.portaro.user.role.editor.EditLevel;
import cz.kpsys.portaro.user.role.editor.EditorAccount;
import cz.kpsys.portaro.user.role.editor.LibrarianPrivileges;
import cz.kpsys.portaro.view.web.rest.record.EditableFieldTypeApiController;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.converter.Converter;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.time.Duration;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static cz.kpsys.portaro.commons.util.StringUtil.listToStringOfIds;
import static cz.kpsys.portaro.security.PermissionResolver.*;
import static cz.kpsys.portaro.user.BasicUser.ROLE_LIBRARIAN;
import static cz.kpsys.portaro.user.BasicUser.ROLE_READER;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordEditConfig {

    @NonNull ObjectMapper appserverXmlMapper;
    @NonNull MappingAppserverService mappingAppserver;
    @NonNull SecurityManager securityManager;
    @NonNull RecordSaver recordSaver;
    @NonNull HierarchyLoader<Department> contextHierarchyLoader;
    @NonNull DetailRecordPropertiesGenerator detailRecordPropertiesGenerator;
    @NonNull ByIdLoadable<FieldType, FieldTypeId> subfieldTypeLoader;
    @NonNull AllValuesProvider<Fond> enabledFondsProvider;
    @NonNull Provider<@NonNull Fond> defaultNonPeriodicalDocumentFondProvider;
    @NonNull Provider<@NonNull Fond> defaultPeriodicalDocumentFondProvider;
    @NonNull Provider<@NonNull Fond> defaultAuthorityFondProvider;
    @NonNull DynamicCache<Record> recordCache;
    @NonNull SettingLoader settingLoader;
    @NonNull CurrentAuthFondsLoader currentAuthEditableFondsLoader;
    @NonNull ContextualConsumer<RecordDeletionCommand, Department> subtreeDepartmentedRecordExemplarsDeleter;
    @NonNull ConverterRegisterer converterRegisterer;
    @NonNull CustomSettingLoader customSettingLoader;
    @NonNull Saver<CustomSetting<String>, CustomSettingEntity> customSettingSaver;
    @NonNull PermissionRegistry permissionRegistry;
    @NonNull PermissionFactory permissionFactory;
    @NonNull ByIdLoadable<Fond, Integer> fondLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> nonDetailedRichRecordLoader;
    @NonNull CachingRecordEditLevelProvider recordEditLevelProvider;
    @NonNull RecordHoldingLoader recordHoldingLoader;
    @NonNull AllValuesProvider<IdentifiedValue<DefaultFieldValueId, @NotBlank String>> defaultFieldValueLoader;
    @NonNull RecordValidator recordValidator;
    @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull IdAndIdsLoadable<Record, UUID> recordLoader;
    @NonNull RecordFieldsLoader recordFieldsLoader;
    @NonNull RecordEntryFieldTypeIdResolver recordEntryFieldTypeIdResolver;
    @NonNull ConversionService conversionService;
    @NonNull InclusionLoader<Fond> enabledFondInheritanceLoader;
    @NonNull AllValuesProvider<Fond> enabledDocumentFondsProvider;
    @NonNull ValueEditorByDatatypeLoader valueEditorByDatatypeLoader;
    @NonNull StringToInstantConverter recordFieldStringToInstantConverter;
    @NonNull SaverBuilderFactory saverBuilderFactory;
    @NonNull ContextualFunction<Record, Department, Boolean> singleNodeDepartmentedRecordHasExemplarsPredicate;
    @NonNull DefaultFieldValueResolver defaultFieldValueResolver;
    @NonNull RecordHoldingUpserter recordHoldingUpserter;
    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull DatabaseProperties databaseProperties;
    @NonNull Runnable saveTransactionAuthenticator;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull AppserverConfigService appserverConfigService;
    @NonNull DirectoryLoader directoryLoader;
    @NonNull UserByBasicUserLoader userLoader;
    @NonNull RecordDayIdLoader recordDayIdLoader;


    @Bean
    public RecordEditationAvailableFondsResolver recordEditationAvailableFondsResolver() {
        return new RecordEditationAvailableFondsResolver(currentAuthEditableFondsLoader, enabledFondInheritanceLoader);
    }


    @Bean
    public ViewableRecordEditationFactory viewableRecordEditationFactory() {
        return new ViewableRecordEditationFactory(
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_EDITED_INDICATORS_ENABLED),
                settingLoader.getDepartmentedProvider(SettingKeys.SHOW_EDITED_FIELD_IDENTIFIERS_ENABLED),
                recordEditationAvailableFondsResolver()
        );
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordMergeRequest> recordMergeRequestDefaulter() {
        return (formObject, ctx, currentAuth) -> {
            if (formObject.mergeDetail() == null) {
                formObject = formObject.withMergeDetail(true);
            }
            return formObject;
        };
    }

    @Bean
    public AuthenticatedAcceptableValuesResolver<RecordCopyRequest, Fond> recordCopyRequestAllowedFondsResolver() {
        return new RecordCopyRequest.RecordCopyRequestAllowedFondsResolver(currentAuthEditableFondsLoader);
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordCopyRequest> recordCopyRequestDefaulter() {
        return new RecordCopyRequest.RecordCopyRequestDefaulter();
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordCopyRequest> recordCopyRequestPreValidationModifier() {
        return new RecordCopyRequest.RecordCopyRequestPreValidationModifier();
    }

    @Bean
    public DefaultFieldValueApiController defaultFieldValueApiController() {
        return new DefaultFieldValueApiController(
                defaultFieldValueLoader,
                defaultFieldValueSaver()
        );
    }

    @Bean
    public RecordImportApiController recordImportApiController() {
        return new RecordImportApiController(
                stringToStrictRecordMarcDtoConverter(),
                stringToLenientVerbisRecordMarcDtoConverter(),
                recordMarcDtoImporter()
        );
    }

    @Bean
    public RecordImporter<RecordMarcDto> recordMarcDtoImporter() {
        return new RecordMarcDtoImporter(
                recordEditationFactory(),
                recordFieldEditor(),
                fondLoader,
                recordFieldStringToInstantConverter
        );
    }

    @Bean
    public Converter<String, StrictVerbisRecordMarcDto> stringToStrictRecordMarcDtoConverter() {
        return JacksonStringToRecordMarcDtoConverter.ofStrict();
    }

    @Bean
    public Converter<String, LenientVerbisRecordMarcDto> stringToLenientVerbisRecordMarcDtoConverter() {
        return JacksonStringToRecordMarcDtoConverter.ofLenientVerbis();
    }

    @Bean
    public EditableFieldTypeApiController editableFieldTypeApiController() {
        return new EditableFieldTypeApiController(
                recordEditableFieldTypesLoader(),
                editableFieldTypesToResponseConverter()
        );
    }

    @Bean
    public EditableFieldTypesToResponseConverter editableFieldTypesToResponseConverter() {
        return new EditableFieldTypesToResponseConverter(
                recordEntryFieldTypeIdResolver,
                valueEditorByFieldTypeLoader()
        );
    }

    @Bean
    public RecordEditationFactory recordEditationFactory() {
        return new RecordEditationFactory(
                recordEditableFieldTypesLoader(),
                recordValidator,
                recordSaver,
                fieldTypesByFondLoader,
                new SwitchContextualProvider<>(settingLoader.getDepartmentedProvider(SettingKeys.PUBLISHING_DOCUMENT_FINISHED_CATALOGIZATION), RecordCatalogizationPhase.CATALOGING, RecordCatalogizationPhase.CATALOGED),
                recordHoldingUpserter,
                fieldValueCommandResolver(),
                fieldTypeLoader,
                recordFieldEditor(),
                recordFieldsLoader,
                defaultFieldValueCommandResolver()
        );
    }

    @Bean
    public DefaultFieldValueCommandResolver defaultFieldValueCommandResolver() {
        return new DelegatingDefaultFieldValueCommandResolver(
                defaultFieldValueResolver,
                new DefaultFieldValueGenerator(recordLoader, recordDayIdLoader, userLoader)
        );
    }

    @Bean
    public RecordFieldEditor recordFieldEditor() {
        return new RecordFieldEditor(
                fieldTypeLoader,
                recordSaver,
                recordHoldingUpserter,
                defaultFieldValueCommandResolver()
        );
    }

    @Bean
    public RecordEditationHelper recordEditationHelper() {
        return new RecordEditationHelper(recordFieldEditor());
    }

    @Bean
    public FieldValueCommandResolver fieldValueCommandResolver() {
        return new FieldValueCommandResolver(recordLoader, conversionService);
    }

    @Bean
    public RecordFieldTypesLoader recordEditableFieldTypesLoader() {
        return new MissingAddingRecordEditableFieldTypesLoader(
                new ByFondLoaderDelegatingRecordFieldTypesLoader(fieldTypesByFondLoader),
                fieldTypesByFondLoader,
                subfieldTypeLoader
        );
    }

    @Bean
    public DatatypeResolvingValueEditorByFieldTypeLoader valueEditorByFieldTypeLoader() {
        return new DatatypeResolvingValueEditorByFieldTypeLoader(
                valueEditorByDatatypeLoader,
                new SourceRecordSupportingRecordEditorFactory(new StandardRecordEditorFactory(), enabledDocumentFondsProvider)
        );
    }

    @Bean
    public RecordFactory recordFactory() {
        return new RecordFactory(
                detailRecordPropertiesGenerator,
                recordValidator
        );
    }

    @Bean
    public BestSuitableFondResolver bestSuitableFondResolver() {
        return new BestSuitableFondResolver(
                enabledFondsProvider,
                defaultNonPeriodicalDocumentFondProvider,
                defaultPeriodicalDocumentFondProvider,
                defaultAuthorityFondProvider,
                recordValidator
        );
    }

    @Bean
    public Saver<DefaultFieldValueSaveCommand, CustomSettingEntity> defaultFieldValueSaver() {
        return new PreConvertingSaver<>(
                new DefaultFieldValueSaveCommandToCustomSettingConverter(customSettingLoader),
                customSettingSaver
        );
    }

    @Bean
    public Repository<UserInteractionRecordEditation, String> recordEditationRepository() {
        Repository<UserInteractionRecordEditation, String> delegate = CacheBackedRepository.ofIdentified(GuavaTimedDynamicCache.ofIdentified(Duration.ofHours(2), false));
        return new NotFoundExceptionTranslatingRepository<>(delegate, source -> {
            Object desiredItemIdentifier = source instanceof ItemNotFoundException ? ((ItemNotFoundException) source).getDesiredItemIdentifier() : null;
            return new SeveritedItemNotFoundException(UserInteractionRecordEditation.class, desiredItemIdentifier, null, Texts.ofNative("Editation is expired or not exists"), SeveritedException.SEVERITY_WARNING);
        });
    }

    @Bean
    public RecordDeleter recordDeleter() {
        AppserverRecordDeleter bean = new AppserverRecordDeleter(
                mappingAppserver,
                subtreeDepartmentedRecordExemplarsDeleter,
                appserverXmlMapper,
                List.of(recordCache)
        );
        return new SecuredRecordDeleter(bean, securityManager);
    }

    @Bean
    public RecordDiscarder recordDiscarder() {
        AppServerRecordHoldingDiscarder bean = new AppServerRecordHoldingDiscarder(
                mappingAppserver,
                appserverXmlMapper,
                List.of(recordCache)
        );
        return new SecuredRecordDiscarder(bean, securityManager);
    }

    @Bean
    public RecordHardDeleter recordHardDeleter() {
        return new RecordHardDeleter(
                notAutoCommittingJdbcTemplate,
                defaultTransactionTemplateFactory.get(),
                queryFactory,
                saveTransactionAuthenticator,
                directoryAndItsContentIdLoader(),
                appserverConfigService,
                databaseProperties.getType().equals(DatabaseConnectionSettings.DBTYPE_FIREBIRD)
        );
    }

    @Bean
    public DirectoryAndItsContentIdLoader directoryAndItsContentIdLoader() {
        return new DirectoryAndItsContentIdLoader(
                notAutoCommittingJdbcTemplate,
                defaultTransactionTemplateFactory.get(),
                queryFactory,
                directoryLoader
        );
    }

    @Bean
    public Saver<RecordHolding, RecordHolding> recordHoldingSaver() {
        return saverBuilderFactory.<RecordHolding, UUID>saver()
                .intermediateConverting(new RecordHoldingToEntityConverter())
                .withClearedCacheName(RecordHoldingEntity.class.getSimpleName())
                .build();
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<RecordHoldingDeletionRequest> recordHoldingDeletionRequestDefaulter() {
        return new RecordHoldingDeletionRequestDefaulter();
    }

    @Bean
    public BooleanValueEditorModifier<RecordHoldingDeletionRequest> recordHoldingDeletionAcceptingExemplarsDeletionEditorModifier() {
        return new RecordHoldingDeletionRequest.RecordHoldingDeletionAcceptingExemplarsDeletionEditorModifier(recordLoader, singleNodeDepartmentedRecordHasExemplarsPredicate);
    }

    @Bean
    public Deleter<RecordHoldingDeletionCommand> recordHoldingDeleter() {
        RecordDeleterDelegatingRecordHoldingDeleter bean = new RecordDeleterDelegatingRecordHoldingDeleter(recordLoader, recordDeleter());
        return new SecuredRecordHoldingDeleter(bean, securityManager);
    }

    @Bean
    public RecordHoldingDiscarder recordHoldingDiscarder() {
        RecordDeleterDelegatingRecordHoldingDiscarder bean = new RecordDeleterDelegatingRecordHoldingDiscarder(
                recordLoader,
                recordDiscarder(),
                singleNodeDepartmentedRecordHasExemplarsPredicate);
        return new SecuredRecordHoldingDiscarder(bean, securityManager);
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        converterRegisterer
                .registerForStringId(RecordAutoSavingPolicyRequest.class, RecordAutoSavingPolicyRequest.CODEBOOK)
                .registerForStringId(UserInteractionRecordEditation.class, recordEditationRepository());

        registerPermissions();
    }

    private void registerPermissions() {
        PermissionResolver<Fond> editFond = and(
                permissionFactory.currentEvidedAuthenticActiveWithAllRoles(BasicUser.ROLE_LIBRARIAN),
                (auth, ctx, fond) -> {
                    List<Fond> editableFonds = currentAuthEditableFondsLoader.getAllByAuth(auth, ctx);
                    return PermissionResult.ifCan(editableFonds.contains(fond), auth)
                            .withReasonWhenForbid(MultiText.ofTexts(Texts.ofNative("You dont have permission to edit fond"), fond.getText(), Texts.ofNative("You can edit only"), Texts.ofNative(listToStringOfIds(editableFonds, ", "))).withSpaceDelimiter());
                }
        );

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_ANY, or(
                permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_DOCUMENT_CREATE),
                permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_AUTHORITY_CREATE)
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_OF_KIND, (auth, ctx, kind) -> {
            PermissionResult createAnyRecordPerm = permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_ANY).can(auth, ctx, null);
            if (createAnyRecordPerm.forbidden()) {
                return createAnyRecordPerm;
            }

            // authority
            if (kind.equals(Record.TYPE_AUTHORITY)) {
                PermissionResult authorityCreateAction = permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_AUTHORITY_CREATE).can(auth, ctx, null);
                if (authorityCreateAction.forbidden()) {
                    return authorityCreateAction;
                }
                if (currentAuthEditableFondsLoader.getAllByAuth(auth, ctx).stream().noneMatch(FondTypeResolver::isAuthorityFond)) {
                    return PermissionResult.cannot(auth, Texts.ofNative("You dont have user permission (in Verbis) to edit any authority fond"));
                }
                return PermissionResult.allow();
            }

            // document
            PermissionResult documentCreateAction = permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(LibrarianPrivileges.ACTION_DOCUMENT_CREATE).can(auth, ctx, null);
            if (documentCreateAction.forbidden()) {
                return documentCreateAction;
            }
            if (currentAuthEditableFondsLoader.getAllByAuth(auth, ctx).stream().noneMatch(FondTypeResolver::isDocumentFond)) {
                return PermissionResult.cannot(auth, Texts.ofNative("You dont have user permission (in Verbis) to edit any document fond"));
            }
            return PermissionResult.allow();
        });

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_OF_DOCUMENT_FOND, (auth, ctx, unused) -> permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_OF_KIND).can(auth, ctx, Record.TYPE_DOCUMENT));

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_OF_AUTHORITY_FOND, (auth, ctx, unused) -> permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_OF_KIND).can(auth, ctx, Record.TYPE_AUTHORITY));

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_OF_FOND, and(
                (auth, ctx, fond) -> {
                    String subkind = FondTypeResolver.isDocumentFond(fond) ? Record.TYPE_DOCUMENT : Record.TYPE_AUTHORITY;
                    return permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_OF_KIND).can(auth, ctx, subkind);
                },
                editFond
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_DRAFT_SAVE, permissionFactory.currentEvidedAuthenticActive());

        permissionRegistry.add(RecordSecurityActions.RECORD_PUBLISH, adaptingSubject(Record::getFond, permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_OF_FOND)));

        permissionRegistry.add(RecordSecurityActions.RECORD_CREATE_DRAFT_OF_FOND, or(
                permissionRegistry.getLazy(RecordSecurityActions.RECORD_CREATE_OF_FOND),
                permissionFactory.currentEvidedAuthenticActiveWithAllRoles(ROLE_READER)
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_EDIT, and(
                (auth, ctx, record) -> !record.isDeleted() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Record is already deleted")),
                and(
                        (auth, ctx, record) -> {
                            int actionId = FondTypeResolver.isDocumentFond(record) ? LibrarianPrivileges.ACTION_DOCUMENT_EDIT : LibrarianPrivileges.ACTION_AUTHORITY_EDIT;
                            return permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(actionId).can(auth, ctx, null);
                        },
                        adaptingSubject(Record::getFond, editFond),
                        currentEvidedAuthenticActiveLibrarianWithSufficientEditLevelToEditRecord(),
                        editedRecordHasHoldingsOnEditableDepartmentsOfCurrentCtxSubtree()
                )
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_HOLDING_DELETE_OF_RECORD, and(
                (auth, ctx, record) -> !record.isDeleted() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Record is already deleted")),
                or(
                        (auth, ctx, record) -> !record.isActive() ? PermissionResult.allow() : PermissionResult.forbid(),
                        and(
                                (auth, ctx, record) -> {
                                    int actionId = FondTypeResolver.isDocumentFond(record) ? LibrarianPrivileges.ACTION_DOCUMENT_DELETE : LibrarianPrivileges.ACTION_AUTHORITY_DELETE;
                                    return permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(actionId).can(auth, ctx, null);
                                },
                                adaptingSubject(Record::getFond, editFond)
                        )
                )
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_HOLDING_DISCARD_OF_RECORD, and(
                (auth, ctx, record) -> !record.isDeleted() ? PermissionResult.allow() : PermissionResult.pointless(Texts.ofNative("Record is already deleted")),
                or(
                        (auth, ctx, record) -> !record.isActive() ? PermissionResult.allow() : PermissionResult.forbid(),
                        (auth, ctx, record) -> {
                            int actionId = FondTypeResolver.isDocumentFond(record) ? LibrarianPrivileges.ACTION_DOCUMENT_DELETE : LibrarianPrivileges.ACTION_AUTHORITY_DELETE;
                            return permissionFactory.currentEvidedAuthenticEditWithCatalogLicencedAction(actionId).can(auth, ctx, null);
                        }
                )
        ));

        // holding deletion should be same as record deletion on current department -> delegate
        permissionRegistry.add(RecordSecurityActions.RECORD_HOLDING_DELETE, adaptingSubject(
                recordHolding -> nonDetailedRichRecordLoader.getById(recordHolding.getRecordId()),
                permissionRegistry.getLazy(RecordSecurityActions.RECORD_HOLDING_DELETE_OF_RECORD)
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_HOLDING_DISCARD, adaptingSubject(
                recordHolding -> nonDetailedRichRecordLoader.getById(recordHolding.getRecordId()),
                permissionRegistry.getLazy(RecordSecurityActions.RECORD_HOLDING_DISCARD_OF_RECORD)
        ));

        permissionRegistry.add(RecordSecurityActions.RECORD_MERGE_FROM, permissionRegistry.getLazy(RecordSecurityActions.RECORD_HOLDING_DELETE_OF_RECORD));

        permissionRegistry.add(RecordSecurityActions.RECORD_MERGE_TO, permissionRegistry.getLazy(RecordSecurityActions.RECORD_EDIT));
    }

    private PermissionResolver<Record> currentEvidedAuthenticActiveLibrarianWithSufficientEditLevelToEditRecord() {
        return and(
                permissionFactory.currentEvidedAuthenticActiveWithAllRoles(ROLE_LIBRARIAN),
                (auth, ctx, record) -> {
                    Stream<EditorAccount> editorAccounts = auth.getActiveUser().roleStreamOn(EditorAccount.class, ctx);
                    EditLevel lastRecordEditLevel = recordEditLevelProvider.getRecordEditLevel(record);
                    if (editorAccounts.noneMatch(editorAccount -> editorAccount.getEditLevel().isBetterThan(lastRecordEditLevel))) {
                        return PermissionResult.cannot(auth, Texts.ofNative("Record is locked (You have not sufficient edit level to edit/delete record)"));
                    }
                    return PermissionResult.allow();
                }
        );
    }

    private PermissionResolver<Record> editedRecordHasHoldingsOnEditableDepartmentsOfCurrentCtxSubtree() {
        return (auth, ctx, record) -> {
            List<Department> recordHoldingDepartments = ListUtil.convert(recordHoldingLoader.getAllByRecordId(record.getId()), RecordHolding::getDepartment); // we do not use "recordHoldingLoader.getAllByRecordIdAndRootDepartment" variant to check if record has absolutely no holdings anywhere
            if (recordHoldingDepartments.isEmpty()) { // if record does not have any holdings, allow
                return PermissionResult.allow();
            }
            List<Department> ctxFamilyDepartments = contextHierarchyLoader.getAllByScope(ctx, HierarchyLoadScope.FAMILY); // we can edit record also when we are on any of subdepartments of currentDepartment (see #20762)
            List<Department> ctxFilteredHoldingDepartments = ListUtil.intersection(recordHoldingDepartments, ctxFamilyDepartments);
            if (ctxFilteredHoldingDepartments.isEmpty()) {
                return PermissionResult.cannot(auth, Texts.ofNative("You cannot edit record %s which does not have any holding on your editable departments (recordHoldingCtx: %s, ctxFamilyDepartments: %s)".formatted(record.getId(), recordHoldingDepartments.toString(), ctxFamilyDepartments.toString())));
            }
            return orSubjects(permissionFactory.editSubjectWithDepartment(), auth, ctx, ctxFilteredHoldingDepartments);
        };
    }

}
