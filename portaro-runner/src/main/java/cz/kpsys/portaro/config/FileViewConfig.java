package cz.kpsys.portaro.config;

import cz.kpsys.portaro.auth.AuthenticationHolder;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.auth.context.TypedAuthenticatedContextualObjectModifier;
import cz.kpsys.portaro.commons.convert.ListToModifiedListExtendingConverter;
import cz.kpsys.portaro.commons.io.FileDataStreamer;
import cz.kpsys.portaro.commons.io.FilesystemService;
import cz.kpsys.portaro.commons.io.JavaTempFileService;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.AndBooleanProvider;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.file.FileAccessType;
import cz.kpsys.portaro.file.FileProcessingState;
import cz.kpsys.portaro.file.FileSearchParams;
import cz.kpsys.portaro.file.IdentifiedFile;
import cz.kpsys.portaro.file.directory.*;
import cz.kpsys.portaro.file.security.FileSecurityActions;
import cz.kpsys.portaro.file.security.IdentifiedFileDeletionCommand;
import cz.kpsys.portaro.file.security.IdentifiedFileSaveCommand;
import cz.kpsys.portaro.file.web.AppserverChunkedFileUploadService;
import cz.kpsys.portaro.file.web.ChunkedFileUploadService;
import cz.kpsys.portaro.file.web.FSChunkedFileUploadService;
import cz.kpsys.portaro.file.web.FallbackingChunkedFileUploadService;
import cz.kpsys.portaro.filter.FilterLoader;
import cz.kpsys.portaro.form.form.Form;
import cz.kpsys.portaro.form.valueeditor.number.NumberValueEditor;
import cz.kpsys.portaro.form.valueeditor.text.TextValueEditor;
import cz.kpsys.portaro.record.file.cover.CoverFactory;
import cz.kpsys.portaro.record.file.cover.CoverService;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import cz.kpsys.portaro.search.view.CompositeSearchFormFactory;
import cz.kpsys.portaro.search.view.SearchFormFactory;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.tx.TransactionTemplateFactory;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.web.FileDownloadController;
import cz.kpsys.portaro.view.web.rest.FilterApiController;
import cz.kpsys.portaro.view.web.rest.file.*;
import cz.kpsys.portaro.view.web.rest.record.RecordCoverApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import static cz.kpsys.portaro.file.FileSettingKeys.BLOB_DIR_ENABLED;
import static cz.kpsys.portaro.file.FileSettingKeys.PORTARO_BLOB_DIR_FILES_DIRECT_LOAD;
import static cz.kpsys.portaro.form.editedproperty.EditedProperty.createWithProperty;

@Configuration
@Lazy
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class FileViewConfig {

    @NonNull FilterLoader filterLoader;
    @NonNull ByIdLoadable<IdentifiedFile, Long> identifiedFileLoader;
    @NonNull ParameterizedSearchLoader<MapBackedParams, IdentifiedFile> fileSearchLoader;
    @NonNull Saver<IdentifiedFile, IdentifiedFile> primaryCoverSavingLoadedIdentifiedFileSaver;
    @NonNull Deleter<IdentifiedFileDeletionCommand> securedIdentifiedFileDeleter;
    @NonNull DirectoryLoader directoryLoader;
    @NonNull Deleter<ParentableDirectory> parentableDirectoryDeleter;
    @NonNull DirectoryInsightLoader directoryInsightLoader;
    @NonNull FileDataStreamer securedFileThumbnailDataStreamer;
    @NonNull FileDataStreamer securedFileDataStreamer;
    @NonNull FileDataStreamer securedFileTextDataStreamer;
    @NonNull CoverService coverService;
    @NonNull Provider<@NonNull FileAccessType> defaultFileAccessTypeProvider;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull CompositeSearchFormFactory searchFormFactory;
    @NonNull SecurityManager securityManager;
    @NonNull AuthenticationHolder authenticationHolder;
    @NonNull Provider<Department> currentDepartmentProvider;
    @NonNull TransactionTemplateFactory defaultTransactionTemplateFactory;
    @NonNull CoverFactory coverFactory;
    @NonNull ParentableDirectoryUpdater parentableDirectoryUpdater;
    @NonNull ParentableDirectoryCreator parentableDirectoryCreator;
    @NonNull SettingLoader settingLoader;
    @NonNull FilesystemService filesystemService;

    @Bean
    public FilterApiController filterApiController() {
        return new FilterApiController(filterLoader);
    }

    @Bean
    public FileApiController fileApiController() {
        return new FileApiController(
                identifiedFileLoader,
                fileSearchLoader,
                securedPrimaryCoverSavingLoadedIdentifiedFileSaver(),
                securedIdentifiedFileDeleter,
                defaultTransactionTemplateFactory.get(),
                chunkedFileUploadService()
        );
    }

    @Bean
    public DirectoryApiController directoryApiController() {
        return new DirectoryApiController(
                directoryLoader,
                parentableDirectoryDeleter,
                directoryInsightLoader,
                defaultFileAccessTypeProvider,
                parentableDirectoryUpdater,
                parentableDirectoryCreator
        );
    }

    @SneakyThrows
    @Bean
    public JavaTempFileService javaTempFileService() {
        // JavaTempFileService must always be @Bean because it cleans up the temp dir on exit!
        return new JavaTempFileService(Files.createTempDirectory("chunkedFiles"));
    }

    @Bean
    public ChunkedFileUploadService chunkedFileUploadService() {
        return new FallbackingChunkedFileUploadService(
                AndBooleanProvider.of(
                        settingLoader.getOnRootProvider(BLOB_DIR_ENABLED),
                        settingLoader.getOnRootProvider(PORTARO_BLOB_DIR_FILES_DIRECT_LOAD)
                ),
                new FSChunkedFileUploadService(
                        filesystemService,
                        defaultTransactionTemplateFactory.get(),
                        directoryLoader,
                        securedPrimaryCoverSavingLoadedIdentifiedFileSaver()
                ),
                new AppserverChunkedFileUploadService(
                        javaTempFileService(),
                        defaultTransactionTemplateFactory.get(),
                        directoryLoader,
                        securedPrimaryCoverSavingLoadedIdentifiedFileSaver()
                )
        );
    }

    @Bean
    public FileToViewableFileConverterFactory fileToViewableFileConverterFactory() {
        return new FileToViewableFileConverterFactory(securityManager, authenticationHolder, currentDepartmentProvider);
    }

    @Bean
    public FileDownloadController fileDownloadController() {
        return new FileDownloadController(
                securedFileDataStreamer,
                securedFileThumbnailDataStreamer,
                securedFileTextDataStreamer
        );
    }

    @Bean
    public RecordCoverApiController recordCoverApiController() {
        return new RecordCoverApiController(coverService, coverFactory);
    }

    @Bean
    public SearchFactory fileSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(SearchViewConstants.TYPE_FILE_SEARCH)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, IdentifiedFile, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_FILE),
                        CoreSearchParams.TYPE, SearchViewConstants.TYPE_FILE_SEARCH,
                        CoreSearchParams.INCLUDE_DELETED, false,
                        FileSearchParams.FILE_PROCESSING_STATE, FileProcessingState.allNotDeleted()
                ));
                return new PageSearchLoaderSearch<>(fileSearchLoader)
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public SearchFormFactory fileSearchTypeSearchFormFactory() {
        return new SearchFormFactory() {
            @Override
            public <PARAMS extends MapBackedParams> List<Form> createForms(@NonNull String searchType, @NonNull List<String> kinds, @NonNull List<String> subkinds, @NonNull PARAMS customParams, @NonNull Department ctx, @NonNull UserAuthentication currentAuth, @NonNull Locale locale) {
                List<Form> forms = new ArrayList<>();

                Form form = new Form("file-search", Texts.ofMessageCoded("hledani.kriteria"));
                form.addField(createWithProperty(CoreSearchParams.Q, Texts.ofMessageCoded("hledani.SearchFile"), TextValueEditor.getEmptyEditor().withMaxLength(40)
                        .withPlaceholder(Texts.ofMessageCoded("hledani.SearchFile"))));
                form.addField(createWithProperty(FileSearchParams.FILENAME, TextValueEditor.getEmptyEditor().withMaxLength(40)));
                form.addField(createWithProperty(FileSearchParams.MINIMAL_FILE_SIZE_KB, NumberValueEditor.getEmptyEditor().withPositiveOrZeroOnlyValidation()));
                forms.add(form);

                return forms;
            }
        };
    }

    @Bean
    public SearchFactory viewableFileSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(SearchViewConstants.TYPE_VIEWABLE_FILE_SEARCH)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, ViewableFile, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                MapBackedParams defaultDynamicParams = MapBackedParams.build(StaticParamsModifier.of(
                        CoreSearchParams.KIND, List.of(BasicMapSearchParams.KIND_FILE),
                        CoreSearchParams.TYPE, SearchViewConstants.TYPE_VIEWABLE_FILE_SEARCH,
                        CoreSearchParams.INCLUDE_DELETED, false,
                        FileSearchParams.FILE_PROCESSING_STATE, FileProcessingState.allNotDeleted()
                ));
                var converter = new ListToModifiedListExtendingConverter<>(fileToViewableFileConverterFactory().createConverter());
                return new PageSearchLoaderSearch<>(new ResultConvertingPageSearchLoader<>(fileSearchLoader, converter))
                        .withDefaultDynamicParams(defaultDynamicParams);
            }
        };
    }

    @Bean
    public CommandLineRunner fileSearchRegistrar() {
        return args -> {
            searchFactoryResolver
                    .withStandardOrder(fileSearchFactory())
                    .withStandardOrder(viewableFileSearchFactory());
            viewableItemsConverter.registerStandardNoop(IdentifiedFile.class);
            searchFormFactory.add(SearchViewConstants.TYPE_FILE_SEARCH, fileSearchTypeSearchFormFactory());
        };
    }

    @Bean
    public TypedAuthenticatedContextualObjectModifier<FileEditationRequest> fileEditationRequestDefaulter() {
        return new FileEditationRequest.FileEditationRequestDefaulter();
    }

    @Bean
    public Saver<IdentifiedFileSaveCommand, IdentifiedFile> securedPrimaryCoverSavingLoadedIdentifiedFileSaver() {
        return cmd -> {
            securityManager.throwIfCannot(FileSecurityActions.FILES_MANAGE, cmd.currentAuth(), cmd.ctx());
            return primaryCoverSavingLoadedIdentifiedFileSaver.save(cmd.file());
        };
    }

}
