package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

import java.util.Objects;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserPreferenceToEntityConverter implements Converter<UserPreference, UserPreferenceEntity> {

    @NonNull DatatypableStringConverter datatypableStringConverter;

    @Override
    public UserPreferenceEntity convert(@NonNull UserPreference userPreference) {
        return new UserPreferenceEntity(
                userPreference.getKeyId(),
                userPreference.getUser().getId(),
                datatypableStringConverter.convertToString(userPreference.getCustomValue()),
                Objects.requireNonNull(userPreference.getLastModified())
        );
    }
}
