package cz.kpsys.portaro.userpreferences;


import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import cz.kpsys.portaro.commons.cache.CacheCleaner;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.ItemNotFoundException;
import cz.kpsys.portaro.commons.util.ListUtil;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static cz.kpsys.portaro.databasestructure.UserPreferenceDb.USER_PREFS.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SpringDbUserPreferenceLoader implements UserPreferenceLoader, CacheCleaner {

    @NonNull NamedParameterJdbcOperations jdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull Codebook<UserPreferenceKey, Integer> userPreferenceKeyLoader;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull Cache<BasicUser, List<UserPreference>> valuesCache = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .maximumSize(200)
            .build();


    @Override
    public List<UserPreference> getAllByUser(BasicUser u) {
        List<UserPreference> cached = valuesCache.getIfPresent(u);
        if (cached != null) {
            return cached;
        }

        List<UserPreference> res;

        if (!u.isEvided()) {
            res = getAllAnonymous();
        } else {
            res = loadAllByUser(u);
        }

        valuesCache.put(u, res);

        return res;
    }

    private List<UserPreference> loadAllByUser(BasicUser u) {
        SelectQuery sq = queryFactory.newSelectQuery();
        sq.from(USER_PREFS);
        sq.where().and().eq(FK_UZIV, u.getId());
        List<UserPreference> allCustomized = jdbcTemplate.query(sq.getSql(), sq.getParamMap(), new NotAnonymousPreferenceMapper(u));

        List<UserPreference> res = getAllNotCustomized(u);
        for (UserPreference namedUP : allCustomized) {
            res = ListUtil.replaceByObjectId(res, namedUP, UserPreference::getKeyId);
        }
        return res;
    }

    @Override
    public UserPreference getByKeyIdAndUser(int keyId, BasicUser u) throws ItemNotFoundException {
        List<UserPreference> allByUser = getAllByUser(u);
        return ListUtil.findById(allByUser, DefaultUserPreference.createId(keyId, u))
                .orElseThrow(() -> new ItemNotFoundException(DefaultUserPreference.class, "key %s of user %s".formatted(keyId, u)));
    }

    public List<UserPreference> getAllAnonymous() {
        return ListUtil.convert(userPreferenceKeyLoader.getAll(), DefaultUserPreference::createAnonymous);
    }

    public List<UserPreference> getAllNotCustomized(final BasicUser u) {
        return ListUtil.convert(userPreferenceKeyLoader.getAll(), key -> DefaultUserPreference.createNotCustomized(key, u));
    }

    @Override
    public void clearCache() {
        valuesCache.invalidateAll();
    }

    @FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
    @RequiredArgsConstructor
    private class NotAnonymousPreferenceMapper implements RowMapper<UserPreference> {

        @NonNull BasicUser user;

        @Override
        public UserPreference mapRow(ResultSet rs, int rowNum) throws SQLException {
            UserPreferenceKey key = userPreferenceKeyLoader.getById(rs.getInt(FK_KEY));
            int userId = rs.getInt(FK_UZIV);
            String customValueString = rs.getString(VAL);

            Object customValue = datatypableStringConverter.convertFromString(customValueString, key.getDatatype());

            return new DefaultUserPreference(key, user, customValue, null);
        }
    }
}
