package cz.kpsys.portaro.userpreferences;


import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldDefaults;


import java.time.Instant;

import static cz.kpsys.portaro.databasestructure.UserPreferenceDb.USER_PREFS.*;

@Entity
@Table(name = USER_PREFS)
@IdClass(UserPreferenceId.class)
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor
@Getter
public class UserPreferenceEntity {

    @Id
    @Column(name = FK_KEY)
    @NonNull
    Integer keyId;

    @Id
    @Column(name = FK_UZIV)
    @NonNull
    Integer userId;

    @Column(name = VAL)
    @NonNull
    String value;

    @Column(name = EDIT_TIME)
    @NonNull
    Instant editTime;
}
