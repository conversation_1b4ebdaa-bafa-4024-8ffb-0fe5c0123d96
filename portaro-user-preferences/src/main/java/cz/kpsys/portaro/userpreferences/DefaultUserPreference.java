package cz.kpsys.portaro.userpreferences;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.NonFinal;

import java.time.Instant;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class DefaultUserPreference implements UserPreference {

    @NonNull UserPreferenceKey key;
    BasicUser user;

    @NonFinal
    Object customValue;

    @Nullable
    @NonFinal
    @JsonIgnore
    Instant lastModified;


    static UserPreference createAnonymous(UserPreferenceKey key) {
        return new DefaultUserPreference(key, null, null, null);
    }

    static UserPreference createNotCustomized(UserPreferenceKey key, BasicUser user) {
        return new DefaultUserPreference(key, user, null, null);
    }

    static String createId(UserPreferenceKey key, BasicUser u) {
        return createId(key.getId(), u);
    }

    static String createId(int keyId, BasicUser u) {
        return "user_preference_" + keyId + (u != null && u.isEvided() ? "_" + u.getId() : "");
    }

    @Override
    public String getId() {
        return createId(key, user);
    }

    @Override
    public boolean isCustomized() {
        return customValue != null;
    }

    @Override
    public void setToDefault() {
        this.customValue = null;
    }

    @Override
    public Object getEffectiveValue() {
        return isCustomized() ? customValue : getDefaultValue();
    }

    @Nullable
    @Override
    public Object getDefaultValue() {
        return key.getDefaultValue();
    }

    @Override
    public Object getCustomValue() {
        return customValue;
    }

    @Override
    public void setCustomValue(Object customValue) {
        this.customValue = customValue;
    }

    @Override
    public BasicUser getUser() {
        return user;
    }

    @NonNull
    @Override
    public UserPreferenceKey getKey() {
        return key;
    }

    @Override
    public int getKeyId() {
        return key.getId();
    }

    @Override
    public Text getKeyText() {
        return key.getText();
    }

    @Override
    public Datatype getDatatype() {
        return key.getDatatype();
    }

    @Nullable
    @Override
    @JsonIgnore
    public Instant getLastModified() {
        return lastModified;
    }

    @Override
    @JsonIgnore
    public void setLastModified(Instant lastModified) {
        this.lastModified = lastModified;
    }

    @Override
    public ValueEditor<?, ?, ?> getEditor() {
        return key.getEditor();
    }

}
