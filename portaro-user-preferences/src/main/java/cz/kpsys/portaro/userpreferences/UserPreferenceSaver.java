package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserPreferenceSaver implements Saver<UserPreference, UserPreference> {

    @NonNull Saver<UserPreference, UserPreference> delegateSaver;
    @NonNull Deleter<UserPreference> userPreferenceDeleter;

    @Override
    public @NonNull UserPreference save(@NonNull UserPreference userPreference) {
        if (!userPreference.isCustomized()) { //smazani nastaveni
            userPreferenceDeleter.delete(userPreference);
        } else {
            Assert.notNull(userPreference.getKey(), "Key of user preference is null");
            Assert.state(userPreference.getUser().isEvided(), "User preferences are only for evided users");
            delegateSaver.save(userPreference);
        }
        return userPreference;
    }
}
