package cz.kpsys.portaro.userpreferences;

import jakarta.persistence.Column;
import java.io.Serializable;

import static cz.kpsys.portaro.databasestructure.UserPreferenceDb.USER_PREFS.FK_KEY;
import static cz.kpsys.portaro.databasestructure.UserPreferenceDb.USER_PREFS.FK_UZIV;

public record UserPreferenceId(
        @Column(name = FK_KEY)
        Integer keyId,

        @Column(name = FK_UZIV)
        Integer userId
) implements Serializable {
}
