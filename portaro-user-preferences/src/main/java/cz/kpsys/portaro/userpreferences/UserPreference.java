package cz.kpsys.portaro.userpreferences;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.commons.object.Identified;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.form.editor.ValueEditorProvider;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;

public interface UserPreference extends Identified<String>, ValueEditorProvider {

    boolean isCustomized();

    void setToDefault();

    Object getEffectiveValue();

    @Nullable
    Object getDefaultValue();

    Object getCustomValue();

    void setCustomValue(Object customValue);

    BasicUser getUser();

    @NonNull
    UserPreferenceKey getKey();

    int getKeyId();

    Text getKeyText();

    Datatype getDatatype();

    @JsonIgnore
    @Nullable
    Instant getLastModified();

    @JsonIgnore
    void setLastModified(Instant lastModified);

}
