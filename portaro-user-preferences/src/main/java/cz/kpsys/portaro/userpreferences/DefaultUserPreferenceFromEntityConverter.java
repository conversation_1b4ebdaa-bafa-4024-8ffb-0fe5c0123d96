package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class DefaultUserPreferenceFromEntityConverter implements Converter<UserPreferenceEntity, UserPreference> {

    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull Codebook<UserPreferenceKey, Integer> userPreferenceKeyLoader;

    @Override
    public UserPreference convert(@NonNull UserPreferenceEntity entity) {
        return new DefaultUserPreference(
                userPreferenceKeyLoader.getById(entity.getKeyId()),
                basicUserLoader.getById(entity.getUserId()),
                entity.getValue(),
                entity.getEditTime()
        );
    }
}
