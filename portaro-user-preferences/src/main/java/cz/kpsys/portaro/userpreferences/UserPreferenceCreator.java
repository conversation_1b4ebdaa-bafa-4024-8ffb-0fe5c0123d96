package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.commons.object.repo.Codebook;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.datatype.DatatypableStringConverter;
import cz.kpsys.portaro.datatype.ListDatatype;
import cz.kpsys.portaro.datatype.ScalarDatatype;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

import java.time.Instant;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserPreferenceCreator {

    @NonNull Saver<UserPreference, UserPreference> userPreferenceSaver;
    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull Codebook<UserPreferenceKey, Integer> userPreferenceKeyLoader;
    @NonNull DatatypableStringConverter datatypableStringConverter;
    @NonNull UserPreferenceLoader userPreferenceLoader;

    public UserPreference create(@NonNull UserPreferenceCreationCommand command) {
        return readwriteTransactionTemplate.execute(_ -> {
            UserPreferenceKey key = userPreferenceKeyLoader.getById(command.keyId());
            Object value = datatypableStringConverter.convertFromSimpleTypePreservingStructure(command.value(), (ScalarDatatype) (key.getDatatype() instanceof ListDatatype ? ((ListDatatype) key.getDatatype()).getNestedDatatype() : key.getDatatype()));

            UserPreference userPreference = userPreferenceLoader.getByKeyIdAndUser(command.keyId(), command.user());
            if (value == null) {
                userPreference.setToDefault();
            } else {
                userPreference.setCustomValue(value);
            }
            userPreference.setLastModified(Instant.now());
            userPreferenceSaver.save(userPreference);
            return userPreference;
        });
    }
}
