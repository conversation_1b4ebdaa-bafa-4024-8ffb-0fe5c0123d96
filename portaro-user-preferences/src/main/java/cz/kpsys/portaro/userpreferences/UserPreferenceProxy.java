package cz.kpsys.portaro.userpreferences;

import com.fasterxml.jackson.annotation.JsonIgnore;
import cz.kpsys.portaro.commons.localization.Text;
import cz.kpsys.portaro.datatype.Datatype;
import cz.kpsys.portaro.form.valueeditor.ValueEditor;
import cz.kpsys.portaro.user.BasicUser;
import jakarta.annotation.Nullable;
import lombok.NonNull;

import java.time.Instant;

public class UserPreferenceProxy implements UserPreference {
    private final UserPreference userPreference;

    public UserPreferenceProxy(UserPreference userPreference) {
        this.userPreference = userPreference;
    }

    @JsonIgnore
    public UserPreference getUserPreference() {
        return userPreference;
    }

    @Override
    public String getId() {
        return userPreference.getId();
    }

    @Override
    public boolean isCustomized() {
        return userPreference.isCustomized();
    }

    @Override
    public void setToDefault() {
        userPreference.setToDefault();
    }

    @Override
    public Object getEffectiveValue() {
        return userPreference.getEffectiveValue();
    }

    @Nullable
    @Override
    public Object getDefaultValue() {
        return userPreference.getDefaultValue();
    }

    @Override
    public Object getCustomValue() {
        return userPreference.getCustomValue();
    }

    @Override
    public void setCustomValue(Object customValue) {
        userPreference.setCustomValue(customValue);
    }

    @Override
    public BasicUser getUser() {
        return userPreference.getUser();
    }

    @NonNull
    @Override
    public UserPreferenceKey getKey() {
        return userPreference.getKey();
    }

    @Override
    public int getKeyId() {
        return userPreference.getKeyId();
    }

    @Override
    public Text getKeyText() {
        return userPreference.getKeyText();
    }

    @Override
    public Datatype getDatatype() {
        return userPreference.getDatatype();
    }

    @Override
    public ValueEditor<?, ?, ?> getEditor() {
        return userPreference.getEditor();
    }

    @JsonIgnore
    @Nullable
    @Override
    public Instant getLastModified() {
        return userPreference.getLastModified();
    }

    @JsonIgnore
    @Override
    public void setLastModified(Instant lastModified) {
        userPreference.setLastModified(lastModified);
    }
}
