package cz.kpsys.portaro.userpreferences;

import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import lombok.experimental.FieldDefaults;
import org.springframework.util.Assert;

import java.time.Instant;
import java.util.List;
import java.util.Locale;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserPrefAccessor {

    public static final UserPreferenceId<List<Department>> SEARCH_BUILDINGS = new UserPreferenceId<>(1);
    public static final UserPreferenceId<Department> DEFAULT_BUILDING_TO_ORDER_ON = new UserPreferenceId<>(2);
    public static final UserPreferenceId<List<Department>> DEFAULT_BUILDINGS_TO_RESERVE_ON = new UserPreferenceId<>(3);
    public static final UserPreferenceId<List<Department>> ANONYMIZATION_TIME = new UserPreferenceId<>(4);
    public static final UserPreferenceId<Locale> LOCALE = new UserPreferenceId<>(5);
    public static final UserPreferenceId<Boolean> BULK_EMAILS_ALLOWED = new UserPreferenceId<>(6);
    public static final UserPreferenceId<Boolean> INTERNET_ACCESS = new UserPreferenceId<>(8);
    public static final UserPreferenceId<Boolean> SELFCHECK_ALLOWED = new UserPreferenceId<>(10);

    @NonNull UserPreferenceLoader userPreferenceLoader;
    @NonNull Saver<UserPreference, ?> userPreferenceSaver;
    @NonNull ContextualProvider<Department, @NonNull Locale> defaultLocaleProvider;


    public <E> E getEffectiveValue(UserPreferenceId<E> p, BasicUser user, Department ctx) {
        if (p == LOCALE) {
            return (E) defaultLocaleProvider.getOn(ctx);
        }
        UserPreference pref = userPreferenceLoader.getByKeyIdAndUser(p.id, user);
        Assert.notNull(pref, () -> "User preference key id " + p.id + " for user " + user + " does not exist");
        return (E) pref.getEffectiveValue();
    }


    public <E> void save(UserPreferenceId<E> p, User user, E value) {
        UserPreference pref = userPreferenceLoader.getByKeyIdAndUser(p.id, user);
        pref.setLastModified(Instant.now());
        pref.setCustomValue(value);
        userPreferenceSaver.save(pref);
    }


    @Value
    public static class UserPreferenceId<E> {
        @NonNull Integer id;
    }

}
