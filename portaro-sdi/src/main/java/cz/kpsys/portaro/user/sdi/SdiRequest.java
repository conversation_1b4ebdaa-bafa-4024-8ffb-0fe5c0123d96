package cz.kpsys.portaro.user.sdi;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import cz.kpsys.portaro.commons.object.BasicNamedLabeledIdentified;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.BasicUser;
import lombok.*;
import lombok.experimental.NonFinal;
import org.springframework.lang.Nullable;

import java.time.Instant;

@Value
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SdiRequest extends BasicNamedLabeledIdentified<Integer> {

    public static final int PROPERTY_QUERY_MAX_LENGTH = 50;

    @NonNull
    BasicUser user;

    @NonNull
    Department department;

    String query;

    @NonNull
    Periodicity periodicity;

    @NonNull
    Instant createDate;

    @Nullable
    Instant terminationDate;

    boolean active;

    boolean deletable;

    @JsonIgnore
    @NonFinal
    @NonNull
    Boolean exists;

    @JsonCreator
    public SdiRequest(Integer id, @NonNull String name, @NonNull BasicUser user, @NonNull Department department, @NonNull String query, @NonNull Periodicity periodicity, @NonNull Instant createDate, @Nullable Instant terminationDate, boolean active, boolean deletable) {
        super(id, name);
        this.user = user;
        this.department = department;
        this.query = query;
        this.createDate = createDate;
        this.periodicity = periodicity;
        this.terminationDate = terminationDate;
        this.active = active;
        this.deletable = deletable;
    }

    public SdiRequest(Integer id, @NonNull String name, @NonNull BasicUser user, @NonNull Department department, @NonNull String query, @NonNull Periodicity periodicity, @NonNull Instant createDate, @Nullable Instant terminationDate, boolean active, boolean deletable, @NonNull Boolean exists) {
        super(id, name);
        this.user = user;
        this.department = department;
        this.query = query;
        this.createDate = createDate;
        this.periodicity = periodicity;
        this.terminationDate = terminationDate;
        this.active = active;
        this.deletable = deletable;
        this.exists = exists;
    }

    @JsonIgnore
    public void setExists(@NonNull Boolean exists) {
        this.exists = exists;
    }
}
