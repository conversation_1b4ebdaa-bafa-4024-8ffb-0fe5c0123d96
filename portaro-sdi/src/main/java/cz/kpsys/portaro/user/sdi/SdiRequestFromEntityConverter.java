package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.core.convert.converter.Converter;

@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SdiRequestFromEntityConverter implements Converter<SdiRequestEntity, SdiRequest> {

    @NonNull ByIdLoadable<User, Integer> userLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;

    @Override
    public SdiRequest convert(@NonNull SdiRequestEntity source) {
        return new SdiRequest(
                source.getId(),
                source.getName(),
                userLoader.getById(source.getUserId()),
                departmentLoader.getById(source.getDepartmentId()),
                source.getQuery(),
                Periodicity.CODEBOOK.getById(source.getPeriodicityId()),
                source.getCreateDate(),
                source.getTerminationDate(),
                source.getActive(),
                source.getDeletable(),
                source.getExists()
        );
    }
}
