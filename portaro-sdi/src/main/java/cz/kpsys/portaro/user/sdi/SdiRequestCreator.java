package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.contextual.ContextualProvider;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.PagedSearchResult;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import java.time.Instant;

@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SdiRequestCreator {

    @NonNull Saver<SdiRequest, SdiRequest> sdiRequestSaver;
    @NonNull ContextualProvider<Department, @NonNull Integer> sdiRequestIdGenerator;
    @NonNull TransactionTemplate readwriteTransactionTemplate;
    @NonNull SecurityManager securityManager;
    @NonNull ByIdLoadable<User, Integer> userLoader;

    public SdiRequest create(@NonNull SdiRequestCreationCommand command,
                             @NonNull UserAuthentication currentAuth) {
        return readwriteTransactionTemplate.execute(_ -> {
            PagedSearchResult<?, ?> result = command.search().getLastResult();
            String usedRawQuery = result.usedRawQuery();

            SdiRequest sdiRequest = new SdiRequest(
                    sdiRequestIdGenerator.getOn(command.department()),
                    command.name(),
                    command.user(),
                    command.department(),
                    usedRawQuery,
                    command.periodicity(),
                    Instant.now(),
                    null,
                    command.active(),
                    command.deletable(),
                    true
            );

            securityManager.throwIfCannot(SdiSecurityActions.SDI_SAVE, currentAuth, command.department(), sdiRequest);

            Assert.notNull(sdiRequest.getUser(), "User cannot be null");
            Assert.state(sdiRequest.getUser().isEvided(), "User must be evided to save SDI request");
            Assert.hasLength(sdiRequest.getQuery(), "Query must not be empty");

            //check if user has any email
            User user = userLoader.getById(sdiRequest.getUser().getId());
            Assert.notNull(user.getEmail(), "User must have email to activate SDI");

            sdiRequestSaver.save(sdiRequest);
            return sdiRequest;
        });
    }

}
