package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.commons.object.Identified;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.time.Instant;

import static cz.kpsys.portaro.databasestructure.UserDb.SDI.*;

@Entity
@Table(name = OPAC_SDI)
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
@AllArgsConstructor
@Getter
@NoArgsConstructor
public class SdiRequestEntity implements Identified<Integer> {

    @Id
    @Column(name = ID_OPAC_SDI)
    @NonNull
    Integer id;

    @Column(name = FK_UZIV)
    @NonNull
    Integer userId;

    @Column(name = FK_PUJC)
    @NonNull
    Integer departmentId;

    @Column(name = NAZEV)
    @NonNull
    String name;

    @Column(name = QUERY)
    @NonNull
    String query;

    @Column(name = PERIODICITA)
    @NonNull
    Integer periodicityId;

    @Column(name = JE_AKTIV)
    @NonNull
    Boolean active;

    @Column(name = LZE_SMAZAT)
    @NonNull
    Boolean deletable;

    @Column(name = DAT_VYTVORENI)
    @NonNull
    Instant createDate;

    @Column(name = DAT_UKONCENI)
    @Nullable
    Instant terminationDate;

    @Column(name = JE_EXIST)
    @NonNull
    Boolean exists;
}
