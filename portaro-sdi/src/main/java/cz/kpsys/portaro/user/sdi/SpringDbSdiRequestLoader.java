package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.database.DbUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.sql.generator.QueryFactory;
import cz.kpsys.portaro.sql.generator.SelectQuery;
import cz.kpsys.portaro.user.BasicUser;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations;
import org.springframework.transaction.support.TransactionTemplate;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import static cz.kpsys.portaro.databasestructure.UserDb.SDI.*;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class SpringDbSdiRequestLoader implements SdiRequestLoader, RowMapper<SdiRequest> {

    @NonNull NamedParameterJdbcOperations notAutoCommittingJdbcTemplate;
    @NonNull QueryFactory queryFactory;
    @NonNull ByIdLoadable<BasicUser, Integer> basicUserLoader;
    @NonNull ByIdLoadable<Department, Integer> departmentLoader;
    @NonNull TransactionTemplate readonlyTransactionTemplate;

    @Override
    public SdiRequest getById(@NonNull Integer id) {
        return readonlyTransactionTemplate.execute(_ -> {
            SelectQuery sq = queryFactory.newSelectQuery();
            sq.from(OPAC_SDI);
            sq.where()
                    .eq(ID_OPAC_SDI, id)
                    .and()
                    .eq(JE_EXIST, true);
            return notAutoCommittingJdbcTemplate.queryForObject(sq.getSql(), sq.getParamMap(), this);
        });
    }

    @Override
    public List<SdiRequest> getAll() {
        return getAllByUser(null);
    }

    @Override
    public List<SdiRequest> getAllByUser(BasicUser u) {
        return readonlyTransactionTemplate.execute(_ -> {
            SelectQuery sq = queryFactory.newSelectQuery();
            sq.from(OPAC_SDI);
            if (u != null) {
                sq.where().eq(FK_UZIV, u.getId());
            }
            sq.where().and().eq(JE_EXIST, true);
            return notAutoCommittingJdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
        });
    }

    @Override
    public List<SdiRequest> getAllActive() {
        return readonlyTransactionTemplate.execute(_ -> {
            SelectQuery sq = queryFactory.newSelectQuery();
            sq.from(OPAC_SDI);
            sq.where()
                    .eq(JE_EXIST, true)
                    .and()
                    .eq(JE_AKTIV, true)
                    .and()
                    .brackets().gtEq(DAT_UKONCENI, new Date()).or().isNull(DAT_UKONCENI);
            return notAutoCommittingJdbcTemplate.query(sq.getSql(), sq.getParamMap(), this);
        });
    }

    @Override
    public SdiRequest mapRow(ResultSet rs, int rowNum) throws SQLException {
        Integer id = DbUtils.getInteger(rs, ID_OPAC_SDI);
        String name = rs.getString(NAZEV);
        String query = rs.getString(QUERY);
        Periodicity periodicity = Periodicity.CODEBOOK.getById(rs.getInt(PERIODICITA));
        boolean active = rs.getBoolean(JE_AKTIV);
        Instant createDate = DbUtils.instantNotNull(rs, DAT_VYTVORENI);
        Instant terminationDate = DbUtils.instantOrNull(rs, DAT_UKONCENI);
        int userId = rs.getInt(FK_UZIV);
        int departmentId = rs.getInt(FK_PUJC);
        boolean deletable = rs.getBoolean(LZE_SMAZAT);
        BasicUser u = basicUserLoader.getById(userId);
        Department department = departmentLoader.getById(departmentId);
        Boolean exists = DbUtils.getBoolean(rs, JE_EXIST);

        return new SdiRequest(id, name, u, department, query, periodicity, createDate, terminationDate, active, deletable, exists);
    }
}
