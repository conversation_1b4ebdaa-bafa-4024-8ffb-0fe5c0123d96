package cz.kpsys.portaro.user.sdi;

import lombok.NonNull;
import org.springframework.core.convert.converter.Converter;

public class SdiRequestToEntityConverter implements Converter<SdiRequest, SdiRequestEntity> {

    @Override
    public SdiRequestEntity convert(@NonNull SdiRequest source) {
        return new SdiRequestEntity(
                source.getId(),
                source.getUser().getId(),
                source.getDepartment().getId(),
                source.getName(),
                source.getQuery(),
                source.getPeriodicity().getId(),
                source.isActive(),
                source.isDeletable(),
                source.getCreateDate(),
                source.getTerminationDate(),
                source.getExists()
        );
    }
}
