package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.localization.Texts;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.conversation.ActionResponse;
import cz.kpsys.portaro.conversation.FinishedActionResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.formannotation.annotations.form.ValidFormObject;
import cz.kpsys.portaro.localization.LocalizationCodes;
import cz.kpsys.portaro.security.SecurityManager;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.web.GenericApiController;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class SdiApiController extends GenericApiController {

    public static final String SDI_REQUESTS_PATH = "/api/sdi-requests";
    public static final String SDI_REQUEST_CREATE_PATH = SDI_REQUESTS_PATH + "/create";
    public static final String SDI_REQUEST_EDIT_PATH = SDI_REQUESTS_PATH + "/edit";
    public static final String SDI_SENDINGS_PATH = "/api/sdi-sendings";
    public static final String SDI_PATH = "/api/sdi";
    public static final String SDI_SEND_PATH = SDI_PATH + "/send";
    public static final String SDI_RESEND_PATH = SDI_PATH + "/resend";

    @NonNull SdiService sdiService;
    @NonNull SdiRequestLoader sdiRequestLoader;
    @NonNull Deleter<SdiRequest> sdiRequestDeleter;
    @NonNull SdiSendingLoader sdiSendingLoader;
    @NonNull SecurityManager securityManager;
    @NonNull SdiRequestCreator sdiRequestCreator;
    @NonNull SdiRequestUpdater sdiRequestUpdater;


    @GetMapping(SDI_REQUESTS_PATH)
    public List<SdiRequest> query(@RequestParam(value = "user", required = false) User user,
                                  @CurrentDepartment Department currentDepartment,
                                  UserAuthentication currentAuth) {
        if (user == null) {
            securityManager.throwIfCannot(SdiSecurityActions.ALL_SDI_REQUESTS_SHOW, currentAuth, currentDepartment, null);
            return sdiRequestLoader.getAll();
        }
        securityManager.throwIfCannot(SdiSecurityActions.USER_SDI_REQUESTS_SHOW, currentAuth, currentDepartment, user);
        return sdiRequestLoader.getAllByUser(user);
    }

    @PostMapping(SDI_REQUEST_CREATE_PATH)
    public ActionResponse create(@RequestBody @ValidFormObject SdiRequestCreationRequest request,
                                 @CurrentDepartment Department currentDepartment,
                                 UserAuthentication currentAuth) {
        sdiRequestCreator.create(SdiRequestCreationCommand.ofNew(request.getUser(),
                                                                currentDepartment,
                                                                request.getName(),
                                                                request.getSearch(),
                                                                request.getPeriodicity(),
                                                                request.isActive(),
                                                                request.isDeletable()), currentAuth);

        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Saved));
    }

    @PostMapping(SDI_REQUEST_EDIT_PATH)
    public ActionResponse edit(@RequestBody @ValidFormObject SdiRequestEditRequest request,
                               @CurrentDepartment Department ctx,
                               UserAuthentication currentAuth) {
        sdiRequestUpdater.update(new SdiRequestEditationCommand(request.getId(), ctx, request.isActive()), currentAuth);

        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Saved));
    }


    @DeleteMapping(SDI_REQUESTS_PATH + "/{id}")
    public ActionResponse delete(@PathVariable("id") int sdiRequestId) {
        SdiRequest r = sdiRequestLoader.getById(sdiRequestId);
        sdiRequestDeleter.delete(r);
        return new FinishedActionResponse(Texts.ofMessageCoded(LocalizationCodes.Deleted));
    }


    @GetMapping(SDI_SENDINGS_PATH)
    public List<SdiSending> getAllSendingsByRequest(@RequestParam("request") int sdiRequestId) {
        SdiRequest r = sdiRequestLoader.getById(sdiRequestId);
        return sdiSendingLoader.getAllByRequest(r);
    }


    @GetMapping(SDI_SEND_PATH)
    public void send(@RequestParam(value = "request", required = false) @Nullable Integer sdiRequestId,
                     @RequestParam(value = "force", defaultValue = "false") boolean force,
                     UserAuthentication currentAuth) {
        if (sdiRequestId != null) {
            SdiRequest r = sdiRequestLoader.getById(sdiRequestId);
            if (force) {
                sdiService.forceSend(r, currentAuth);
            } else {
                sdiService.sendIfSendable(r, currentAuth);
            }
        } else {
            if (force) {
                throw new IllegalStateException("Parameter 'force=true' is usable only in combination with request");
            }
            sdiService.sendAllSendable(currentAuth);
        }
    }


    @GetMapping(SDI_RESEND_PATH)
    public void resend(@RequestParam("sending") int sdiSendingId,
                       UserAuthentication currentAuth) {
        SdiSending sdiSending = sdiSendingLoader.getById(sdiSendingId);
        sdiService.resend(sdiSending, currentAuth);
    }

}