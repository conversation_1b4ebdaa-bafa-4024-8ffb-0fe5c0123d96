package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.Saver;
import cz.kpsys.portaro.security.SecurityManager;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import org.springframework.transaction.support.TransactionTemplate;

@FieldDefaults(level = lombok.AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class SdiRequestUpdater {

    @NonNull SdiRequestLoader sdiRequestLoader;
    @NonNull Saver<SdiRequest, SdiRequest> sdiRequestSaver;
    @NonNull SecurityManager securityManager;
    @NonNull TransactionTemplate readwriteTransactionTemplate;

    public SdiRequest update(@NonNull SdiRequestEditationCommand command, @NonNull UserAuthentication currentAuth) {
        return readwriteTransactionTemplate.execute(_ -> {
            SdiRequest sdiRequest = sdiRequestLoader.getById(command.id());

            sdiRequest = new SdiRequest(sdiRequest.getId(),
                    sdiRequest.getName(),
                    sdiRequest.getUser(),
                    sdiRequest.getDepartment(),
                    sdiRequest.getQuery(),
                    sdiRequest.getPeriodicity(),
                    sdiRequest.getCreateDate(),
                    sdiRequest.getTerminationDate(),
                    command.active(),
                    sdiRequest.isDeletable(),
                    true
            );

            securityManager.throwIfCannot(SdiSecurityActions.SDI_SAVE, currentAuth, command.department(), sdiRequest);
            sdiRequestSaver.save(sdiRequest);

            return sdiRequest;
        });
    }
}
