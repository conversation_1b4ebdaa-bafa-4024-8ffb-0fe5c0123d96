package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.search.MapBackedParams;
import cz.kpsys.portaro.search.Paging;
import cz.kpsys.portaro.search.Search;
import cz.kpsys.portaro.user.BasicUser;
import lombok.NonNull;

public record SdiRequestCreationCommand(
        @NonNull BasicUser user,
        @NonNull Department department,
        @NonNull String name,
        @NonNull Search<MapBackedParams, ?, Paging> search,
        @NonNull Periodicity periodicity,
        @NonNull Boolean active,
        @NonNull Boolean deletable
) {
    public static SdiRequestCreationCommand ofNew(
            @NonNull BasicUser user,
            @NonNull Department department,
            @NonNull String name,
            @NonNull Search<MapBackedParams, ?, Paging> search,
            @NonNull Periodicity periodicity,
            boolean active,
            boolean deletable
    ) {
        return new SdiRequestCreationCommand(user, department, name, search, periodicity, active, deletable);
    }
}
