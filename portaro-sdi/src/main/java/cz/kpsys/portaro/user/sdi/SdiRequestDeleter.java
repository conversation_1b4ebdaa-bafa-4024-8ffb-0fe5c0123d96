package cz.kpsys.portaro.user.sdi;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.commons.object.repo.Saver;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = lombok.AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public class SdiRequestDeleter implements Deleter<SdiRequest> {

    @NonNull Saver<SdiRequest, SdiRequest> saver;

    @Override
    public void delete(@NonNull SdiRequest sdiRequest) {
        sdiRequest.setExists(false);
        saver.save(sdiRequest);
    }
}
