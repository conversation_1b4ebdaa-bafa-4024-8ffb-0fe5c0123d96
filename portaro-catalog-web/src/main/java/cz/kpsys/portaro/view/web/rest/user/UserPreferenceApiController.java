package cz.kpsys.portaro.view.web.rest.user;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.repo.Deleter;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.user.User;
import cz.kpsys.portaro.user.sec.SecurityActions;
import cz.kpsys.portaro.userpreferences.*;
import cz.kpsys.portaro.web.GenericApiController;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Hidden
@RequestMapping("/api/user-preferences")
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class UserPreferenceApiController extends GenericApiController {

    @NonNull UserPreferenceLoader userPreferenceLoader;
    @NonNull Deleter<UserPreference> userPreferenceDeleter;
    @NonNull UserPreferenceCreator userPreferenceCreator;

    @GetMapping
    public List<? extends UserPreference> query(@RequestParam("user") User user,
                                                @RequestParam(value = "withAutoGenerated", defaultValue = "false") final boolean withAutoGenerated,
                                                @RequestParam(value = "preferenceKeys", defaultValue = "") List<Integer> preferenceKeys,
                                                @CurrentDepartment Department currentDepartment,
                                                UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_PREFERENCES_EDIT, currentAuth, currentDepartment, user);

        return userPreferenceLoader.getAllByUser(user).stream()
                .map(preference -> ViewableUserPreference.createFromUserPreference(preference, currentAuth.getActiveUser()))
                .filter(viewableUserPreference -> viewableUserPreference.isViewable() && (withAutoGenerated || !viewableUserPreference.getKey().isAutoGenerated()))
                .filter(viewableUserPreference -> preferenceKeys.isEmpty() || preferenceKeys.contains(viewableUserPreference.getKey().getId()))
                .toList();
    }

    @DeleteMapping
    public void delete(@RequestParam("key") int keyId,
                       @RequestParam("user") User user,
                       @CurrentDepartment Department currentDepartment,
                       UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_PREFERENCES_EDIT, currentAuth, currentDepartment, user);
        UserPreference up = userPreferenceLoader.getByKeyIdAndUser(keyId, user);
        userPreferenceDeleter.delete(up);
    }

    @PostMapping
    public UserPreference save(@RequestBody UserPreferenceRequest request,
                               @CurrentDepartment Department currentDepartment,
                               UserAuthentication currentAuth) {
        securityManager.throwIfCannot(SecurityActions.USER_PREFERENCES_EDIT, currentAuth, currentDepartment, request.user());
        UserPreference userPreference = userPreferenceCreator.create(new UserPreferenceCreationCommand(request.keyId(), request.user(), request.value()));
        return ViewableUserPreference.createFromUserPreference(userPreference, currentAuth.getActiveUser());
    }
}