package cz.kpsys.portaro.view.web.rest.record;

import cz.kpsys.portaro.app.CatalogWebConstants;
import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.LabeledIdRecord;
import cz.kpsys.portaro.conversation.FinishedSaveResponse;
import cz.kpsys.portaro.department.CurrentDepartment;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.record.creation.CreateRecordsFromFilesRequest;
import cz.kpsys.portaro.record.creation.RecordsFromFilesCreatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;
import java.util.UUID;

@Tag(name = "record-extras", description = "Endpoints for managing records")
@RequestMapping(CatalogWebConstants.API_URL_PREFIX + CatalogWebConstants.RECORDS_URL_PART)
@ResponseBody
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class RecordExtrasController {

    @NonNull RecordsFromFilesCreatorService recordsFromFilesCreatorService;


    @Operation(summary = "Create new records based on uploaded files")
    @PostMapping(path = "/create-from-files", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public FinishedSaveResponse<List<LabeledIdRecord<UUID>>> createFromFiles(
            @Valid CreateRecordsFromFilesRequest request,
            UserAuthentication currentAuth,
            @CurrentDepartment Department ctx) {

        var briefRecordInfos = recordsFromFilesCreatorService.createRecords(
                request.toCommand(ctx, currentAuth));
        return FinishedSaveResponse.saved(briefRecordInfos);
    }

}
