package cz.kpsys.portaro.databasestructure;

@SuppressWarnings("TypeName")
public class RecordDb {

    public static final String COMMON_KINDED_NAME = "kinded_name";

    public static class RECORD {
        public static final String TABLE = "record";
        public static final String ID = "id";
        public static final String CREATION_EVENT_ID = "creation_event_id";
        public static final String ACTIVATION_EVENT_ID = "activation_event_id";
        public static final String DELETION_EVENT_ID = "deletion_event_id";
        public static final String FOND_ID = "fk_fond";
        public static final String RECORD_STATUS_ID = "record_status_id";
        public static final String SORTING_KEY = "sorting_key";
        public static final String MASTER_RECORD_ID = "master_record_id";
        public static final String DIRECTORY_ID = "directory_id";
        public static final String PRIMARY_FILE_ID = "primary_file_id";
        public static final String FIELDS_FULLY_CACHED = "fields_fully_cached";
        public static final String NAME = "name";
    }

    public static class RECORD_HOLDING {
        public static final String TABLE = "record_holding";
        public static final String ID = "id";
        public static final String RECORD_ID = "record_id";
        public static final String DEPARTMENT_ID = "department_id";
        public static final String CREATION_EVENT_ID = "creation_event_id";
        public static final String DISCARDION_EVENT_ID = "discardion_event_id";
        public static final String DELETION_EVENT_ID = "deletion_event_id";
    }

    public static class RECORD_HOLDING_SOURCE {
        public static final String TABLE = "record_holding_source";
        public static final String ID = "id";
        public static final String RECORD_HOLDING_ID = "record_holding_id";
        public static final String TYPE_ID = "type_id";
        public static final String CREATION_EVENT_ID = "creation_event_id";
    }

    public static class RECORD_STATUS {
        public static final String TABLE = "record_status";
        public static final String ID = "id";
        public static final String NAME = "name";
        public static final String ORIGINAL_ID = "original_id";
        public static final String FOR_DOCUMENT = "for_document";
        public static final String FOR_AUTHORITY = "for_authority";
    }

    public static class RECORD_KEY {
        public static final String TABLE = "record_key";
        public static final String ID = "id";
        public static final String RECORD_ID = "record_id";
        public static final String NAME = "name";
        public static final String VAL = "val";
        public static final String VAL_WIN1250 = "val_win1250";
    }

    public static class RECORD_FIELD {
        public static final String TABLE = "record_field";
        public static final String ID = "id";
        public static final String RECORD_ID = "record_id";
        public static final String TARGET_RECORD_ID = "target_record_id";
        public static final String CACHE_VALID = "cache_valid";
        public static final String FIELD_CODE = "field_code";
        public static final String FIELD_INDEX = "field_index";
        public static final String TEXT_VALUE = "text_value";
        public static final String SUBFIELD_CODE = "subfield_code";
        public static final String SUBFIELD_INDEX = "subfield_index";
        public static final String LAST_MODIFICATION_DATE = "last_modification_date";
        public static final String LAST_PROCESS_DATE = "last_process_date";
        public static final String TEXT_SUFFIX = "text_suffix";
        public static final String LARGE_TEXT_VALUE = "large_text_value";
        public static final String NUMERIC_VALUE = "numeric_value";
        public static final String DATE_VALUE = "date_value";
        public static final String DATETIME_VALUE = "datetime_value";
        public static final String BOOLEAN_VALUE = "boolean_value";
    }

    public static class RECORD_KEYWORD_RELATION {
        public static final String TABLE = "record_keyword_relation";
    }

    public static class SYNCHRONIZATION_CNB {
        public static final String TABLE = "synchronization_cnb";
    }

    public static class RECORD_KEYWORD {
        public static final String TABLE = "record_keyword";
    }

    public static class RECORD_LINK {
        public static final String TABLE = "record_link";
    }

    public static class KATX_1 {
        public static final String ID = "id";
        public static final String CIS_POL = "cis_pol";
        public static final String PORADI_POLE = "poradi_pole";
        public static final String INDIK = "indik";
        public static final String OBSAH = "obsah";
        public static final String DATCAS = "datcas";
        public static final String FK_UZIV = "fk_uziv";
        public static final String RECORD_ID = "record_id";
    }

    public static class KATX_1_COMBINED extends KATX_1 {
        public static final String FIELD_NUMBER_PREFIX = "field_number_prefix";
    }

    public static class KAT1_1 extends KATX_1 {
        public static final String TABLE = "kat1_1";
        public static final String FK_ZAZ = "fk_zaz";
    }

    public static class KATAUT_1 extends KATX_1 {
        public static final String TABLE = "kataut_1";
        public static final String FK_AUT = "fk_aut";
    }

    public static class KATAUT_2 {
        public static final String TABLE = "kataut_2";
    }

    public static class KATAUT_3 {
        public static final String TABLE = "kataut_3";
    }

    public static class KAT1_3 {
        public static final String TABLE = "kat1_3";
        public static final String FK_ZAZ = "fk_zaz";
    }

    public static class KAT1_2 {
        public static final String TABLE = "kat1_2";
    }

    public static class KATAUT_4 {
        public static final String TABLE = "kataut_4";
        public static final String RECORD_ID = "record_id";
        public static final String ID_AUT = "id_aut";
        public static final String FK_AUT = "fk_aut";
        @Deprecated
        public static final String TRIDNAZ = "tridnaz";
        public static final String STATUS4 = "status4";
        @Deprecated
        public static final String CIS_NAR_AUT = "cis_nar_aut";
        public static final String NAZEV = "nazev";
        public static final String FK_AUTFOND = "fk_autfond";
        public static final String FK_FULLTEXT_SKUPINY = "fk_fulltext_skupiny";
        public static final String FK_FULLTEXT_IMAGE = "fk_fulltext_image";
        public static final String FK_UZIV_KORP = "fk_uziv_korp";
        public static final String DATCAS = "datcas";
        public static final String FK_UZIV = "fk_uziv";
    }

    public static class KAT1_4 {
        public static final String TABLE = "kat1_4";
        public static final String ID_ZAZ = "id_zaz";
        public static final String FK_ZAZ = "fk_zaz";
        public static final String RECORD_ID = "record_id";
        public static final String FK_DOKFOND = "fk_dokfond";
        @Deprecated
        public static final String STATUS4 = "status4";
        public static final String NAZEV = "nazev";
        public static final String TRIDNAZ = "tridnaz";
        @Deprecated
        public static final String AUTOR = "autor";
        @Deprecated
        public static final String NAKL = "nakl";
        @Deprecated
        public static final String ISBN = "isbn";
        @Deprecated
        public static final String ISBN_CORE = "isbn_core";
        @Deprecated
        public static final String ROK_OD = "rok_od";
        @Deprecated
        public static final String ROK_DO = "rok_do";
        public static final String FK_FULLTEXT_SKUPINY = "fk_fulltext_skupiny";
        public static final String FK_FULLTEXT_IMAGE = "fk_fulltext_image";
        public static final String DATCAS = "datcas";
        public static final String FK_UZIV = "fk_uziv";
    }

    public static class KAT1_7 {
        public static final String KAT1_7 = "kat1_7";
        public static final String CIS_FOND = "cis_fond";
        public static final String CIS_POL = "cis_pol";
        public static final String SOURCE_RECORD_ID = "source_record_id";
        public static final String TARGET_RECORD_ID = "target_record_id";
    }

    public static class SPROC_AUT_STROM {
        public static final String SPROC_AUT_STROM = "sproc_aut_strom_uuid";
        public static final String RECORD_ID = "record_id";
        public static final String NAZEV = "nazev";
        public static final String UROVEN = "uroven";
        public static final String TYP_VAZBY = "typ_vazby";
    }

    public static class AUT_VAZBY {
        public static final String AUT_VAZBY = "aut_vazby";
        public static final String TYP_NADR = "typ_nadr";
        public static final String FK_AUTPOD = "fk_autpod";
        public static final String FK_AUTNAD = "fk_autnad";
        public static final String PORADI = "poradi";
        public static final int TYP_NADR_PARENT_CHILD = 0;
        public static final int TYP_NADR_SEE_ALSO = 1;
    }

    public static class OPAC_HODNOCENI {
        public static final String OPAC_HODNOCENI = "opac_hodnoceni";
        public static final String ID_OPAC_HODNOCENI = "id_opac_hodnoceni";
        public static final String RECORD_ID = "record_id";
        public static final String POCET = "pocet";
        public static final String HODNOTA = "hodnota";
        public static final String NAPOSLEDY = "naposledy";
    }

    public static class OPAC_RATING {
        public static final String OPAC_RATING = "opac_rating";
        public static final String COUNTER = "counter";
        public static final String RECORD_ID = "record_id";
        public static final String DATUM = "datum";
    }

    public static class OPAC_KOMENTARE {
        public static final String ID_OPAC_KOMENTARE = "id_opac_komentare";
        public static final String OPAC_KOMENTARE = "opac_komentare";
        public static final String RECORD_ID = "record_id";
        public static final String CAS = "cas";
        public static final String CAS_SMAZ = "datcas_smaz";
        public static final String FK_UZIV = "fk_uziv";
        public static final String TEXT = "text";
    }

    public static class VIEW_NEWS {
        public static final String RECORD_ID = "record_id";
        public static final String DEPARTMENT_ID = "department_id";
        public static final String EXEMPLAR_STATUS_ID = "exemplar_status_id";
        public static final String PUBLISH_DATE = "publish_date";
    }

    public static class OBALKYKNIH {
        public static final String TABLE = "obalkyknih";
        public static final String RECORD_ID = "record_id";
        public static final String FK_FULLTEXT = "fk_fulltext";
        public static final String URL_BACKLINK = "url_backlink";
        public static final String CAS_HLEDANI = "cas_hledani";
        public static final String VYSLEDEK = "vysledek";
        public static final String CITACE = "citace";
        public static final String URL_COVER = "url_cover";
        public static final String URL_TOC_PDF = "url_toc_pdf";
        public static final String TOC = "toc";
        public static final String BOOK_ID = "book_id";
    }

    public static class FDEF_ROOT {
        public static final String ID = "id";
        public static final String PODPOLE = "podpole";
        public static final String CIS_POL = "cis_pol";
        public static final String NAZEV = "nazev";
        public static final String NENIOPAK = "neniopak";
        public static final String AUTTYP = "auttyp";
        public static final String PODPEXT = "podpext";
        public static final String FONDEXT = "fondext";
        public static final String PIC = "pic";
        public static final String PRENASET = "prenaset";
        public static final String EXPORT_CIS_POLE = "cis_tag";
        public static final String EXPORT_KOD_PODPOLE = "pod_tag";
        public static final String FK_FRAZESKUP = "fk_frazeskup";
    }

    public static class FDEF {
        public static final String FDEF = "fdef";
        public static final String ID_FDEF = "id_fdef";
    }

    public static class FDEFAUT {
        public static final String FDEFAUT = "fdefaut";
        public static final String ID_FDEFAUT = "id_fdefaut";
    }

    public static class STYLY {
        public static final String TABLE = "styly";
        public static final String ID_STYL = "id_styl";
        public static final String PORADI_POLE = "poradi_pole";
        public static final String PORADI_PODP = "poradi_podp";
        public static final String ZPUS_ZOBR = "zpus_zobr";
        public static final String CIS_POL = "cis_pol";
        public static final String PODPOLE = "podpole";
        public static final String REQUIREMENT = "requirement";
    }

    public static class DEF_INDIK_ROOT {
        public static final String I1 = "i1";
        public static final String I2 = "i2";
        public static final String CIS_POL = "cis_pol";
        public static final String NAZEV = "nazev";
        public static final String JE_DEFAULT = "je_default";
    }

    public static class DEF_DOKINDIK extends DEF_INDIK_ROOT {
        public static final String DEF_DOKINDIK = "def_dokindik";
    }

    public static class DEF_AUTINDIK {
        public static final String DEF_AUTINDIK = "def_autindik";
    }

    public static class DEF_POPIS007 {
        public static final String TABLE = "def_popis007";
        public static final String ID = "id";
        public static final String PORADI = "poradi";
        public static final String KOD = "kod";
        public static final String POPIS = "popis";
    }

    public static class DEF_KODY007 {
        public static final String TABLE = "def_kody007";
        public static final String ID = "id";
        public static final String TYPDOK = "typdok";
        public static final String POZICE = "pozice";
        public static final String KOD = "kod";
        public static final String POPIS = "popis";
        public static final String JE_DEFAULT = "je_default";
    }

    public static class DEF_TYPDOK007 {
        public static final String TABLE = "def_typdok007";
        public static final String ID = "id";
        public static final String PORADI = "poradi";
        public static final String KOD = "kod";
        public static final String POPIS = "popis";
    }

    public static class DEF_KODY008 {
        public static final String TABLE = "def_kody008";
        public static final String ID = "id";
        public static final String TYPDOK = "typdok";
        public static final String POZICE = "pozice";
        public static final String KOD = "kod";
        public static final String POPIS = "popis";
        public static final String JE_DEFAULT = "je_default";
    }

    public static class ZAZNAM_HISTORIE {
        public static final String TABLE = "zaznam_historie";
        public static final String CIS_ZAZ = "cis_zaz";
        public static final String DATCAS = "datcas";
        public static final String FK_UZIV = "fk_uziv";
        public static final String PROGRAM = "program";
        public static final String OBSAH = "obsah";
    }
}
