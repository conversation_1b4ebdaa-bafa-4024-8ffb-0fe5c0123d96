rootProject.name = "portaro"
include("portaro-acme")
include("portaro-acquisition")
include("portaro-appserver")
include("portaro-auth")
include("portaro-auth-anonymous")
include("portaro-auth-bankid")
include("portaro-auth-bankid-notify")
include("portaro-auth-cas")
include("portaro-auth-credreg")
include("portaro-auth-ezak")
include("portaro-auth-ezak-config")
include("portaro-auth-httpbasic")
include("portaro-auth-internal")
include("portaro-auth-jwt")
include("portaro-auth-ldap")
include("portaro-auth-logout")
include("portaro-auth-mojeid")
include("portaro-auth-multifactor")
include("portaro-auth-oauth2-authorizationcode")
include("portaro-auth-oauth2-authorizationserver")
include("portaro-auth-oauth2-commons")
include("portaro-auth-oauth2-client")
include("portaro-auth-pairing")
include("portaro-auth-pairing-config")
include("portaro-auth-saml2-idp")
include("portaro-auth-saml2-sp")
include("portaro-auth-sidechannel")
include("portaro-auth-stringpair")
include("portaro-auth-switchuser")
include("portaro-auth-token")
include("portaro-auth-token-config")
include("portaro-auth-useragent")
include("portaro-batch")
include("portaro-calendar")
include("portaro-calendar-config")
include("portaro-catalog")
include("portaro-catalog-web")
include("portaro-comment")
include("portaro-commons")
include("portaro-commons-containers")
include("portaro-commons-crypto")
include("portaro-commons-image")
include("portaro-commons-pdf")
include("portaro-core")
include("portaro-core-template-velocity")
include("portaro-database-backup")
include("portaro-database-datacopy")
include("portaro-database-properties")
include("portaro-database-structure")
include("portaro-erp")
include("portaro-exemplar")
include("portaro-exemplar-import")
include("portaro-exemplar-export")
include("portaro-export")
include("portaro-ext-alive")
include("portaro-ext-bakalari")
include("portaro-ext-cpk")
include("portaro-ext-cpk-impl")
include("portaro-ext-edookit")
include("portaro-ext-edupage")
include("portaro-ext-ifis")
include("portaro-ext-obalkyknih")
include("portaro-ext-powerkey")
include("portaro-ext-report-server")
include("portaro-ext-sol")
include("portaro-ext-sutin")
include("portaro-ext-synchronizer")
include("portaro-ext-unis")
include("portaro-ext-unob")
include("portaro-ext-ziskej")
include("portaro-ext-ziskej-impl")
include("portaro-file")
include("portaro-file-config")
include("portaro-file-image")
include("portaro-file-text")
include("portaro-file-text-config")
include("portaro-finance")
include("portaro-finance-config")
include("portaro-form")
include("portaro-form-annotation")
include("portaro-form-config")
include("portaro-grid")
include("portaro-integ-feign")
include("portaro-inventory")
include("portaro-licence")
include("portaro-licence-updater")
include("portaro-loan")
include("portaro-loan-config")
include("portaro-loan-ill")
include("portaro-loan-mail")
include("portaro-message")
include("portaro-marcxml")
include("portaro-ncip")
include("portaro-ncip-impl")
include("portaro-oai-provider")
include("portaro-oai-provider-impl")
include("portaro-payment")
include("portaro-payment-provider")
include("portaro-payment-provider-csobgw")
include("portaro-payment-provider-gopay")
include("portaro-payment-provider-gpwebpay")
include("portaro-payment-provider-manual")
include("portaro-pops")
include("portaro-proveniences-map")
include("portaro-record")
include("portaro-record-binding")
include("portaro-record-citation")
include("portaro-record-collection")
include("portaro-record-config")
include("portaro-record-export")
include("portaro-record-grid")
include("portaro-record-grid-config")
include("portaro-record-import")
include("portaro-resources-update")
include("portaro-runner")
include("portaro-sba")
include("portaro-sdi")
include("portaro-search")
include("portaro-search-factory")
include("portaro-search-ui")
include("portaro-search-z")
include("portaro-security")
include("portaro-sip2-client")
include("portaro-sip2-commons")
include("portaro-sip2-server")
include("portaro-sip2-server-impl")
include("portaro-soap")
include("portaro-sql-generator")
include("portaro-tcp-client")
include("portaro-test-environment")
include("portaro-test-e2e")
include("portaro-test-integration")
include("portaro-thumbnail")
include("portaro-thumbnail-config")
include("portaro-user")
include("portaro-user-impl")
include("portaro-user-config")
include("portaro-user-preferences")
include("portaro-util")
include("portaro-verbisbox")
include("portaro-verbisboxer-manager")
include("portaro-web")
include("portaro-web-proxy")

System.setProperty("buildEnv", System.getProperty("buildEnv", "local"))
System.setProperty("mavenRepositoryFetchUrl", System.getProperty("mavenRepositoryBaseUrl", "https://repo-maven.kpsys.cz") + "/repository/maven-public/")
System.setProperty("mavenRepositoryPublishUrl", System.getProperty("mavenRepositoryBaseUrl", "https://repo-maven.kpsys.cz") + "/repository/maven-releases/")
System.setProperty("nodejsRepositoryUrl", System.getProperty("nodejsRepositoryBaseUrl", "https://repo-npm.kpsys.cz") + "/repository/nodejs-dist")
System.setProperty("gradleCacheUrl", System.getProperty("gradleCacheBaseUrl", "https://gradle-cache.kpsys.cz") + "/cache/")

println("============================================================= BUILDSCRIPT (settings.gradle.kts) ====")
println("buildEnv:                  ${System.getProperty("buildEnv")}")
println("gradleCacheUrl:            ${System.getProperty("gradleCacheUrl")}")
println("mavenRepositoryFetchUrl:   ${System.getProperty("mavenRepositoryFetchUrl")}")
println("mavenRepositoryPublishUrl: ${System.getProperty("mavenRepositoryPublishUrl")}")
println("nodejsRepositoryUrl:       ${System.getProperty("nodejsRepositoryUrl")}")
println("====================================================================================================")

buildCache {
    local {
//        isEnabled = System.getProperty("buildEnv") == "local" -- aktualne chceme zkusit cachovani v gitlab runnerech
        isEnabled = true
    }
    remote<HttpBuildCache> {
        url = uri(System.getProperty("gradleCacheUrl"))
        isAllowInsecureProtocol = true
        isPush = true
    }
}
