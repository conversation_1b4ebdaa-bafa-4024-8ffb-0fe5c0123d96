package cz.kpsys.portaro.record.grid;

import cz.kpsys.portaro.auth.UserAuthentication;
import cz.kpsys.portaro.commons.object.Provider;
import cz.kpsys.portaro.commons.object.repo.AllByIdsLoadable;
import cz.kpsys.portaro.commons.object.repo.ByIdLoadable;
import cz.kpsys.portaro.commons.object.repo.DataUtils;
import cz.kpsys.portaro.department.Department;
import cz.kpsys.portaro.grid.Row;
import cz.kpsys.portaro.hierarchy.HierarchyLoadScope;
import cz.kpsys.portaro.hierarchy.HierarchyLoader;
import cz.kpsys.portaro.record.Record;
import cz.kpsys.portaro.record.RecordConstants;
import cz.kpsys.portaro.record.RecordSettingKeys;
import cz.kpsys.portaro.record.RecordStatus;
import cz.kpsys.portaro.record.detail.FieldType;
import cz.kpsys.portaro.record.detail.FieldTypeId;
import cz.kpsys.portaro.record.edit.FieldTypesByFondLoader;
import cz.kpsys.portaro.record.edit.RecordEditationAvailableFondsResolver;
import cz.kpsys.portaro.record.edit.RecordEditationFactory;
import cz.kpsys.portaro.record.edit.RecordFieldEditor;
import cz.kpsys.portaro.record.edit.fieldshierarchy.FieldValueCommandResolver;
import cz.kpsys.portaro.record.edit.view.ValueEditorByFieldTypeLoader;
import cz.kpsys.portaro.record.fond.Fond;
import cz.kpsys.portaro.record.fond.FondInheritanceLoader;
import cz.kpsys.portaro.record.grid.edit.RecordGridRowEditor;
import cz.kpsys.portaro.record.search.ConvertingStandardSearch;
import cz.kpsys.portaro.record.search.SearchServiceBackedSearch;
import cz.kpsys.portaro.record.sec.CurrentAuthFondsLoader;
import cz.kpsys.portaro.search.*;
import cz.kpsys.portaro.search.factory.SearchFactory;
import cz.kpsys.portaro.search.factory.SearchFactoryMatching;
import cz.kpsys.portaro.search.factory.SearchFactoryResolver;
import cz.kpsys.portaro.search.factory.SearchFactoryResolverMatcher;
import cz.kpsys.portaro.search.sorting.Sorting;
import cz.kpsys.portaro.search.view.SearchViewConstants;
import cz.kpsys.portaro.setting.SettingLoader;
import cz.kpsys.portaro.view.ViewableItemsConverter;
import cz.kpsys.portaro.view.ViewableItemsTypedConverter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.convert.converter.Converter;
import org.springframework.util.Assert;

import java.time.ZoneId;
import java.util.List;
import java.util.function.Function;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Configuration
public class RecordTableConfig {

    @NonNull SettingLoader settingLoader;
    @NonNull HierarchyLoader<Department> departmentAccessor;
    @NonNull CurrentAuthFondsLoader currentAuthShowableFondsLoader;
    @NonNull SearchService<InternalSearchResult<String, MapBackedParams, RangePaging>, MapBackedParams> recordSearchService;
    @NonNull AllByIdsLoadable<RecordStatus, Integer> recordStatusesByIdsLoader;
    @NonNull Converter<List<String>, List<Record>> stringIdsToRecordsConverter;
    @NonNull Provider<Sorting> defaultRecordSearchSortingProvider;
    @NonNull SearchFactoryResolver searchFactoryResolver;
    @NonNull ViewableItemsConverter viewableItemsConverter;
    @NonNull FieldTypesByFondLoader fieldTypesByFondLoader;
    @NonNull RecordEditationFactory recordEditationFactory;
    @NonNull FieldValueCommandResolver fieldValueCommandResolver;
    @NonNull ValueEditorByFieldTypeLoader valueEditorByFieldTypeLoader;
    @NonNull Function<Fond, List<Fond>> enabledLoadableFondsExpander;
    @NonNull FondInheritanceLoader enabledFondInheritanceLoader;
    @NonNull Provider<@NonNull ZoneId> defaultTimeZoneProvider;
    @NonNull RecordEditationAvailableFondsResolver recordEditationAvailableFondsResolver;
    @NonNull ByIdLoadable<FieldType, FieldTypeId> fieldTypeLoader;
    @NonNull RecordFieldEditor recordFieldEditor;

    @Bean
    public RecordGridApiController recordGridApiController() {
        return new RecordGridApiController(
                fieldTypesByFondLoader,
                recordsToRecordRowsConverter(),
                new RecordGridRowEditor(
                        recordEditationFactory,
                        fieldValueCommandResolver,
                        fieldTypeLoader,
                        recordFieldEditor
                ),
                new EditableFieldTypesToGridResponsesConverter(
                        valueEditorByFieldTypeLoader,
                        enabledLoadableFondsExpander,
                        enabledFondInheritanceLoader,
                        defaultTimeZoneProvider,
                        fieldTypesByFondLoader
                ),
                recordEditationAvailableFondsResolver
        );
    }

    @Bean
    public SearchFactory tableSearchFactory() {
        return new SearchFactoryMatching(new SearchFactoryResolverMatcher.SearchFactoryResolverMatcherByType(SearchViewConstants.TYPE_TABLE)) {
            @Override
            public AbstractStandardSearch<MapBackedParams, Record, RangePaging> createSearch(@NonNull MapBackedParams customParams, @NonNull UserAuthentication currentAuth, @NonNull Department ctx) {
                Assert.state(!customParams.has(CoreSearchParams.SUBKIND), "%s search must not contain %s parameter".formatted(SearchViewConstants.TYPE_TABLE, CoreSearchParams.SUBKIND));
                DataUtils.requireSingle(customParams.get(RecordConstants.SearchParams.ROOT_FOND), Fond.class, RecordConstants.SearchParams.ROOT_FOND);

                MapBackedParams defaultDynamicParams = MapBackedParams.build(p -> {
                    p.set(CoreSearchParams.RIGHT_HAND_EXTENSION, false);
                    p.set(CoreSearchParams.FACETS_ENABLED, true);
                    p.set(CoreSearchParams.INCLUDE_DRAFT, false);
                    p.set(CoreSearchParams.INCLUDE_DELETED, false);
                    p.set(RecordConstants.SearchParams.INCLUDE_EXCLUDED, false);
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD_STATUS, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORD_STATUSES).andThen(recordStatusesByIdsLoader::getAllByIds).getOn(ctx));
                    p.set(RecordConstants.SearchParams.FORBIDDEN_RECORD, settingLoader.getDepartmentedProvider(RecordSettingKeys.FORBIDDEN_RECORDS).getOn(ctx));
                    p.set(CoreSearchParams.DEPARTMENT, departmentAccessor.getAllByScope(ctx, HierarchyLoadScope.FAMILY));
                });

                return ConvertingStandardSearch.of(new SearchServiceBackedSearch<>(recordSearchService), stringIdsToRecordsConverter)
                        .pageSizeChangeable(false)
                        .randomPageAccessible(false)
                        .sortable(true)
                        .facetable(true)
                        .withIntersectingConstraintParam(RecordConstants.SearchParams.ROOT_FOND, currentAuthShowableFondsLoader.getAllByAuth(currentAuth, ctx))
                        .withDefaultDynamicParams(defaultDynamicParams)
                        .withDefaultSorting(defaultRecordSearchSortingProvider.get());
            }
        };
    }

    @Bean
    public ViewableItemsTypedConverter<Record, Row> recordsToRecordRowsConverter() {
        return new RecordToRecordRowsConverter();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void registerModule() {
        searchFactoryResolver.withStandardOrder(tableSearchFactory());
        viewableItemsConverter.register(Record.class, ViewableItemsConverter.ViewMode.TABLE_ROW, recordsToRecordRowsConverter());
    }

}
