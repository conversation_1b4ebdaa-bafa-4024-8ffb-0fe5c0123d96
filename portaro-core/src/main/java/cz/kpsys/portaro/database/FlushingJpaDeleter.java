package cz.kpsys.portaro.database;

import cz.kpsys.portaro.commons.object.repo.Deleter;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.JpaRepository;

import java.io.Serializable;

@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Slf4j
public class FlushingJpaDeleter<E, ID extends Serializable> implements Deleter<E> {

    @NonNull JpaRepository<E, ID> jpa;

    @Override
    public void delete(E e) {
        log.info("Deleting & flushing entity {}", e);
        jpa.delete(e);
        jpa.flush();
        log.debug("Deleted & flushed entity {}", e);
    }
}