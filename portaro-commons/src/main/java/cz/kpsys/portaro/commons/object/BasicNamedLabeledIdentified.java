package cz.kpsys.portaro.commons.object;

import cz.kpsys.portaro.commons.util.ObjectUtil;
import cz.kpsys.portaro.commons.util.StringUtil;

import java.io.Serializable;

public class BasicNamedLabeledIdentified<ID extends Serializable> extends BasicIdentified<ID> implements NamedLabeledIdentified<ID> {

    private String name;

    public BasicNamedLabeledIdentified(ID id) {
        super(id);
    }

    public BasicNamedLabeledIdentified(ID id, String name) {
        super(id);
        this.name = StringUtil.notBlankTrimmedString(name);
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = StringUtil.notBlankTrimmedString(name);
    }

    @Override
    public String toString() {
        return ObjectUtil.firstNotNull(getId(), "<no-id>") +
                (name == null ? "" : " (" + StringUtil.limitCharsWithoutTrimWithoutEllipsis(name, 8, true) + ")");
    }
}
